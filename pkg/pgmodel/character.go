package pgmodel

import (
	"database/sql/driver"
	"fmt"
	"time"
)

// TimeLayout defines the format for parsing and formatting time values
const TimeLayout = "2006-01-02T15:04:05.999999"

// Time represents a custom time type with JSON marshalling and database serialization
type Time struct {
	time.Time
}

// UnmarshalJSON implements the json.Unmarshaler interface for custom time parsing
func (t *Time) UnmarshalJSON(b []byte) error {
	// 去掉引号
	s := string(b[1 : len(b)-1])
	// 解析时间
	nt, err := time.Parse(TimeLayout, s)
	if err != nil {
		return err
	}
	t.Time = nt
	return nil
}

// MarshalJSON implements the json.Marshaler interface for custom time formatting
func (t *Time) MarshalJSON() ([]byte, error) {
	return []byte(t.Time.Format(TimeLayout)), nil
}

// Value implements the driver.Valuer interface for database serialization
func (t Time) Value() (driver.Value, error) {
	return t.Time, nil
}

// <PERSON><PERSON> implements the sql.Scanner interface for database deserialization
func (t Time) Scan(v any) error {
	val, ok := v.(time.Time)
	if ok {
		t.Time = val
		return nil
	}
	return fmt.Errorf("can covert into pgmodel.Time: %v", v)
}

// Character represents a character entity in the database
type Character struct {
	ID         int  `gorm:"column:charId;primaryKey" json:"charId"`
	UpdateTime Time `gorm:"column:updateTime" json:"updateTime"`
}

// TableName returns the database table name for the Character entity
func (Character) TableName() string {
	return "aiu-character-table"
}

// UserCharacter represents a user-created character entity in the database
type UserCharacter struct {
	ID         int  `gorm:"column:charId" json:"charId"`
	UpdateTime Time `gorm:"column:updateTime" json:"updateTime"`
}

// TableName returns the database table name for the UserCharacter entity
func (UserCharacter) TableName() string {
	return "aiu_user_inserted_char_table"
}
