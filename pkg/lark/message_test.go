package lark

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestTextMessage_build(t *testing.T) {
	// Test cases
	testCases := []struct {
		name    string
		message TextMessage
		want    map[string]interface{}
	}{
		{
			name:    "simple text message",
			message: TextMessage("hello world"),
			want: map[string]interface{}{
				"msg_type": "text",
				"content": map[string]interface{}{
					"text": "hello world",
				},
			},
		},
		{
			name:    "empty text message",
			message: TextMessage(""),
			want: map[string]interface{}{
				"msg_type": "text",
				"content": map[string]interface{}{
					"text": "",
				},
			},
		},
		{
			name:    "text with special characters",
			message: TextMessage("hello\nworld\t!@#$%^&*()"),
			want: map[string]interface{}{
				"msg_type": "text",
				"content": map[string]interface{}{
					"text": "hello\nworld\t!@#$%^&*()",
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Build the message
			data, err := tc.message.build()
			require.NoError(t, err)

			// Unmarshal and verify
			var got map[string]interface{}
			err = json.Unmarshal(data, &got)
			require.NoError(t, err)

			// Check structure
			assert.Equal(t, tc.want["msg_type"], got["msg_type"])

			// Check content
			gotContent, ok := got["content"].(map[string]interface{})
			require.True(t, ok, "content should be a map")

			wantContent, ok := tc.want["content"].(map[string]interface{})
			require.True(t, ok, "tc.want[\"content\"] should be a map")

			assert.Equal(t, wantContent["text"], gotContent["text"])
		})
	}
}

func TestInteractiveMessage_build(t *testing.T) {
	// Test cases
	testCases := []struct {
		name    string
		message *InteractiveMessage
		check   func(t *testing.T, got map[string]interface{})
	}{
		{
			name: "basic interactive message",
			message: &InteractiveMessage{
				Header: &InteractiveHeader{
					Title: &TagContent{
						Tag:     "plain_text",
						Content: "Test Title",
					},
					Template: "blue",
				},
				Elements: []interface{}{
					map[string]interface{}{
						"tag": "div",
						"text": &TagContent{
							Tag:     "plain_text",
							Content: "Test Content",
						},
					},
				},
			},
			check: func(t *testing.T, got map[string]interface{}) {
				assert.Equal(t, "interactive", got["msg_type"])

				card, ok := got["card"].(map[string]interface{})
				require.True(t, ok, "card should be a map")

				// Check header
				header, ok := card["header"].(map[string]interface{})
				require.True(t, ok, "header should be a map")
				assert.Equal(t, "blue", header["template"])

				title, ok := header["title"].(map[string]interface{})
				require.True(t, ok, "title should be a map")
				assert.Equal(t, "plain_text", title["tag"])
				assert.Equal(t, "Test Title", title["content"])

				// Check elements
				elements, ok := card["elements"].([]interface{})
				require.True(t, ok, "elements should be an array")
				require.Len(t, elements, 1)

				element, ok := elements[0].(map[string]interface{})
				require.True(t, ok, "element should be a map")
				assert.Equal(t, "div", element["tag"])

				text, ok := element["text"].(map[string]interface{})
				require.True(t, ok, "text should be a map")
				assert.Equal(t, "plain_text", text["tag"])
				assert.Equal(t, "Test Content", text["content"])
			},
		},
		{
			name: "interactive message with config",
			message: &InteractiveMessage{
				Header: &InteractiveHeader{
					Title: &TagContent{
						Tag:     "plain_text",
						Content: "Test Title",
					},
					Template: "red",
				},
				Elements: []interface{}{
					map[string]interface{}{
						"tag": "div",
						"text": &TagContent{
							Tag:     "lark_md",
							Content: "**Bold Text**",
						},
					},
				},
				Config: &InteractiveConfig{
					EnableForward: true,
					UpdateMulti:   false,
				},
			},
			check: func(t *testing.T, got map[string]interface{}) {
				assert.Equal(t, "interactive", got["msg_type"])

				card, ok := got["card"].(map[string]interface{})
				require.True(t, ok, "card should be a map")

				// Check config
				config, ok := card["config"].(map[string]interface{})
				require.True(t, ok, "config should be a map")
				assert.Equal(t, true, config["enable_forward"])
				assert.Equal(t, false, config["update_multi"])
			},
		},
		{
			name: "interactive message with card link",
			message: &InteractiveMessage{
				Header: &InteractiveHeader{
					Title: &TagContent{
						Tag:     "plain_text",
						Content: "Test Title",
					},
					Template: "green",
				},
				Elements: []interface{}{
					map[string]interface{}{
						"tag": "div",
						"text": &TagContent{
							Tag:     "plain_text",
							Content: "Test Content",
						},
					},
				},
				CardLink: &CardLink{
					URL:        "https://example.com",
					AndroidURL: "https://example.com/android",
					IosURL:     "https://example.com/ios",
					PcURL:      "https://example.com/pc",
				},
			},
			check: func(t *testing.T, got map[string]interface{}) {
				assert.Equal(t, "interactive", got["msg_type"])

				card, ok := got["card"].(map[string]interface{})
				require.True(t, ok, "card should be a map")

				// Check card link
				cardLink, ok := card["card_link"].(map[string]interface{})
				require.True(t, ok, "card_link should be a map")
				assert.Equal(t, "https://example.com", cardLink["url"])
				assert.Equal(t, "https://example.com/android", cardLink["android_url"])
				assert.Equal(t, "https://example.com/ios", cardLink["ios_url"])
				assert.Equal(t, "https://example.com/pc", cardLink["pc_url"])
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Build the message
			data, err := tc.message.build()
			require.NoError(t, err)

			// Unmarshal and verify
			var got map[string]interface{}
			err = json.Unmarshal(data, &got)
			require.NoError(t, err)

			// Run custom checks
			tc.check(t, got)
		})
	}
}

// Note: Tests for PostMessage and ImageMessage would be added once the implementation
// of these types is completed. Currently their build() methods panic with "todo implement me".
