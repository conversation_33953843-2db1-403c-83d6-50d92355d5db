package lark

import "encoding/json"

// messageBuilder defines an interface for building different message types
// according to https://open.larksuite.com/document/client-docs/bot-v3/add-custom-bot
type messageBuilder interface {
	build() ([]byte, error)
}

// TagContent represents a tagged content in a Lark message
type TagContent struct {
	Tag     string `json:"tag"`
	Content string `json:"content"`
}

// TextMessage represents a simple text message in Lark
type TextMessage string

// build implements messageBuilder interface for TextMessage
func (t TextMessage) build() ([]byte, error) {
	req := map[string]interface{}{
		"msg_type": "text",
		"content": map[string]interface{}{
			"text": t,
		},
	}
	return json.Marshal(req)
}

// PostMessage represents a rich text message in Lark
type PostMessage struct {
}

func (t *PostMessage) build() ([]byte, error) {
	panic("todo implement me")
}

// ImageMessage represents an image message in Lark
type ImageMessage struct {
}

func (t *ImageMessage) build() ([]byte, error) {
	panic("todo implement me")
}

// InteractiveHeader represents the header part of an interactive message
type InteractiveHeader struct {
	Title    *TagContent `json:"title"`
	Template string      `json:"template"`
}

// InteractiveConfig defines configuration options for interactive messages
type InteractiveConfig struct {
	EnableForward bool `json:"enable_forward"`
	UpdateMulti   bool `json:"update_multi"`
}

// CardLink defines links for card messages on different platforms
type CardLink struct {
	URL        string `json:"url"`
	AndroidURL string `json:"android_url,omitempty"`
	IosURL     string `json:"ios_url,omitempty"`
	PcURL      string `json:"pc_url,omitempty"`
}

// InteractiveMessage represents a card message in Lark
// See: https://open.larksuite.com/document/common-capabilities/message-card/getting-started/card-structure/card-content
type InteractiveMessage struct {
	Header   *InteractiveHeader `json:"header,omitempty"`
	Elements []interface{}      `json:"elements"`
	Config   *InteractiveConfig `json:"config,omitempty"`
	CardLink *CardLink          `json:"card_link,omitempty"`
}

func (t *InteractiveMessage) build() ([]byte, error) {
	req := map[string]interface{}{
		"msg_type": "interactive",
		"card":     t,
	}
	return json.Marshal(req)
}
