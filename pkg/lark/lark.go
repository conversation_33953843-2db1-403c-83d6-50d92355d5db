// Package lark implements the ability to interact with lark
package lark

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

// BotClient provides functionality to interact with Lark/Feishu bots
type BotClient struct {
	cfg BotConfig
	cli *http.Client
}

// BotConfig holds configuration for a Lark bot
type BotConfig struct {
	Webhook string `yaml:"webhook"`
}

// NewBotClient creates a new Lark bot client with the provided configuration
func NewBotClient(cfg BotConfig) (*BotClient, error) {
	return &BotClient{
		cfg: cfg,
		cli: http.DefaultClient,
	}, nil
}

// SendBotMessage sends a message to Lark using the bot
// lark custom bot doc: https://open.larksuite.com/document/client-docs/bot-v3/add-custom-bot
func (l *BotClient) SendBotMessage(ctx context.Context, message messageBuilder) error {
	data, err := message.build()
	if err != nil {
		return err
	}

	rsp, err := l.cli.Post(l.cfg.Webhook, "application/json", bytes.NewBuffer(data))
	if err != nil {
		return err
	}
	defer rsp.Body.Close()

	data, err = io.ReadAll(rsp.Body)
	if err != nil {
		return err
	}

	type rspBody struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data any    `json:"data"`
	}
	var b *rspBody
	err = json.Unmarshal(data, &b)
	if err != nil {
		return err
	}
	if b.Code != 0 {
		return fmt.Errorf("send bot message failed, rsp: %+v", b)
	}
	return nil
}
