package lark

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestBotClient_SendBotMessage tests the basic functionality of the SendBotMessage method
func TestBotClient_SendBotMessage(t *testing.T) {
	testCases := []struct {
		name           string
		message        messageBuilder
		serverResponse interface{}
		serverStatus   int
		expectError    bool
	}{
		{
			name:    "success with text message",
			message: TextMessage("test text message"),
			serverResponse: map[string]interface{}{
				"code": 0,
				"msg":  "success",
				"data": map[string]string{},
			},
			serverStatus: http.StatusOK,
			expectError:  false,
		},
		{
			name: "success with interactive message",
			message: &InteractiveMessage{
				Header: &InteractiveHeader{
					Title: &TagContent{
						Tag:     "plain_text",
						Content: "test card",
					},
					Template: "red",
				},
				Elements: []interface{}{
					map[string]interface{}{
						"tag": "div",
						"text": &TagContent{
							Tag:     "lark_md",
							Content: "**Bold text**",
						},
					},
				},
			},
			serverResponse: map[string]interface{}{
				"code": 0,
				"msg":  "success",
				"data": map[string]string{},
			},
			serverStatus: http.StatusOK,
			expectError:  false,
		},
		{
			name:    "error from server",
			message: TextMessage("test text message"),
			serverResponse: map[string]interface{}{
				"code": 9999,
				"msg":  "some error occurred",
				"data": map[string]string{},
			},
			serverStatus: http.StatusOK,
			expectError:  true,
		},
		{
			name:         "server returns non-200 status",
			message:      TextMessage("test text message"),
			serverStatus: http.StatusInternalServerError,
			expectError:  true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify request method and content type
				assert.Equal(t, "POST", r.Method)
				assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

				// Read request body
				body, err := io.ReadAll(r.Body)
				require.NoError(t, err)
				defer r.Body.Close()

				// Verify request is valid JSON
				var requestData map[string]interface{}
				err = json.Unmarshal(body, &requestData)
				require.NoError(t, err)

				// Set response status
				w.WriteHeader(tc.serverStatus)

				// Return response if not a server error case
				if tc.serverStatus == http.StatusOK {
					responseBody, err := json.Marshal(tc.serverResponse)
					require.NoError(t, err)
					_, err = w.Write(responseBody)
					require.NoError(t, err)
				}
			}))
			defer server.Close()

			// Create bot client with test server URL
			client, err := NewBotClient(BotConfig{Webhook: server.URL})
			require.NoError(t, err)

			// Call the method being tested
			err = client.SendBotMessage(context.Background(), tc.message)

			// Check if error matches expectation
			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestBotClient_WithCustomHTTPClient tests using a custom HTTP client with timeouts
func TestBotClient_WithCustomHTTPClient(t *testing.T) {
	// Create a server that simulates a delay
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, _ *http.Request) {
		// Sleep to simulate a slow server
		time.Sleep(100 * time.Millisecond)

		// Return success response
		response := map[string]interface{}{
			"code": 0,
			"msg":  "success",
			"data": map[string]string{},
		}
		responseBody, _ := json.Marshal(response)
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write(responseBody)
	}))
	defer server.Close()

	// Test with custom client with very short timeout
	t.Run("custom client with timeout", func(t *testing.T) {
		// Create custom client with short timeout
		customClient := &http.Client{
			Timeout: 50 * time.Millisecond, // Shorter than server delay
		}

		// Create bot client
		client, err := NewBotClient(BotConfig{Webhook: server.URL})
		require.NoError(t, err)

		// Replace default client with custom client
		client.cli = customClient

		// Call should timeout
		err = client.SendBotMessage(context.Background(), TextMessage("test timeout"))
		assert.Error(t, err)
		// Check for any timeout-related terms in the error
		assert.True(t, strings.Contains(err.Error(), "timeout") ||
			strings.Contains(err.Error(), "deadline exceeded"),
			"Error should include timeout or deadline exceeded: %v", err)
	})

	// Test with custom client with sufficient timeout
	t.Run("custom client with sufficient timeout", func(t *testing.T) {
		// Create custom client with longer timeout
		customClient := &http.Client{
			Timeout: 500 * time.Millisecond, // Longer than server delay
		}

		// Create bot client
		client, err := NewBotClient(BotConfig{Webhook: server.URL})
		require.NoError(t, err)

		// Replace default client with custom client
		client.cli = customClient

		// Call should succeed
		err = client.SendBotMessage(context.Background(), TextMessage("test success"))
		assert.NoError(t, err)
	})
}

// TestBotClient_InvalidJSON tests how the client handles invalid JSON responses
func TestBotClient_InvalidJSON(t *testing.T) {
	// Create server that returns invalid JSON
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, _ *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte("this is not valid json"))
	}))
	defer server.Close()

	client, err := NewBotClient(BotConfig{Webhook: server.URL})
	require.NoError(t, err)

	// Call should fail with a JSON parsing error
	err = client.SendBotMessage(context.Background(), TextMessage("test invalid json"))
	assert.Error(t, err)
}

// Examples are kept for documentation
func ExampleBotClient_SendBotMessage() {
	bot, _ := NewBotClient(BotConfig{Webhook: "https://open.larksuite.com/open-apis/bot/v2/hook/****"})

	// send text message
	msg := TextMessage("test text message")
	_ = bot.SendBotMessage(context.Background(), msg)

	// send interactive message
	cardMsg := &InteractiveMessage{
		Header: &InteractiveHeader{
			Title: &TagContent{
				Tag:     "plain_text",
				Content: "测试消息卡片",
			},
			Template: "red",
		},
		Elements: []interface{}{
			map[string]interface{}{
				"tag": "div",
				"text": &TagContent{
					Tag: "lark_md",
					Content: "**西湖**，位于浙江省杭州市西湖区龙井路1号，杭州市区西部，" +
						"景区总面积49平方千米，汇水面积为21.22平方千米，湖面面积为6.38平方千米。",
				}},
		},
	}
	_ = bot.SendBotMessage(context.Background(), cardMsg)
}
