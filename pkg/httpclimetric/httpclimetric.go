// Package httpclimetric provides a metric for http client.
package httpclimetric

import (
	"context"
	"errors"
	"net"
	"net/http"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/metric"
)

const clientNamespace = "http_client"

var (
	MetricClientReqDur = metric.NewHistogramVec(&metric.HistogramVecOpts{
		Namespace: clientNamespace,
		Subsystem: "requests",
		Name:      "duration_ms",
		Help:      "http client requests duration(ms).",
		Labels:    []string{"host", "method"},
		Buckets:   []float64{1, 2, 5, 10, 25, 50, 100, 250, 500, 1000, 2000, 5000, 10000, 15000, 20000, 30000},
	})

	MetricClientReqCodeTotal = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace: clientNamespace,
		Subsystem: "requests",
		Name:      "code_total",
		Help:      "http client requests code count.",
		Labels:    []string{"host", "method", "code"},
	})
)

// Transport implements the http.RoundTripper interface and wraps
// outbound HTTP(S) requests with metrics.
type Transport struct {
	rt http.RoundTripper
}

var _ http.RoundTripper = &Transport{}

// NewTransport wraps the provided http.RoundTripper with one that
// enriches it with metrics.
//
// If the provided http.RoundTripper is nil, http.DefaultTransport will be used
// as the base http.RoundTripper.
func NewTransport(base http.RoundTripper) *Transport {
	if base == nil {
		base = http.DefaultTransport
	}

	t := Transport{
		rt: base,
	}

	return &t
}

// RoundTrip creates a Span and propagates its context via the provided request's headers
// before handing the request to the configured base RoundTripper. The created span will
// end when the response body is closed or when a read from the body returns io.EOF.
func (t *Transport) RoundTrip(r *http.Request) (*http.Response, error) {
	var code int
	requestStartTime := time.Now()

	res, err := t.rt.RoundTrip(r)
	if err != nil {
		code = getErrCode(err)
	} else {
		code = res.StatusCode
	}

	// Use floating point division here for higher precision (instead of Millisecond method).
	elapsedTime := float64(time.Since(requestStartTime)) / float64(time.Millisecond)

	var host string
	if r.URL != nil {
		host = r.URL.Host
	} else {
		host = r.Header.Get("Host")
	}

	MetricClientReqDur.ObserveFloat(elapsedTime, host, r.Method)
	MetricClientReqCodeTotal.Inc(host, r.Method, strconv.Itoa(code))

	return res, err
}

func getErrCode(err error) int {
	// 检查上下文取消或截止时间超时
	if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
		return -1
	}

	// 检查网络超时错误
	var netErr net.Error
	if errors.As(err, &netErr) && netErr.Timeout() {
		return -2
	}

	// 未分类错误
	return -3
}
