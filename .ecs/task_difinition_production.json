{"taskDefinitionArn": "arn:aws:ecs:us-east-1:010526279899:task-definition/sekai-go-production:2", "containerDefinitions": [{"name": "sekai-go-production", "image": "010526279899.dkr.ecr.us-east-1.amazonaws.com/sekai-go:v0.1.0", "cpu": 0, "memory": 2048, "portMappings": [{"name": "server", "containerPort": 7000, "hostPort": 7000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "AIU_ENV", "value": "production"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/sekai-go-production", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}, {"name": "otel-collector", "image": "010526279899.dkr.ecr.us-east-1.amazonaws.com/otel/opentelemetry-collector:sekaigo", "cpu": 0, "portMappings": [], "essential": false, "environment": [{"name": "SERVICE_NAME", "value": "sekai-go"}, {"name": "AIU_ENV", "value": "production"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/sekai-go-production/otel-collector", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "family": "sekai-go-production", "taskRoleArn": "arn:aws:iam::010526279899:role/ecsTask1", "executionRoleArn": "arn:aws:iam::010526279899:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 2, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.28"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2025-03-12T12:25:13.035Z", "registeredBy": "arn:aws:iam::010526279899:user/liwei", "tags": []}