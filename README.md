# sekai-go

## Step 1: Install Go

### Go 1.23

安装 go1.23 版本，安装方式自行 Google

注意最好设置 `$GOPATH`，并将 `$GOPATH/bin` 加入到 `PATH` 环境环境变量中，方便后续通过 `go install` 安装的二进制文件可以直接在命令行终端中执行

常用 go 命令：
```shell
# 更新并格式化锁定依赖，修改 go.mod go.sum 文件
go mod tidy
```

### Golangci-lint 1.62.x
```shell
# 推荐 v1.62.*，对应 Go 1.23，安装更高版本可能跟 Go 1.23 不兼容
go install github.com/golangci/golangci-lint/cmd/golangci-lint@v1.62.2

# 验证安装
golangci-lint --version
```

若 `go install` 安装后无法找到 `golangci-lint` 命令，请尝试通过 `go help install` 了解 `go install` 的安装路径，或者问一下 AI，或者 Google 一下，或者咨询项目维护人。

### GoMock
```shell
# 安装 gomock 方便单元测试
go install go.uber.org/mock/mockgen@latest

# 验证安装
mockgen -version
```

## Step 2: Install Pre-Commit

```shell
# 安装 pre-commit，已安装可以忽略
pip install pre-commit

# 设置 git hook，提交阶段和push阶段
pre-commit install --hook-type pre-commit --hook-type pre-push
```

## Step 3: Install Go-Zero

```shell
# 安装 goctl v1.8.1
go install github.com/zeromicro/go-zero/tools/goctl@v1.8.1

goctl --version

# 通过 goctl 可以一键安装 protoc，protoc-gen-go，protoc-gen-go-grpc 相关组件
goctl env check --install --verbose --force

# 安装 swagger 插件，用于生成 swagger json
go install github.com/zeromicro/goctl-swagger@latest
```

go-zero 开发指南：https://go-zero.dev/docs/tutorials

## Step 4: Install sqlc

```shell
# 推荐 v1.28.0 也可安装更新的与之兼容的版本
go install github.com/sqlc-dev/sqlc/cmd/sqlc@v1.28.0

sqlc version
```

sqlc 使用参考：https://docs.sqlc.dev/en/stable/index.html

## Step 5: Develop

```shell
# 在 api/ 目录下定义好 API 后生成代码
make api

# 生成 swagger 文档，位于 api/swagger 目录，可导入到 apifox
make swagger

# 在 internal/db/pg/schema internal/db/pg/query 中写好 sql 文件后生成代码
make sql

# 执行 lint 检查
make check

# 执行单元测试，输出项目覆盖率
make unit-test
```

## Directory Structure

```
.
├── api/                    # API 定义目录，包含 .api 文件
├── deploy/                 # 部署相关配置和脚本
├── etc/                    # 配置文件目录
├── internal/              # 内部代码
│   ├── config/           # 配置结构定义
│   ├── db/               # 数据库相关代码
│   │   └── pg/          # PostgreSQL 相关
│   │       ├── query/   # SQL 查询
│   │       └── schema/  # 数据库表结构
│   ├── handler/         # HTTP 处理器
│   ├── logic/           # 业务逻辑
│   ├── middleware/      # 中间件
│   ├── svc/             # 服务上下文
│   ├── types/           # 类型定义
│   ├── crates/           # 业务上有用的包
│   └── values/          # 常量值
├── tools/                # 一些工具代码，通常都是一些可独立编译为可执行文件的包
│   └── lambda/               # AWS Lambda 函数
├── pkg/                  # 可重用的公共包
├── scripts/             # 脚本文件
├── .ecs/                # ECS 部署配置
├── .github/             # GitHub 配置
│   └── workflows/       # GitHub Actions 工作流
├── .vscode/             # VS Code 配置
├── go.mod              # Go 模块定义
├── go.sum              # Go 依赖版本锁定
├── makefile
├── sqlc.yaml           # SQLC 配置
└── .pre-commit-config.yaml  # Pre-commit 配置
```

### 主要目录说明

- `api/`: 使用 go-zero 的 API 定义文件，定义了服务的 HTTP 接口
- `internal/`: 包含所有内部代码，遵循 Go 项目最佳实践
  - `handler/`: HTTP 请求处理器，负责参数验证和调用业务逻辑，一般由 go-zero 自动生成，特殊需求可进行修改
  - `logic/`: 业务逻辑实现
  - `db/`: 数据库访问层，包含 SQL 查询和表结构定义，SQL 相关 Go 代码采用 sqlc 自动生成
  - `config/`: 配置结构定义
  - `middleware/`: HTTP 中间件
  - `svc/`: 服务上下文，包含共享资源
  - `types/`: 类型定义，由 go-zero 自动生产的 API 请求参数和返回参数
  - `crates/`: 业务上有用的包
- `deploy/`: 部署相关配置和脚本
- `etc/`: 配置文件目录
- `lambda/`: AWS Lambda 函数实现
- `pkg/`: 可重用的公共包
- `scripts/`: 各种脚本文件
- `.ecs/`: AWS ECS 部署配置
- `.github/`: GitHub 相关配置，包含 CI/CD 工作流
