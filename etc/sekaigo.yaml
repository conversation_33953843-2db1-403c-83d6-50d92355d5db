Server:
  Name: sekai_go
  Host: 0.0.0.0
  Port: 7000
  Timeout: 30000  # 30秒超时

  Log:
    Encoding: json
    Level: info
    Stat: false

  Telemetry:
    Name: sekai_go
    Endpoint: http://localhost:4317
    Sampler: 1.0
    Batcher: otlphttp
    OtlpHttpPath: /v1/traces
    OtlpHttpSecure: false
    Disabled: true

  DevServer:
    Enabled: true
    Host: 127.0.0.1
    Port: 6060
    MetricsPath: /metrics
    HealthPath: /healthz
    EnableMetrics: true
    EnablePprof: true
    HealthResponse: OK

Auth:
  AccessSecret: NYV1MLeEMy00beVLg9
  AccessExpire: 360h # 360小时, 15天
  RefreshSecret: ILcbFZMQPsyGK9x7Vx
  RefreshExpire: 360h # 360小时, 15天

DB:
  Host: ai-universe-prod.c1m6sy4eyzif.us-east-1.rds.amazonaws.com
  Port: 5432
  User: postgres
  Password: 4!_j??ZvUWyg
  DBName: postgres
  SSLMode: 
  MaxConns: 20      # 最大连接数
  MinConns: 5       # 最小连接数
  MaxIdleTime: 30m  # 最大空闲时间
  MaxLifetime: 1h   # 连接最大生命周期
  HealthCheck: true # 是否启用健康检查

Dynamo:
  Region: us-east-1

S3:
  Region: us-east-1
  BucketName: sekaiapp-prod-data
  CloudFrontDomain: prod-data.sekai.chat
  Expires: 24h

Redis:
  Host: aiu-reids-prod.blwg4y.ng.0001.use1.cache.amazonaws.com:6379
  Type: "node"

OpenSearch:
  Addr: https://vpc-opensearch-prod-hf66stjtdl6axt5zcpm6g5c77m.us-east-1.es.amazonaws.com
  User: Adminstrator
  Password: E9+c3iyFkPUGcKT+zkgm3EBGN/zIWj0ZFgPOrQTUeLPYHssb1ad6lIsid1tuJSaa
