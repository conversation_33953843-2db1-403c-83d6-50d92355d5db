Server:
  Name: sekai_go
  Host: 0.0.0.0
  Port: 7700
  Timeout: 30000
  Mode: dev

  Log:
    Encoding: plain
    Level: debug
    Stat: false

  Telemetry:
    Name: sekai_go
    Endpoint: http://localhost:4317
    Sampler: 1.0
    Batcher: otlphttp
    OtlpHttpPath: /v1/traces
    OtlpHttpSecure: false
    Disabled: true

  DevServer:
    Enabled: true
    Host: 127.0.0.1
    Port: 6060
    MetricsPath: /metrics
    HealthPath: /healthz
    EnableMetrics: true
    EnablePprof: true
    HealthResponse: OK

Auth:
  AccessSecret: riX6WgTlbmxNqi0CiX
  AccessExpire: 360h # 360小时, 15天
  RefreshSecret: wQ2MGl1eOh3C9kLs71
  RefreshExpire: 360h # 360小时, 15天

DB:
  Host: ai-universe-stage.c1m6sy4eyzif.us-east-1.rds.amazonaws.com
  Port: 5432
  User: postgres
  Password: FEm!iWb>bTP%:CBFai-universe
  DBName: postgres
  SSLMode: 
  MaxConns: 10      # 最大连接数
  MinConns: 2       # 最小连接数
  MaxIdleTime: 30m  # 最大空闲时间
  MaxLifetime: 1h   # 连接最大生命周期
  HealthCheck: true # 是否启用健康检查

Dynamo:
  Region: us-east-1

S3:
  Region: us-east-1
  BucketName: sekaiapp-stage-data
  CloudFrontDomain: stage-data.sekai.chat
  Expires: 24h

Redis:
  Host: localhost:6379
  Type: "node"

OpenSearch:
  Addr: https://127.0.0.1:2222
  User: Adminstrator
  Password: E9+c3iyFkPUGcKT+zkgm3EBGN/zIWj0ZFgPOrQTUeLPYHssb1ad6lIsid1tuJSaa
