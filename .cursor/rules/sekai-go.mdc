---
description: 
globs: 
alwaysApply: true
---
# Sekai Go Project

## Overview
Sekai Go Backend - A Go implementation of backend services using go-zero and sqlc

## Code Structure
- `/api` - API definitions in go-zero format
- `/internal` - Internal application code
  - `/db` - Database layer, include dynamodb, opensearch, postgresql and redis
    - `/dynamo` - DynamoDB interface for bussiness
    - `/pg` - Postgresql CURD code with sqlc-generated code
  - `/handler` - HTTP request handlers with goctl-generated code
  - `/logic` - Business logic implementation with goctl-generated code
  - `/svc` - Service context and dependencies
- `/etc` - Configuration files

## Coding Guidelines

### SQL Query Style
- Use `-- name: QueryName :one|:many|:exec` format for sqlc queries
- Include descriptive comments for complex queries
- Use named parameters like sqlc.arg('paramName')
- Use consistent casing for SQL keywords (uppercase)
- When dealing with arrays in PostgreSQL use `= ANY()` pattern

### Go-Zero API Style
- Group related request/response types together
- Use clear and consistent type naming (XxxReq, XxxResp)
- Add proper documentation using @doc annotations
- Use appropriate tags for validation (optional, default, range, options)
- Group API endpoints by functional areas

### Handler Implementation
- Files named `xxxhandler.go` are generated by go-zero using `make api`
- Keep handlers thin - only parse request, call logic, and format response
- Use proper error handling with httpx.ErrorCtx
- Don't implement business logic in handlers
- Use consistent response formats

### Logic Implementation
- Files named `xxxlogic.go` are generated by go-zero using `make api`
- Structure logic methods with clear responsibility separation
- Log important operations and errors
- Use the converter pattern for data transformation
- Include proper error handling with descriptive messages
- Leverage context for cancellation and timeouts

### Error Handling
- Use error codes from values package
- Wrap database errors appropriately
- Log detailed error information
- Return user-friendly error messages in responses

## Common Patterns

### Database Operations
- Use sqlc-generated methods for database access
- Add new queries to the appropriate SQL files
- Use context propagation for all operations
- Handle transaction boundaries carefully

### API Response Structure
- Success responses should include:
  - Code (0 for success)
  - Message
  - Data payload
- Error responses should include:
  - Error code
  - Human-readable message
  - Optional details

### Pagination
- Use consistent pagination parameters (page, size)
- Include total count in paginated responses
- Validate pagination parameters

## Development Workflow
- Generate code after API changes: `make api`
- Generate SQL code after query changes: `make sql`
- Run linting before commit: `make check`
- Run tests: `make unit-test`
- Build project: `make build`

## Key Files and Modules
- API: `/api/main.api` includes all submodule api, eg: bgm.api,common.api
- SQLC Config: `/sqlc.yaml`
- BGM Module: Files in `/internal/db/pg/schema/bgm.sql`, `/internal/db/pg/query/bgm.sql`, `/api/bgm.api`
- Core Configuration: Files in `/internal/config`, `/etc`
- Middleware Components: Files in `/internal/middleware` 

## Unit Testing

### Testing Approach
- Write tests for both logic and database layers
- Use table-driven tests with clear test cases
- Aim for high test coverage of business logic
- Test both success and error paths
- Use subtests for related test scenarios with `t.Run()`

### Mocking with gomock
- Generate mocks for interfaces using gomock, Keep the mock interface generated by gomock in the directory of the original interface and name it as follows: xxx/xxxmock: 
  ```
  mockgen -source=path/to/interface.go -destination=path/to/tomock/mock_interface.go -package=tomock
  ```
- Create mock controller in test setup:
  ```go
  ctrl := gomock.NewController(t)
  defer ctrl.Finish()
  ```
- Initialize mocks and set expectations:
  ```go
  mockRepo := tomock.NewMockRepository(ctrl)
  mockRepo.EXPECT().
    GetUser(gomock.Any(), gomock.Eq(userID)).
    Return(&user, nil).
    Times(1)
  ```
- Use mocks in the service under test:
  ```go
  svc := &Service{repo: mockRepo}
  result, err := svc.DoSomething(ctx, input)
  ```

### Assert with testify
Exmaple
```go
import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_hello(t *testing.T) {
	l := &lambdaHandler{}
	_, err := l.hello()
	assert.NoError(t, err)
}
```

### Test Structure
- Usually, in database layer, do not need to generate unit tests or only test methods or functions that have no external dependencies. For example, in `/internal/db/dynamo` package, if a function or method depends on the AWS DynamoDB client, you don't need to test this method or function.
- Organize tests to match package structure
- Test file naming: `xxx_test.go` where xxx is the name of the file being tested
- Group related test functions using a common prefix (e.g., `TestUserService_`)
- Use clear test function names that describe what's being tested

### Test Helpers
- Create helper functions for common test setup
- Use test fixtures for consistent test data
- Implement cleanup functions with `t.Cleanup()`
- Use test utilities for common assertions

### Test Execution
- Run unit tests: `go test ./...`
- Run specific tests: `go test ./path/to/package -run TestName`
- Measure coverage: `go test ./... -coverprofile=coverage.out`
- View coverage report: `go tool cover -html=coverage.out`
- Run tests in CI pipeline using `make test`

### Example Test
```go
func TestUserLogic_GetUser(t *testing.T) {
    // Set up mock controller
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    // Create mocks
    mockUserRepo := mocks.NewMockUserRepo(ctrl)
    
    // Create test cases
    tests := []struct {
        name       string
        userID     string
        mockSetup  func()
        wantUser   *types.User
        wantErr    bool
        wantErrMsg string
    }{
        {
            name:   "success case",
            userID: "user123",
            mockSetup: func() {
                user := &model.User{ID: "user123", Name: "Test User"}
                mockUserRepo.EXPECT().
                    GetUser(gomock.Any(), "user123").
                    Return(user, nil).
                    Times(1)
            },
            wantUser: &types.User{ID: "user123", Name: "Test User"},
            wantErr:  false,
        },
        {
            name:   "not found error",
            userID: "user999",
            mockSetup: func() {
                mockUserRepo.EXPECT().
                    GetUser(gomock.Any(), "user999").
                    Return(nil, errors.New("user not found")).
                    Times(1)
            },
            wantUser:   nil,
            wantErr:    true,
            wantErrMsg: "user not found",
        },
    }
    
    // Run test cases
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Set up mock expectations
            tt.mockSetup()
            
            // Create logic with dependencies
            l := NewUserLogic(context.Background(), &svc.ServiceContext{
                UserRepo: mockUserRepo,
            })
            
            // Execute function under test
            got, err := l.GetUser(&types.GetUserReq{ID: tt.userID})
            
            // Assert results
            if tt.wantErr {
                assert.Error(t, err)
                assert.Contains(t, err.Error(), tt.wantErrMsg)
            } else {
                assert.NoError(t, err)
                assert.Equal(t, tt.wantUser, got)
            }
        })
    }
} 

