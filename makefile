app := sekai-go

ignore_unit_test_dir := tools|internal/handler|internal/db/pg

export CGO_CXXFLAGS_ALLOW:=.*
export CGO_LDFLAGS_ALLOW:=.*
export CGO_CFLAGS_ALLOW:=.*

.PHONY: all test clean api

all: build

build:
	@echo "make sekai go ..."
	go build -ldflags='-w -s' $(FLAGS) -o ./${app} .

check:
	@echo "start check ..."
	golangci-lint run

mock:
	@echo "Generating mocks from go:generate directives..."
	@find . -name "*.go" -exec grep -l "//go:generate mockgen" {} \; | xargs -I{} sh -c 'echo "Processing: {}"; GO111MODULE=on go generate {}'
	@echo "All mocks generated successfully."

unit-test:
	@echo "start unit test ..."
	go test `go list ./... | grep -v -E "(${ignore_unit_test_dir})"` -v -run='^Test' -covermode=count -coverprofile=coverage.out -gcflags=all=-l ./...
	@go tool cover -func=coverage.out -o=coverage.txt
	@go tool cover -html=coverage.out -o=coverage.html
	@cat coverage.txt | grep total | awk '{print "\n\nTotal coverage: " $$3}'

clean:
	@echo "clean files ..."
	rm -fv ${app} coverage.out coverage.txt coverage.html

api:
	@echo "start generating api ..."
	goctl api format --declare --dir ./api
	goctl api go --api ./api/main.api -dir . -style gozero

swagger:
	@echo "start generating swagger ..."
	goctl api plugin -plugin goctl-swagger="swagger -filename ./api/swagger/main.json" -api ./api/main.api -dir .

sql:
	@echo "start generating sql query ..."
	sqlc generate
