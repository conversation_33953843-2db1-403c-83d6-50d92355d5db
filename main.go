// Sekai Go is a backend service implementation using go-zero and sqlc.
// It provides API endpoints for managing background music (BGM), characters,
// and user interactions within the Sekai application ecosystem.
package main

import (
	"flag"
	"fmt"
	"net/http"

	"github.com/sekai-app/sekai-go/internal/config"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/handler"
	"github.com/sekai-app/sekai-go/internal/middleware"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/pkg/httpclimetric"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"
)

var configFile = flag.String("f", "etc/sekaigo_local.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	server := rest.MustNewServer(c.Server)
	defer server.Stop()

	// 添加 ServerMsgMiddleware 中间件
	server.Use(middleware.NewServerMsgMiddleware().Handle)
	// 添加 DebugLogMiddleware 中间件
	server.Use(middleware.NewDebugLogMiddleware().Handle)

	ctx := svc.NewServiceContext(c)
	handler.RegisterHandlers(server, ctx)

	fmt.Printf("Starting server at %s:%d...\n", c.Server.Host, c.Server.Port)

	// 设置统一接口错误处理
	httpx.SetErrorHandlerCtx(utils.ErrorHandler)

	// 设置默认的 http Transport 为 httpclimetric.NewTransport 进行主调指标监控
	http.DefaultTransport = httpclimetric.NewTransport(http.DefaultTransport)

	server.Start()
}
