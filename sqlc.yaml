version: "2"
sql:
  - engine: "postgresql"
    queries: "internal/db/pg/query"
    schema: "internal/db/pg/schema"
    gen:
      go:
        package: "pg"
        out: "internal/db/pg"
        sql_package: "pgx/v5"
        emit_interface: true # 生成 interface 接口
        emit_json_tags: true # 生成 json tag
        emit_result_struct_pointers: true # 使用指针传递返回结果
        emit_params_struct_pointers: true # 使用指针传递参数
