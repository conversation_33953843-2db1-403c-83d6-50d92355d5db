service:
  golangci-lint-version: 1.62.x

run:
  timeout: 20m

linters-settings:
  funlen:
    lines: 80
    statements: 80
  goconst:
    min-len: 2
    min-occurrences: 2
  gocyclo:
    min-complexity: 20
  govet:
    shadow: true
  lll:
    line-length: 120
  errcheck:
    check-type-assertions: true
  gocritic:
    enabled-checks:
      - nestingReduce
    settings:
      nestingReduce:
        bodyWidth: 5

linters:
  disable-all: true
  enable:
    - funlen
    - goconst
    - gocyclo
    - gofmt
    - ineffassign
    - staticcheck
    - typecheck
    - goimports
    - gosimple
    - govet
    - lll
    - rowserrcheck
    - errcheck
    - unused
    - sqlclosecheck
    - revive

issues:
  new-from-rev: HEAD~1  # 对比上一次提交
  whole-files: false    # 仅检查变更行
  exclude-rules:
    # 忽略测试文件
    - path: _test\.go
      linters:
        - lll
        - funlen
        - gocyclo
        - goconst
        - errcheck

output:
  sort-results: true
