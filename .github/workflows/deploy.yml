name: Deploy to ECS

on:
  push:
    branches:
      - main
      - develop
      - prod
env:
      ECR_REPOSITORY: ${{ github.event.repository.name }}
      IMAGE_TAG: ${{ github.sha }}                        
      AWS_REGION: us-east-1
      ECS_TASK_EXECUTION_ROLE_ARN: arn:aws:iam::123456789012:role/ecsTask1

jobs:
    build-and-deploy:
      runs-on: ubuntu-latest
    
      steps:
      - name: Checkout
        uses: actions/checkout@v4
       
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      # - name: Clean up old cache
      #   run: rm -rf /tmp/.buildx-cache
      #   if: ${{ steps.cache.outputs.cache-hit != 'true' }}
    
      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-
     
        # 配置 AWS CLI
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
    
      # 登录到 Amazon ECR
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        # 根据分支名称设置环境变量


      - name: Set up environment variables
        run: |
          if [ "${{ github.ref_name }}" == "main" ]; then
            echo "ENV_SUFFIX=stage" >> $GITHUB_ENV
          elif [ "${{ github.ref_name }}" == "develop" ]; then
            echo "ENV_SUFFIX=develop" >> $GITHUB_ENV
          elif [ "${{ github.ref_name }}" == "prod" ]; then
            echo "ENV_SUFFIX=production" >> $GITHUB_ENV
          fi
          echo "CLUSTER=${{ github.ref_name }}" >> $GITHUB_ENV
          echo "SERVICE_SUFFIX=${{ env.ENV_SUFFIX }}" >> $GITHUB_ENV
          echo "TASK_DEFINITION_FAMILY=${{ github.event.repository.name }}-${{ env.ENV_SUFFIX }}" >> $GITHUB_ENV

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        with:
          context: .
          file: deploy/dockerfile # 指定 Dockerfile 的路径
          push: true
          tags: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}-${{ env.ENV_SUFFIX }}:${{ env.IMAGE_TAG }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache
          provenance: false
          build-args: |
            APP_RELATIVE_PATH=./sekai_go.go

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1.3.0
        with:
          task-definition: .ecs/task_difinition_${{ env.ENV_SUFFIX }}.json
          container-name: ${{ github.event.repository.name }}-${{ env.ENV_SUFFIX }}
          image: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}-${{ env.ENV_SUFFIX }}:${{ env.IMAGE_TAG }}        
        
      - name: Deploy Amazon ECS task definition
        id: deploy
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1.5.0
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECR_REPOSITORY }}-${{ env.ENV_SUFFIX }}
          cluster: sekai-app-backend-api
          wait-for-service-stability: true

      - name: Expose git commit data
        uses: rlespinasse/git-commit-data-action@v1

      - name: Notify Success to IM
        if: success()
        uses: echoings/actions.notify@v0.1.0
        with:
          plat_type: 'Lark'
          notify_title: '${{ env.ECR_REPOSITORY }}-${{ env.ENV_SUFFIX }} 部署完成'
          notify_message: '部署完成，最近的提交: ${{ env.GIT_COMMIT_MESSAGE_SUBJECT }} 作者：${{ env.GIT_COMMIT_COMMITTER_NAME }}  Hash为: ${{ env.GIT_COMMIT_SHORT_SHA }}'
        env:
          NOTIFY_WEBHOOK: ${{ secrets.NOTIFY_WEBHOOK }}
          
    notify-on-failure:
      needs: build-and-deploy
      if: ${{ failure() && (github.ref_name == 'main' || github.ref_name == 'develop' || github.ref_name == 'prod') }}
      runs-on: ubuntu-latest
      steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Set up environment variables
        run: |
          if [ "${{ github.ref_name }}" == "main" ]; then
            echo "ENV_SUFFIX=stage" >> $GITHUB_ENV
          elif [ "${{ github.ref_name }}" == "develop" ]; then
            echo "ENV_SUFFIX=develop" >> $GITHUB_ENV
          elif [ "${{ github.ref_name }}" == "prod" ]; then
            echo "ENV_SUFFIX=production" >> $GITHUB_ENV
          fi
          
      - name: Expose git commit data
        uses: rlespinasse/git-commit-data-action@v1
          
      - name: Notify Failure to IM
        uses: echoings/actions.notify@v0.1.0
        with:
          plat_type: 'Lark'
          notify_title: '${{ github.event.repository.name }}-${{ env.ENV_SUFFIX }} 部署失败'
          notify_message: '部署失败（可能在构建或部署阶段），最近的提交: ${{ env.GIT_COMMIT_MESSAGE_SUBJECT }} 作者：${{ env.GIT_COMMIT_COMMITTER_NAME }}  Hash为: ${{ env.GIT_COMMIT_SHORT_SHA }}'
        env:
          NOTIFY_WEBHOOK: ${{ secrets.NOTIFY_WEBHOOK }}