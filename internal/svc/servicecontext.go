package svc

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/config"
	"github.com/sekai-app/sekai-go/internal/crates/ffmpeg"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/opensearch"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/redis"
	"github.com/sekai-app/sekai-go/internal/middleware"
	red "github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
)

type ServiceContext struct {
	APICacheMiddleware rest.Middleware

	Config   config.Config
	DB       pg.Querier
	Dynamo   dynamo.DB
	S3       s3.S3
	FFmpeg   ffmpeg.FFmpeg
	Opsearch opensearch.OpenSearch
	RedisOp  redis.Redis
}

func NewServiceContext(c config.Config) *ServiceContext {
	// 创建数据库连接池
	pool, err := c.DB.GetPool(context.Background())
	if err != nil {
		panic(err)
	}
	// 初始化数据库
	queries := pg.New(pool)

	// 初始化 DynamoDB
	dynamo, err := dynamo.NewDynamoDB(c.Dynamo)
	if err != nil {
		panic(err)
	}

	// 初始化 Redis 客户端
	rdsCli := red.MustNewRedis(c.Redis)
	if !rdsCli.PingCtx(context.Background()) {
		panic("redis ping failed")
	}
	s3 := s3.MustNewS3(c.S3, redis.NewS3PresingCache(rdsCli))

	redisOp := redis.NewRedis(rdsCli)

	// 初始化 OpenSearch
	opsearch, err := opensearch.NewOpenSearch(c.OpenSearch)
	if err != nil {
		panic(err)
	}

	return &ServiceContext{
		APICacheMiddleware: middleware.NewAPICacheMiddleware().Handle,
		Config:             c,
		DB:                 queries,
		Dynamo:             dynamo,
		S3:                 s3,
		FFmpeg:             ffmpeg.MustNewFFmpeg(),
		Opsearch:           opsearch,
		RedisOp:            redisOp,
	}
}
