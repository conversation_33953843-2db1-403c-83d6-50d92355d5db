// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.1

package types

type Accent struct {
	ID        int    `json:"id"`
	Name      string `json:"name"`
	VoiceName string `json:"voice_name"`
}

type BGMBaseInfo struct {
	ID              int            `json:"id"`
	Name            string         `json:"name"`
	URL             string         `json:"url"`
	CoverURL        string         `json:"cover_url"`
	Duration        int            `json:"duration"`
	Tags            []string       `json:"tags"`
	ReferenceCount  int            `json:"reference_count"`
	CreatorUserID   int            `json:"creator_user_id"`
	CreatorUserName string         `json:"creator_user_name"`
	Public          bool           `json:"public"`
	NSFW            bool           `json:"nsfw"`
	Liked           bool           `json:"liked"`
	Categories      []*BGMCategory `json:"categories"`
}

type BGMCategory struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	CoverURL string `json:"cover_url"`
}

type BGMCategoryPagination struct {
	Pagination
	Items []*BGMCategory `json:"items"`
}

type BGMPagination struct {
	Pagination
	Items []*BGMBaseInfo `json:"items"`
}

type CloneVoiceReq struct {
	RecordURL string `json:"record_url"`
	AccentID  int    `json:"accent_id"`
}

type CloneVoiceResp struct {
	ResponseBase
	Data *VoiceInfo `json:"data"`
}

type CreateBGMReq struct {
	Name       string   `json:"name"`
	URL        string   `json:"url"`
	Duration   int      `json:"duration"`
	CoverURL   string   `json:"cover_url,optional"`
	Tags       []string `json:"tags,optional"`
	Public     bool     `json:"public,default=false"`
	Categories []int32  `json:"categories,optional"`
	PremiseIDs []string `json:"premise_ids,optional"`
}

type CreateBGMResp struct {
	ResponseBase
	Data BGMBaseInfo `json:"data"`
}

type DeleteBGMReq struct {
	ID int `json:"id"`
}

type DeleteBGMResp struct {
	ResponseBase
}

type DiscoverPageConfigData struct {
	Title                 string  `json:"title"`
	Subtitle              string  `json:"subtitle"`
	CoverImageUrl         string  `json:"cover_image_url"`
	SecondaryPageImageUrl string  `json:"secondary_page_image_url"`
	LinkedSekaiIDs        []int64 `json:"linked_sekai_ids"`
}

type DiscoverPageConfigItem struct {
	ID        int64                  `json:"id"`
	Type      string                 `json:"type"`
	Data      DiscoverPageConfigData `json:"data"`
	Status    string                 `json:"status"`
	SortOrder int32                  `json:"sort_order"`
	CreatedAt int64                  `json:"created_at"` // timestamp
	UpdatedAt int64                  `json:"updated_at"` // timestamp
}

type DiscoverPageConfigPagination struct {
	Pagination
	Items []*DiscoverPageConfigItem `json:"items"`
}

type GetAccentsPagination struct {
	Pagination
	Items []*Accent `json:"items"`
}

type GetAccentsReq struct {
	Page int `form:"page,default=1,range=[1:]"`
	Size int `form:"size,default=10,range=[1:]"`
}

type GetAccentsResp struct {
	ResponseBase
	Data *GetAccentsPagination `json:"data"`
}

type GetBGMByIDReq struct {
	ID int `form:"id"`
}

type GetBGMByIDResp struct {
	ResponseBase
	Data BGMBaseInfo `json:"data"`
}

type GetBGMCategoryReq struct {
	Page int `form:"page,default=1,range=[1:]"`
	Size int `form:"size,default=10,range=[1:]"`
}

type GetBGMCategoryResp struct {
	ResponseBase
	Data BGMCategoryPagination `json:"data"`
}

type GetBGMsByCategoryReq struct {
	CategoryID int `form:"category_id"`
	Page       int `form:"page,default=1,range=[1:]"`
	Size       int `form:"size,default=10,range=[1:]"`
}

type GetBGMsByCategoryResp struct {
	ResponseBase
	Data BGMPagination `json:"data"`
}

type GetByCategoryReq struct {
	CategoryID int `form:"category_id"`
	Page       int `form:"page,default=1,range=[1:]"`
	Size       int `form:"size,default=10,range=[1:]"`
}

type GetByCategoryResp struct {
	ResponseBase
	Data *VoicePagination `json:"data"`
}

type GetCategoryPagination struct {
	Pagination
	Items []*VoiceCategory `json:"items"`
}

type GetCategoryReq struct {
	Page int `form:"page,default=1,range=[1:]"`
	Size int `form:"size,default=99,range=[1:]"`
}

type GetCategoryResp struct {
	ResponseBase
	Data *GetCategoryPagination `json:"data"`
}

type GetCreatedByUserReq struct {
	UserID int `form:"user_id"`
	Page   int `form:"page,default=1,range=[1:]"`
	Size   int `form:"size,default=10,range=[1:]"`
}

type GetCreatedByUserResp struct {
	ResponseBase
	Data BGMPagination `json:"data"`
}

type GetDiscoverPageConfigByTypeReq struct {
	Type string `form:"type"`                       // 配置类型
	Page int    `form:"page,default=1,range=[1:]"`  // 页码，从1开始
	Size int    `form:"size,default=10,range=[1:]"` // 每页数量
}

type GetDiscoverPageConfigByTypeResp struct {
	ResponseBase
	Data *DiscoverPageConfigPagination `json:"data"`
}

type GetHotSekaiReq struct {
	Page int `form:"page,default=1,range=[1:]"`
	Size int `form:"size,default=10,range=[1:]"`
}

type GetHotSekaiResp struct {
	ResponseBase
	Data HotSekaiPagination `json:"data"`
}

type GetLikedByUserReq struct {
	UserID int `form:"user_id,optional"`
	Page   int `form:"page,default=1,range=[1:]"`
	Size   int `form:"size,default=10,range=[1:]"`
}

type GetLikedByUserResp struct {
	ResponseBase
	Data *VoicePagination `json:"data"`
}

type GetQuestionReq struct {
}

type GetQuestionResp struct {
	ResponseBase
	Data string `json:"data"`
}

type GetSekaiKeywordsReq struct {
	Prefix string `form:"prefix"`
	Page   int    `form:"page,default=1,range=[1:]"`
	Size   int    `form:"size,default=10,range=[1:]"`
}

type GetSekaiKeywordsResp struct {
	ResponseBase
	Data SekaiKeywordsData `json:"data"`
}

type GetTrendingBGMReq struct {
	Page int `form:"page,default=1,range=[1:]"`
	Size int `form:"size,default=10,range=[1:]"`
}

type GetTrendingBGMResp struct {
	ResponseBase
	Data BGMPagination `json:"data"`
}

type GetTrendingReq struct {
	Page int `form:"page,default=1,range=[1:]"`
	Size int `form:"size,default=10,range=[1:]"`
}

type GetTrendingResp struct {
	ResponseBase
	Data *VoicePagination `json:"data"`
}

type GetUserLikedBGMReq struct {
	UserID int `form:"user_id,optional"`
	Page   int `form:"page,default=1,range=[1:]"`
	Size   int `form:"size,default=10,range=[1:]"`
}

type GetUserLikedBGMResp struct {
	ResponseBase
	Data BGMPagination `json:"data"`
}

type GetVoiceByIDReq struct {
	VoiceID int `form:"voice_id"`
}

type GetVoiceByIDResp struct {
	ResponseBase
	Data *VoiceInfo `json:"data"`
}

type GetVoiceCreatedByUserReq struct {
	UserID int `form:"user_id,optional"`
	Page   int `form:"page,default=1,range=[1:]"`
	Size   int `form:"size,default=10,range=[1:]"`
}

type GetVoiceCreatedByUserResp struct {
	ResponseBase
	Data *VoicePagination `json:"data"`
}

type HealthReq struct {
}

type HealthRsp struct {
	ResponseBase
}

type HotSekaiPagination struct {
	Pagination
	Items []*HotSekaiResult `json:"items"`
}

type HotSekaiResult struct {
	SekaiID       int    `json:"sekai_id"`
	Title         string `json:"title"`
	MinAppVersion int    `json:"min_app_version"`
}

type LableInfo struct {
	LableID     int    `json:"lable_id"`
	LabelName   string `json:"label_name"`
	DisplayName string `json:"display_name"`
	Emoji       string `json:"emoji"`
}

type LikeVoiceReq struct {
	VoiceID int  `json:"voice_id"`
	Like    bool `json:"like"`
}

type LikeVoiceResp struct {
	ResponseBase
}

type Pagination struct {
	Total int `json:"total"`
	Page  int `json:"page"`
	Size  int `json:"size"`
	Pages int `json:"pages"`
}

type ResponseBase struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type SearchBGMReq struct {
	Keyword string `form:"keyword"`
	Page    int    `form:"page,default=1,range=[1:]"`
	Size    int    `form:"size,default=10,range=[1:]"`
}

type SearchBGMResp struct {
	ResponseBase
	Data BGMPagination `json:"data"`
}

type SearchReq struct {
	Keyword string `form:"keyword"`
	Page    int    `form:"page,default=1,range=[1:]"`
	Size    int    `form:"size,default=10,range=[1:]"`
}

type SearchResp struct {
	ResponseBase
	Data *VoicePagination `json:"data"`
}

type SearchSekaiReq struct {
	Keywords string `form:"keywords"`
	Page     int    `form:"page,default=1,range=[1:]"`
	Size     int    `form:"size,default=10,range=[1:]"`
}

type SearchSekaiResp struct {
	ResponseBase
	Data SekaiBasePagination `json:"data"`
}

type SearchStoryReq struct {
	Keywords string `form:"keywords"`
	Page     int    `form:"page,default=1,range=[1:]"`
	Size     int    `form:"size,default=10,range=[1:]"`
}

type SearchStoryResp struct {
	ResponseBase
	Data StoryBasePagination `json:"data"`
}

type SekaiBaseInfo struct {
	SekaiID         int          `json:"sekai_id"`          // sekai id
	Title           string       `json:"title"`             // 标题
	CoverURL        string       `json:"cover_url"`         // 封面url
	NewCoverURL     string       `json:"new_cover_url"`     // 新封面url
	CreatorUserID   int          `json:"creator_user_id"`   // 创建者id
	CreatorUserName string       `json:"creator_user_name"` // 创建者用户名
	Public          bool         `json:"public"`            // 是否公开
	Intro           string       `json:"intro"`             // 简介
	BackgroundURL   string       `json:"background_url"`    // 背景url
	Categories      []*LableInfo `json:"categories"`        // 分类
	ViewCount       int          `json:"view_count"`        // 浏览量
	TemplateID      int          `json:"template_id"`       // 模板id
	MinAppVersion   int          `json:"min_app_version"`   // 最小app版本
}

type SekaiBasePagination struct {
	Pagination
	Items []*SekaiBaseInfo `json:"items"`
}

type SekaiKeywordsData struct {
	Pagination
	Items []string `json:"items"`
}

type StoryBaseInfo struct {
	StoryID         int    `json:"story_id"`          // story id
	HighlightText   string `json:"highlight_text"`    // 亮点
	GifURL          string `json:"gif_url"`           // gif url
	CreatorUserID   int    `json:"creator_user_id"`   // 创建者id
	CreatorUserName string `json:"creator_user_name"` // 创建者用户名
	Public          bool   `json:"public"`            // 是否公开
	IsDeleted       bool   `json:"is_deleted"`        // 是否删除
	WhatIf          string `json:"what_if"`           // 设定
	ViewCount       int    `json:"view_count"`        // 浏览量
}

type StoryBasePagination struct {
	Pagination
	Items []*StoryBaseInfo `json:"items"`
}

type UpdateBGMReq struct {
	ID         int      `json:"id"`
	Name       *string  `json:"name,optional"`
	URL        *string  `json:"url,optional"`
	Duration   *int     `json:"duration,optional"`
	CoverURL   *string  `json:"cover_url,optional"`
	Tags       []string `json:"tags,optional"`
	Public     *bool    `json:"public,optional"`
	Categories []int32  `json:"categories,optional"`
	PremiseIDs []string `json:"premise_ids,optional"`
}

type UpdateBGMResp struct {
	ResponseBase
	Data BGMBaseInfo `json:"data"`
}

type UpdateInfo struct {
	DisplayName *string `json:"display_name,optional"`
	Enable      *bool   `json:"enable,optional"`
	Public      *bool   `json:"public,optional"`
	Categories  []int   `json:"categories,optional"`
	AccentID    *int    `json:"accent_id,optional"`
}

type UpdateVoiceByIDReq struct {
	VoiceID    int        `json:"voice_id"`
	UpdateInfo UpdateInfo `json:"update_info"`
}

type UpdateVoiceByIDResp struct {
	ResponseBase
	Data *VoiceInfo `json:"data"`
}

type UserInfoReq struct {
	ID           int    `path:"id"`
	OptionalCase string `form:"optional_case,optional"` // 可选参数
}

type UserInfoRsp struct {
	ResponseBase
	Name string `json:"name"`
}

type VoiceCategory struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	CoverURL string `json:"cover_url"`
}

type VoiceInfo struct {
	VoiceID            int              `json:"voice_id"`
	DisplayName        string           `json:"display_name"`
	SampleURL          string           `json:"sample_url"`
	RecordText         string           `json:"record_text"`
	ProcessedRecordURL string           `json:"processed_record_url"`
	DisplayEnable      bool             `json:"display_enable"`
	CharName           string           `json:"char_name"`
	LabsTag            []string         `json:"labs_tag"`
	LabsID             string           `json:"labs_id"`
	RvcTag             []string         `json:"rvc_tag"`
	RvcID              string           `json:"rvc_id"`
	RvcTranspose       int              `json:"rvc_transpose"`
	CreatorUserID      int              `json:"creator_user_id"`
	CreatorUserName    string           `json:"creator_user_name"`
	CreationType       int32            `json:"creation_type"`
	Enable             bool             `json:"enable"`
	CreateTime         int64            `json:"create_time"`
	Duration           int              `json:"duration"`
	Public             bool             `json:"public"`
	Nsfw               bool             `json:"nsfw"`
	Liked              bool             `json:"liked"`
	Categories         []*VoiceCategory `json:"categories"`
	Accent             Accent           `json:"accent"`
}

type VoicePagination struct {
	Pagination
	Items []*VoiceInfo `json:"items"`
}
