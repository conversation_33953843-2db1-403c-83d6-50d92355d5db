package middleware

import (
	"crypto/sha256"
	"encoding/hex"
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/core/collection"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// APICacheMiddleware HTTP接口缓存中间件
// 只缓存GET请求，缓存10分钟，最多缓存10000条记录
type APICacheMiddleware struct {
	cache *collection.Cache // go-zero的缓存接口
}

// NewAPICacheMiddleware 创建一个新的API缓存中间件
func NewAPICacheMiddleware() *APICacheMiddleware {
	// 创建内存缓存
	c, err := collection.NewCache(10*time.Minute, collection.WithLimit(10000))
	if err != nil {
		panic(err)
	}

	m := &APICacheMiddleware{
		cache: c,
	}

	return m
}

// Handle 处理HTTP请求
func (m *APICacheMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 检查当前路径是否需要缓存
		if !m.shouldCache(r) {
			next(w, r)
			return
		}

		// 生成缓存键
		cacheKey, err := m.generateCacheKey(r)
		if err != nil {
			logc.Errorf(r.Context(), "Failed to generate cache key: %v", err)
			next(w, r)
			return
		}

		// 尝试从缓存获取
		if cached, ok := m.cache.Get(cacheKey); ok {
			w.Header().Add(httpx.ContentType, httpx.JsonContentType)
			w.Header().Add("X-Api-Cache", "HIT")
			w.WriteHeader(http.StatusOK)
			_, err := w.Write(cached.([]byte)) // nolint:errcheck
			if err != nil {
				logc.Errorf(r.Context(), "Failed to write cached response: %v", err)
			}
			return
		}

		// 缓存未命中，继续处理请求并拦截响应
		rw := &responseWriter{
			ResponseWriter: w,
		}
		next(rw, r)

		// 只缓存成功的响应 (状态码 200)
		if rw.statusCode == http.StatusOK {
			m.cache.Set(cacheKey, rw.body)
		}
	}
}

// shouldCache 判断当前请求是否应该被缓存
func (m *APICacheMiddleware) shouldCache(r *http.Request) bool {
	// 只缓存GET请求
	return r.Method == http.MethodGet
}

// generateCacheKey 根据请求生成缓存键
func (m *APICacheMiddleware) generateCacheKey(r *http.Request) (string, error) {
	hasher := sha256.New()

	// 添加HTTP方法和路径
	hasher.Write([]byte(r.Method))
	hasher.Write([]byte(r.URL.Path))
	hasher.Write([]byte(r.URL.Query().Encode()))

	// 生成最终的缓存键
	hash := hex.EncodeToString(hasher.Sum(nil))
	return "apicache:" + hash, nil
}

type responseWriter struct {
	http.ResponseWriter
	statusCode int
	body       []byte
}

func (w *responseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body = b
	return w.ResponseWriter.Write(b)
}
