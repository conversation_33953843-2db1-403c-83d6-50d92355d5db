package middleware

import (
	"net/http"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/zeromicro/go-zero/core/logx"
)

// DebugLogMiddleware 用于向日志中添加必要的 Field，辅助 Debug
type DebugLogMiddleware struct {
}

func NewDebugLogMiddleware() *DebugLogMiddleware {
	return &DebugLogMiddleware{}
}

func (m *DebugLogMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		msg := servermsg.Get(r.Context())

		fields := []logx.LogField{
			{Key: "api", Value: r.URL.Path},
			{Key: "user_id", Value: msg.CurrentUser.UserID},
			{Key: "request_id", Value: msg.RequestID},
			{Key: "app_version", Value: msg.AppVersion},
			{Key: "remote_ip", Value: msg.RemoteAddr},
		}
		ctx := logx.ContextWithFields(r.Context(), fields...)
		next(w, r.WithContext(ctx))
	}
}
