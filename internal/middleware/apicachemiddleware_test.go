package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewAPICacheMiddleware(t *testing.T) {
	middleware := NewAPICacheMiddleware()
	assert.NotNil(t, middleware)
	assert.NotNil(t, middleware.cache)
}

func TestAPICacheMiddleware_Handle(t *testing.T) {
	// Test scenarios
	tests := []struct {
		name          string
		method        string
		path          string
		queryParams   string
		initialStatus int
		responseBody  []byte
		runTwice      bool
		expectedCache bool // Whether cache should be used on second request
	}{
		{
			name:          "GET request should be cached",
			method:        http.MethodGet,
			path:          "/api/test",
			queryParams:   "",
			initialStatus: http.StatusOK,
			responseBody:  []byte(`{"success":true}`),
			runTwice:      true,
			expectedCache: true,
		},
		{
			name:          "GET request with query params should be cached separately",
			method:        http.MethodGet,
			path:          "/api/test",
			queryParams:   "param=value",
			initialStatus: http.StatusOK,
			responseBody:  []byte(`{"success":true,"param":"value"}`),
			runTwice:      true,
			expectedCache: true,
		},
		{
			name:          "Non-200 response should not be cached",
			method:        http.MethodGet,
			path:          "/api/error",
			initialStatus: http.StatusBadRequest,
			responseBody:  []byte(`{"error":"Bad request"}`),
			runTwice:      true,
			expectedCache: false,
		},
		{
			name:          "POST request should not be cached",
			method:        http.MethodPost,
			path:          "/api/test",
			initialStatus: http.StatusOK,
			responseBody:  []byte(`{"success":true}`),
			runTwice:      true,
			expectedCache: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a new middleware
			middleware := NewAPICacheMiddleware()

			// Create a test handler that returns a predefined response
			testHandler := func(w http.ResponseWriter, _ *http.Request) {
				w.WriteHeader(tt.initialStatus)
				_, _ = w.Write(tt.responseBody)
			}

			// Wrap the test handler with the middleware
			handler := middleware.Handle(testHandler)

			// Setup path with query parameters if provided
			url := tt.path
			if tt.queryParams != "" {
				url = url + "?" + tt.queryParams
			}

			// First request
			req1 := httptest.NewRequest(tt.method, url, nil)
			rec1 := httptest.NewRecorder()
			handler(rec1, req1)

			// Verify first response
			assert.Equal(t, tt.initialStatus, rec1.Code)
			assert.Equal(t, tt.responseBody, rec1.Body.Bytes())

			// No cache header on first request
			assert.Empty(t, rec1.Header().Get("X-Api-Cache"))

			if tt.runTwice {
				// Second request
				req2 := httptest.NewRequest(tt.method, url, nil)
				rec2 := httptest.NewRecorder()
				handler(rec2, req2)

				// Verify second response
				assert.Equal(t, tt.initialStatus, rec2.Code)
				assert.Equal(t, tt.responseBody, rec2.Body.Bytes())

				// Check if cache was used
				if tt.expectedCache {
					assert.Equal(t, "HIT", rec2.Header().Get("X-Api-Cache"))
				} else {
					assert.Empty(t, rec2.Header().Get("X-Api-Cache"))
				}
			}
		})
	}
}

func TestAPICacheMiddleware_GenerateCacheKey(t *testing.T) {
	tests := []struct {
		name     string
		method   string
		path     string
		query    string
		expected string // Expected key prefix
	}{
		{
			name:     "simple GET request",
			method:   http.MethodGet,
			path:     "/api/test",
			query:    "",
			expected: "apicache:",
		},
		{
			name:     "GET request with query params",
			method:   http.MethodGet,
			path:     "/api/test",
			query:    "param=value",
			expected: "apicache:",
		},
		{
			name:     "POST request",
			method:   http.MethodPost,
			path:     "/api/test",
			query:    "",
			expected: "apicache:",
		},
	}

	middleware := NewAPICacheMiddleware()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			url := tt.path
			if tt.query != "" {
				url = url + "?" + tt.query
			}
			req := httptest.NewRequest(tt.method, url, nil)

			key, err := middleware.generateCacheKey(req)
			assert.NoError(t, err)
			assert.NotEmpty(t, key)
			assert.True(t, len(key) > len(tt.expected))
			assert.True(t, key[:len(tt.expected)] == tt.expected)
		})
	}
}

func TestResponseWriterImplementation(t *testing.T) {
	// Create underlying http.ResponseWriter
	rec := httptest.NewRecorder()

	// Create our wrapper
	writer := &responseWriter{
		ResponseWriter: rec,
	}

	// Test WriteHeader method
	writer.WriteHeader(http.StatusCreated)
	assert.Equal(t, http.StatusCreated, writer.statusCode)
	assert.Equal(t, http.StatusCreated, rec.Code)

	// Test Write method
	data := []byte("test response")
	n, err := writer.Write(data)
	assert.NoError(t, err)
	assert.Equal(t, len(data), n)
	assert.Equal(t, data, writer.body)
	assert.Equal(t, "test response", rec.Body.String())
}

func TestShouldCache(t *testing.T) {
	middleware := NewAPICacheMiddleware()

	tests := []struct {
		name     string
		method   string
		expected bool
	}{
		{
			name:     "GET request should be cached",
			method:   http.MethodGet,
			expected: true,
		},
		{
			name:     "POST request should not be cached",
			method:   http.MethodPost,
			expected: false,
		},
		{
			name:     "PUT request should not be cached",
			method:   http.MethodPut,
			expected: false,
		},
		{
			name:     "DELETE request should not be cached",
			method:   http.MethodDelete,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(tt.method, "/api/test", nil)
			result := middleware.shouldCache(req)
			assert.Equal(t, tt.expected, result)
		})
	}
}
