package middleware

import (
	"testing"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/stretchr/testify/assert"
)

func TestNewServerMsgMiddleware(t *testing.T) {
	// Test that NewServerMsgMiddleware is properly aliased to servermsg.NewMiddleware
	middleware := NewServerMsgMiddleware()

	// Verify that we get a valid middleware instance
	assert.NotNil(t, middleware)
	assert.IsType(t, &servermsg.Middleware{}, middleware)

	// Verify that it's the same type created by servermsg.NewMiddleware
	directMiddleware := servermsg.NewMiddleware()
	assert.IsType(t, directMiddleware, middleware)
}
