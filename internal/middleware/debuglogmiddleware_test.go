package middleware

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/stretchr/testify/assert"
)

func TestNewDebugLogMiddleware(t *testing.T) {
	middleware := NewDebugLogMiddleware()
	assert.NotNil(t, middleware)
	assert.IsType(t, &DebugLogMiddleware{}, middleware)
}

func TestDebugLogMiddleware_Handle(t *testing.T) {
	tests := []struct {
		name           string
		setupContext   func() context.Context
		expectedFields map[string]interface{}
	}{
		{
			name: "with complete server message",
			setupContext: func() context.Context {
				serverMsg := &servermsg.ServerMsg{
					CurrentUser: servermsg.CurrentUser{
						UserID:   123,
						UserName: "testuser",
					},
					AppVersion: "1.0.0",
					DeviceID:   "device-123",
					RequestID:  "req-123",
					RemoteAddr: "***********:8080",
					UserAgent:  "Test Agent",
				}
				return context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)
			},
			expectedFields: map[string]interface{}{
				"api":         "/test",
				"user_id":     123,
				"request_id":  "req-123",
				"app_version": "1.0.0",
				"remote_ip":   "***********",
			},
		},
		{
			name: "with empty remote address",
			setupContext: func() context.Context {
				serverMsg := &servermsg.ServerMsg{
					CurrentUser: servermsg.CurrentUser{
						UserID:   456,
						UserName: "testuser2",
					},
					AppVersion: "2.0.0",
					RequestID:  "req-456",
					RemoteAddr: "",
				}
				return context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)
			},
			expectedFields: map[string]interface{}{
				"api":         "/test",
				"user_id":     456,
				"request_id":  "req-456",
				"app_version": "2.0.0",
				"remote_ip":   "",
			},
		},
		{
			name: "with invalid remote address format",
			setupContext: func() context.Context {
				serverMsg := &servermsg.ServerMsg{
					CurrentUser: servermsg.CurrentUser{
						UserID: 789,
					},
					RequestID:  "req-789",
					RemoteAddr: "invalid-format",
				}
				return context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)
			},
			expectedFields: map[string]interface{}{
				"api":         "/test",
				"user_id":     789,
				"request_id":  "req-789",
				"app_version": "",
				"remote_ip":   "invalid-format",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a middleware instance
			middleware := NewDebugLogMiddleware()

			// Create a test handler that will verify log fields
			var contextWithFields context.Context
			nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				contextWithFields = r.Context()
				w.WriteHeader(http.StatusOK)
			})

			// Create a test request with the context
			req := httptest.NewRequest("GET", "/test", nil)
			req = req.WithContext(tt.setupContext())

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute middleware
			handler := middleware.Handle(nextHandler)
			handler.ServeHTTP(rr, req)

			// Verify response
			assert.Equal(t, http.StatusOK, rr.Code)

			// Verify fields were added to the context
			// Since logx.ContextWithFields doesn't provide a way to extract fields,
			// we can only verify that the context is different from the original
			assert.NotEqual(t, req.Context(), contextWithFields)
		})
	}
}
