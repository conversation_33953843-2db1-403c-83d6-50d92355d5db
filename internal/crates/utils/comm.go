package utils

import "github.com/sekai-app/sekai-go/internal/types"

func GetWithDefault[T comparable](value T, defaultValue T) T {
	v := new(T)
	if *v == value {
		return defaultValue
	}
	return value
}

// GetPageInfo 获取分页信息
func GetPageInfo(total int, page int, size int) types.Pagination {
	pages := int(total) / size
	if int(total)%size > 0 {
		pages++
	}
	return types.Pagination{
		Total: total,
		Page:  page,
		Size:  size,
		Pages: pages,
	}
}
