// Package utils provides utility functions for the application,
// including error handling, conversion utilities, and common helpers.
package utils

import (
	"context"
	"net/http"

	"github.com/sekai-app/sekai-go/internal/types"
	xerrors "github.com/zeromicro/x/errors"
)

// getHTTPStatusCode converts an error code to an HTTP status code based on
// the convention defined in the internal/values package.
func getHTTPStatusCode(code int) int {
	if code < 1000 {
		return code
	}

	return code / 1000
}

// ErrorHandler provides a unified error handling mechanism for the API.
// It formats errors into a consistent response structure and determines
// the appropriate HTTP status code.
func ErrorHandler(_ context.Context, err error) (int, any) {
	rsp := &types.ResponseBase{}
	switch e := err.(type) {
	case *xerrors.CodeMsg:
		rsp.Code = e.Code
		rsp.Msg = e.Msg
	default:
		return http.StatusBadRequest, err
	}

	return getHTTPStatusCode(rsp.Code), rsp
}
