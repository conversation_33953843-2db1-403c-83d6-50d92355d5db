package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestToAppVersionString(t *testing.T) {
	tests := []struct {
		name     string
		version  int
		expected string
	}{
		{
			name:     "normal version",
			version:  1017000,
			expected: "1.17.0",
		},
		{
			name:     "single digit major version",
			version:  2003004,
			expected: "2.3.4",
		},
		{
			name:     "double digit minor version",
			version:  3045006,
			expected: "3.45.6",
		},
		{
			name:     "triple digit patch version",
			version:  4005999,
			expected: "4.5.999",
		},
		{
			name:     "zero version",
			version:  0,
			expected: "0.0.0",
		},
		{
			name:     "large version numbers",
			version:  9999999,
			expected: "9.999.999",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToAppVersionString(tt.version)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestToAppVersionInt(t *testing.T) {
	tests := []struct {
		name     string
		version  string
		expected int
	}{
		{
			name:     "normal version",
			version:  "1.17.0",
			expected: 1017000,
		},
		{
			name:     "single digit components",
			version:  "2.3.4",
			expected: 2003004,
		},
		{
			name:     "double digit minor version",
			version:  "3.45.6",
			expected: 3045006,
		},
		{
			name:     "triple digit patch version",
			version:  "4.5.999",
			expected: 4005999,
		},
		{
			name:     "zero version",
			version:  "0.0.0",
			expected: 0,
		},
		{
			name:     "large version numbers",
			version:  "9.999.999",
			expected: 9999999,
		},
		{
			name:     "incorrect format - too few components",
			version:  "1.2",
			expected: 0,
		},
		{
			name:     "incorrect format - too many components",
			version:  "*******",
			expected: 0,
		},
		{
			name:     "incorrect format - non-numeric major",
			version:  "a.2.3",
			expected: 0,
		},
		{
			name:     "incorrect format - non-numeric minor",
			version:  "1.b.3",
			expected: 0,
		},
		{
			name:     "incorrect format - non-numeric patch",
			version:  "1.2.c",
			expected: 0,
		},
		{
			name:     "empty string",
			version:  "",
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToAppVersionInt(tt.version)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestRoundTrip(t *testing.T) {
	// Test round-trip conversion (int -> string -> int)
	versionInts := []int{1017000, 2003004, 3045067, 0, 9999999}
	for _, v := range versionInts {
		t.Run(ToAppVersionString(v), func(t *testing.T) {
			str := ToAppVersionString(v)
			back := ToAppVersionInt(str)
			assert.Equal(t, v, back)
		})
	}

	// Test round-trip conversion (string -> int -> string)
	versionStrs := []string{"1.17.0", "2.3.4", "3.45.67", "0.0.0", "9.999.999"}
	for _, v := range versionStrs {
		t.Run(v, func(t *testing.T) {
			num := ToAppVersionInt(v)
			back := ToAppVersionString(num)
			assert.Equal(t, v, back)
		})
	}
}
