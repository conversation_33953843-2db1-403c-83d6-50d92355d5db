package utils

import (
	"fmt"
	"strconv"
	"strings"
)

// ToAppVersionString 将版本号转换为字符串
func ToAppVersionString(version int) string {
	// Convert MinVersion from integer (e.g., 1017000) to semver format (e.g., "1.17.0")
	// Each segment is 3 digits: major (1), minor (017), patch (000)
	versionStr := fmt.Sprintf("%d.%d.%d",
		version/1000000,        // Major version
		(version%1000000)/1000, // Minor version
		version%1000)           // Patch version
	return versionStr
}

// ToAppVersionInt 将版本号转换为整数
func ToAppVersionInt(versionStr string) int {
	version := strings.Split(versionStr, ".")
	if len(version) != 3 {
		return 0
	}

	major, err := strconv.Atoi(version[0])
	if err != nil {
		return 0
	}
	minor, err := strconv.Atoi(version[1])
	if err != nil {
		return 0
	}
	patch, err := strconv.Atoi(version[2])
	if err != nil {
		return 0
	}

	return major*1000000 + minor*1000 + patch
}
