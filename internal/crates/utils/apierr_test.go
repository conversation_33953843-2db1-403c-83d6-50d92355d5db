package utils

import (
	"context"
	"errors"
	"net/http"
	"testing"

	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
)

func TestGetHTTPStatusCode(t *testing.T) {
	tests := []struct {
		name     string
		code     int
		expected int
	}{
		{
			name:     "code less than 1000",
			code:     http.StatusBadRequest,
			expected: http.StatusBadRequest,
		},
		{
			name:     "code 1000",
			code:     1000,
			expected: 1,
		},
		{
			name:     "code 2000",
			code:     2000,
			expected: 2,
		},
		{
			name:     "code 4000",
			code:     4000,
			expected: 4,
		},
		{
			name:     "code 5000",
			code:     5000,
			expected: 5,
		},
		{
			name:     "code 40001",
			code:     40001,
			expected: 40,
		},
		{
			name:     "code 50001",
			code:     50001,
			expected: 50,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getHTTPStatusCode(tt.code)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestErrorHandler(t *testing.T) {
	ctx := context.Background()

	t.Run("with xerrors.CodeMsg error", func(t *testing.T) {
		err := xerrors.New(1001, "test error")
		statusCode, resp := ErrorHandler(ctx, err)

		assert.Equal(t, 1, statusCode)

		respObj, ok := resp.(*types.ResponseBase)
		assert.True(t, ok)
		assert.Equal(t, 1001, respObj.Code)
		assert.Equal(t, "test error", respObj.Msg)
	})

	t.Run("with standard error", func(t *testing.T) {
		err := errors.New("standard error")
		statusCode, resp := ErrorHandler(ctx, err)

		assert.Equal(t, http.StatusBadRequest, statusCode)
		assert.Equal(t, err, resp)
	})

	t.Run("with HTTP status code error", func(t *testing.T) {
		err := xerrors.New(http.StatusNotFound, "not found")
		statusCode, resp := ErrorHandler(ctx, err)

		assert.Equal(t, http.StatusNotFound, statusCode)

		respObj, ok := resp.(*types.ResponseBase)
		assert.True(t, ok)
		assert.Equal(t, http.StatusNotFound, respObj.Code)
		assert.Equal(t, "not found", respObj.Msg)
	})
}
