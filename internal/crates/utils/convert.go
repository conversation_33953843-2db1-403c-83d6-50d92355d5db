package utils

import (
	"os"

	"github.com/jackc/pgx/v5/pgtype"
)

// CovertEnvName converts the environment name to the format used by legacy Python services.
// Returns "prod" for production or stage environments, and "stage" for others.
func CovertEnvName() string {
	env := os.Getenv("AIU_ENV")
	// nolint
	if env == "production" || env == "stage" {
		return "prod"
	}
	return "stage"
}

// CovertNumberSlice converts a slice of numbers from one numeric type to another.
// Supports conversion between int, int32, and int64.
func CovertNumberSlice[T int | int32 | int64, R int | int32 | int64](numbers []T) []R {
	result := make([]R, 0, len(numbers))
	for _, number := range numbers {
		result = append(result, R(number))
	}
	return result
}

// ToPGText converts a string pointer to a pgtype.Text value for PostgreSQL.
// If the input is nil, returns an invalid pgtype.Text.
func ToPGText(text *string) pgtype.Text {
	if text == nil {
		return pgtype.Text{String: "", Valid: false}
	}
	return pgtype.Text{String: *text, Valid: true}
}

// ToPGBool converts a boolean pointer to a pgtype.Bool value for PostgreSQL.
// If the input is nil, returns an invalid pgtype.Bool.
func ToPGBool(value *bool) pgtype.Bool {
	if value == nil {
		return pgtype.Bool{Bool: false, Valid: false}
	}
	return pgtype.Bool{Bool: *value, Valid: true}
}

// ToPGInt8 converts an integer pointer to a pgtype.Int8 value for PostgreSQL.
// If the input is nil, returns an invalid pgtype.Int8.
func ToPGInt8[T int | int32 | int64](value *T) pgtype.Int8 {
	if value == nil {
		return pgtype.Int8{Int64: 0, Valid: false}
	}
	return pgtype.Int8{Int64: int64(*value), Valid: true}
}

// ToPGInt4 converts an integer pointer to a pgtype.Int4 value for PostgreSQL.
// If the input is nil, returns an invalid pgtype.Int4.
func ToPGInt4[T int | int32 | int64](value *T) pgtype.Int4 {
	if value == nil {
		return pgtype.Int4{Int32: 0, Valid: false}
	}
	return pgtype.Int4{Int32: int32(*value), Valid: true}
}
