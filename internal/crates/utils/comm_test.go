package utils

import (
	"testing"

	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/stretchr/testify/assert"
)

func TestGetPageInfo(t *testing.T) {
	tests := []struct {
		name     string
		total    int
		page     int
		size     int
		expected types.Pagination
	}{
		{
			name:  "exact division",
			total: 100,
			page:  2,
			size:  20,
			expected: types.Pagination{
				Total: 100,
				Page:  2,
				Size:  20,
				Pages: 5,
			},
		},
		{
			name:  "with remainder",
			total: 101,
			page:  1,
			size:  20,
			expected: types.Pagination{
				Total: 101,
				Page:  1,
				Size:  20,
				Pages: 6,
			},
		},
		{
			name:  "zero total",
			total: 0,
			page:  1,
			size:  10,
			expected: types.Pagination{
				Total: 0,
				Page:  1,
				Size:  10,
				Pages: 0,
			},
		},
		{
			name:  "single page",
			total: 5,
			page:  1,
			size:  10,
			expected: types.Pagination{
				Total: 5,
				Page:  1,
				Size:  10,
				Pages: 1,
			},
		},
		{
			name:  "last page with remainder",
			total: 21,
			page:  3,
			size:  10,
			expected: types.Pagination{
				Total: 21,
				Page:  3,
				Size:  10,
				Pages: 3,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetPageInfo(tt.total, tt.page, tt.size)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetWithDefault(t *testing.T) {
	tests := []struct {
		name         string
		value        interface{}
		defaultValue interface{}
		expected     interface{}
	}{
		{
			name:         "int zero value",
			value:        0,
			defaultValue: 42,
			expected:     42,
		},
		{
			name:         "int non-zero value",
			value:        10,
			defaultValue: 42,
			expected:     10,
		},
		{
			name:         "string empty value",
			value:        "",
			defaultValue: "default",
			expected:     "default",
		},
		{
			name:         "string non-empty value",
			value:        "hello",
			defaultValue: "default",
			expected:     "hello",
		},
		{
			name:         "bool false value",
			value:        false,
			defaultValue: true,
			expected:     true,
		},
		{
			name:         "bool true value",
			value:        true,
			defaultValue: false,
			expected:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			switch v := tt.value.(type) {
			case int:
				result := GetWithDefault(v, tt.defaultValue.(int)) //nolint:errcheck
				assert.Equal(t, tt.expected, result)
			case string:
				result := GetWithDefault(v, tt.defaultValue.(string)) //nolint:errcheck
				assert.Equal(t, tt.expected, result)
			case bool:
				result := GetWithDefault(v, tt.defaultValue.(bool)) //nolint:errcheck
				assert.Equal(t, tt.expected, result)
			}
		})
	}

	// Test with custom struct
	type TestStruct struct {
		Name string
		Age  int
	}

	t.Run("custom struct zero value", func(t *testing.T) {
		var value TestStruct
		defaultValue := TestStruct{Name: "John", Age: 30}
		result := GetWithDefault(value, defaultValue)
		assert.Equal(t, defaultValue, result)
	})

	t.Run("custom struct non-zero value", func(t *testing.T) {
		value := TestStruct{Name: "Alice", Age: 25}
		defaultValue := TestStruct{Name: "John", Age: 30}
		result := GetWithDefault(value, defaultValue)
		assert.Equal(t, value, result)
	})

	// Test with pointers
	t.Run("nil pointer", func(t *testing.T) {
		var value *int
		defaultInt := 42
		defaultValue := &defaultInt
		result := GetWithDefault(value, defaultValue)
		assert.Equal(t, defaultValue, result)
	})

	t.Run("non-nil pointer", func(t *testing.T) {
		valueInt := 10
		value := &valueInt
		defaultInt := 42
		defaultValue := &defaultInt
		result := GetWithDefault(value, defaultValue)
		assert.Equal(t, value, result)
	})
}
