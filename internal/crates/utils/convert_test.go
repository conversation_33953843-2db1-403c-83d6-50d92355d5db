package utils

import (
	"os"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
)

func TestCovertEnvName(t *testing.T) {
	tests := []struct {
		name     string
		envValue string
		expected string
	}{
		{
			name:     "production environment",
			envValue: "production",
			expected: "prod",
		},
		{
			name:     "stage environment",
			envValue: "stage",
			expected: "prod",
		},
		{
			name:     "development environment",
			envValue: "development",
			expected: "stage",
		},
		{
			name:     "empty environment",
			envValue: "",
			expected: "stage",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Save original env and restore after test
			oldEnv := os.Getenv("AIU_ENV")
			defer os.Setenv("AIU_ENV", oldEnv)

			// Set test environment
			os.Setenv("AIU_ENV", tt.envValue)

			// Run test
			result := CovertEnvName()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCovertNumberSlice(t *testing.T) {
	tests := []struct {
		name     string
		input    []int64
		expected []int32
	}{
		{
			name:     "empty slice",
			input:    []int64{},
			expected: []int32{},
		},
		{
			name:     "single value",
			input:    []int64{42},
			expected: []int32{42},
		},
		{
			name:     "multiple values",
			input:    []int64{1, 2, 3, 4, 5},
			expected: []int32{1, 2, 3, 4, 5},
		},
		{
			name:     "large values",
			input:    []int64{2147483647, 1000000000},
			expected: []int32{2147483647, 1000000000},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CovertNumberSlice[int64, int32](tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}

	// Test int to int64 conversion
	t.Run("int to int64", func(t *testing.T) {
		input := []int{1, 2, 3}
		expected := []int64{1, 2, 3}
		result := CovertNumberSlice[int, int64](input)
		assert.Equal(t, expected, result)
	})
}

func TestToPGText(t *testing.T) {
	t.Run("with nil value", func(t *testing.T) {
		result := ToPGText(nil)
		assert.Equal(t, pgtype.Text{String: "", Valid: false}, result)
	})

	t.Run("with empty string", func(t *testing.T) {
		value := ""
		result := ToPGText(&value)
		assert.Equal(t, pgtype.Text{String: "", Valid: true}, result)
	})

	t.Run("with non-empty string", func(t *testing.T) {
		value := "test string"
		result := ToPGText(&value)
		assert.Equal(t, pgtype.Text{String: "test string", Valid: true}, result)
	})
}

func TestToPGBool(t *testing.T) {
	t.Run("with nil value", func(t *testing.T) {
		result := ToPGBool(nil)
		assert.Equal(t, pgtype.Bool{Bool: false, Valid: false}, result)
	})

	t.Run("with true value", func(t *testing.T) {
		value := true
		result := ToPGBool(&value)
		assert.Equal(t, pgtype.Bool{Bool: true, Valid: true}, result)
	})

	t.Run("with false value", func(t *testing.T) {
		value := false
		result := ToPGBool(&value)
		assert.Equal(t, pgtype.Bool{Bool: false, Valid: true}, result)
	})
}

func TestToPGInt8(t *testing.T) {
	t.Run("with nil value", func(t *testing.T) {
		result := ToPGInt8[int](nil)
		assert.Equal(t, pgtype.Int8{Int64: 0, Valid: false}, result)
	})

	t.Run("with int value", func(t *testing.T) {
		value := 42
		result := ToPGInt8(&value)
		assert.Equal(t, pgtype.Int8{Int64: 42, Valid: true}, result)
	})

	t.Run("with int32 value", func(t *testing.T) {
		var value int32 = 42
		result := ToPGInt8(&value)
		assert.Equal(t, pgtype.Int8{Int64: 42, Valid: true}, result)
	})

	t.Run("with int64 value", func(t *testing.T) {
		var value int64 = 9223372036854775807 // Max int64
		result := ToPGInt8(&value)
		assert.Equal(t, pgtype.Int8{Int64: 9223372036854775807, Valid: true}, result)
	})
}

func TestToPGInt4(t *testing.T) {
	t.Run("with nil value", func(t *testing.T) {
		result := ToPGInt4[int](nil)
		assert.Equal(t, pgtype.Int4{Int32: 0, Valid: false}, result)
	})

	t.Run("with int value", func(t *testing.T) {
		value := 42
		result := ToPGInt4(&value)
		assert.Equal(t, pgtype.Int4{Int32: 42, Valid: true}, result)
	})

	t.Run("with int32 value", func(t *testing.T) {
		var value int32 = 2147483647 // Max int32
		result := ToPGInt4(&value)
		assert.Equal(t, pgtype.Int4{Int32: 2147483647, Valid: true}, result)
	})

	t.Run("with int64 value", func(t *testing.T) {
		var value int64 = 42
		result := ToPGInt4(&value)
		assert.Equal(t, pgtype.Int4{Int32: 42, Valid: true}, result)
	})

	t.Run("with large int64 value truncated to int32", func(t *testing.T) {
		var value int64 = 2147483648 // Max int32 + 1
		result := ToPGInt4(&value)
		assert.Equal(t, pgtype.Int4{Int32: -2147483648, Valid: true}, result) // Truncated to int32
	})
}
