package utils

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCleanupFile(t *testing.T) {
	t.Run("cleanup existing file", func(t *testing.T) {
		// Create a temporary file
		tempDir, err := os.MkdirTemp("", "test")
		assert.NoError(t, err)
		defer os.RemoveAll(tempDir)

		tempFile := filepath.Join(tempDir, "test.txt")
		err = os.WriteFile(tempFile, []byte("test content"), 0644)
		assert.NoError(t, err)

		// Verify file exists
		_, err = os.Stat(tempFile)
		assert.NoError(t, err)

		// Call cleanup function
		CleanupFile(tempFile)

		// Verify file does not exist
		_, err = os.Stat(tempFile)
		assert.True(t, os.IsNotExist(err))
	})

	t.Run("cleanup non-existent file", func(t *testing.T) {
		// Use a path that shouldn't exist
		nonExistentFile := "/tmp/nonexistent_file_that_should_never_exist_for_testing"

		// Verify file does not exist
		_, err := os.Stat(nonExistentFile)
		assert.True(t, os.IsNotExist(err))

		// Cleanup should not error
		CleanupFile(nonExistentFile)
	})
}
