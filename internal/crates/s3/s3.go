// Package s3 provides functionality for interacting with AWS S3 for file storage and retrieval,
// with features like presigned URLs, caching, and resolution-specific image access.
package s3

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/samber/lo"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/mr"
)

// ResolutionType represents different image resolution types for S3 assets
type ResolutionType string

const (
	// Original represents the original image resolution
	Original ResolutionType = "original"
	// High represents high-quality resolution
	High ResolutionType = "high"
	// Normal represents standard resolution
	Normal ResolutionType = "normal"
	// Low represents low-quality resolution
	Low ResolutionType = "low"
)

var resolutionOrder = map[ResolutionType][]string{
	Original: {"original"},
	High:     {"large.webp", "original"},
	Normal:   {"medium.webp", "large.webp", "original"},
	Low:      {"small.webp", "medium.webp", "large.webp", "original"},
}

// S3 defines the interface for interacting with S3 storage
//
//go:generate mockgen -source=s3.go -destination=s3mock/mocks3.go -package=s3mock
type S3 interface {
	// ParseURLToS3Key converts a URL to an S3 object key
	ParseURLToS3Key(URL string) (string, error)
	// GetPresignedURL generates a presigned URL for the given S3 object with the specified resolution
	GetPresignedURL(ctx context.Context, URL string, resolution ResolutionType) (string, error)
	// BatchGetPresignedURL generates presigned URLs for multiple S3 objects with the specified resolution
	// Returns a map where keys are original URLs and values are presigned URLs
	// If generation fails for a URL, its value will be an empty string
	BatchGetPresignedURL(ctx context.Context, URLs []string, resolution ResolutionType) (map[string]string, error)
	// Download downloads an S3 object to a local file
	Download(ctx context.Context, key, fileName string) error
	// Upload uploads a file to S3 and returns a presigned URL to access it
	// The URL domain will be replaced with the CloudFront domain if configured
	Upload(ctx context.Context, fileName, key string) (string, error)
}

type s3Impl struct {
	cfg        Config
	cache      Cache
	s3Client   s3ClientAPI
	presignCli s3PresignClientAPI
}

// Config holds configuration for S3 service connection and behavior
type Config struct {
	Region           string
	BucketName       string
	CloudFrontDomain string
	Expires          time.Duration
}

// MustNewS3 creates a new S3 client or panics if initialization fails
// If cache is provided, some operations will use it to improve performance
func MustNewS3(cfg Config, cache Cache) S3 {
	awsCfg, err := config.LoadDefaultConfig(context.Background(), config.WithRegion(cfg.Region))
	if err != nil {
		panic(fmt.Sprintf("failed to load aws config: %v", err))
	}

	if cache == nil {
		cache = &noCache{}
	}

	s3Client := s3.NewFromConfig(awsCfg, func(o *s3.Options) {
		o.DisableLogOutputChecksumValidationSkipped = true
	})
	return &s3Impl{
		cfg:   cfg,
		cache: cache,
		// 创建一个普通 s3 客户端
		s3Client: s3Client,
		// 创建一个预签名客户端
		presignCli: s3.NewPresignClient(s3Client),
	}
}

func (s *s3Impl) ParseURLToS3Key(URL string) (string, error) {
	// Parse URL for http/https
	u, err := url.Parse(URL)
	if err != nil {
		return "", fmt.Errorf("failed to parse image URL: %w", err)
	}

	// Extract path component as S3 key
	if u.Scheme == "http" || u.Scheme == "https" || u.Scheme == "s3" {
		// Remove leading slash
		return strings.TrimPrefix(u.Path, "/"), nil
	}

	return "", fmt.Errorf("unsupported URL scheme: %s", u.Scheme)
}

func (s *s3Impl) getResolutionS3Key(ctx context.Context, key string, resolution ResolutionType) (string, error) {
	if _, ok := resolutionOrder[resolution]; !ok {
		return "", errors.New("invalid resolution type")
	}

	keyWithoutExt := strings.TrimSuffix(key, filepath.Ext(key))

	for _, res := range resolutionOrder[resolution] {
		versionKey := keyWithoutExt + "." + res
		if res == "original" {
			versionKey = key
		}

		_, err := s.s3Client.HeadObject(ctx, &s3.HeadObjectInput{
			Bucket: aws.String(s.cfg.BucketName),
			Key:    aws.String(versionKey),
		})

		if err != nil {
			var notFound *types.NotFound
			if errors.As(err, &notFound) {
				continue
			}
			return "", err
		}

		// 如果找到了，则返回对应的分辨率的 key
		return versionKey, nil
	}

	// 如果都没有找到，则返回原始 key
	return key, nil
}

func (s *s3Impl) generatePresignedURL(ctx context.Context, key string) (string, error) {
	req, err := s.presignCli.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(s.cfg.BucketName),
		Key:    aws.String(key),
	}, s3.WithPresignExpires(s.cfg.Expires))
	if err != nil {
		return "", fmt.Errorf("failed to Presign GetObject: %w", err)
	}

	return req.URL, nil
}

func (s *s3Impl) coverToCloudFrontURL(URL string) string {
	if s.cfg.CloudFrontDomain != "" {
		return strings.Replace(URL, s.cfg.BucketName+".s3."+s.cfg.Region+".amazonaws.com", s.cfg.CloudFrontDomain, 1)
	}
	return URL
}

func (s *s3Impl) getPresignedURL(ctx context.Context, resolution ResolutionType, key string) (string, error) {
	// 获取对应的分辨率的 key
	resolutionKey, err := s.getResolutionS3Key(ctx, key, resolution)
	if err != nil {
		return "", err
	}

	// 生成带有过期时间的预签名URL
	presignedURL, err := s.generatePresignedURL(ctx, resolutionKey)
	if err != nil {
		return "", err
	}

	// 如果 CloudFrontDomain 不为空，则将 presignedURL 替换为 CloudFrontDomain
	presignedURL = s.coverToCloudFrontURL(presignedURL)
	return presignedURL, nil
}

func (s *s3Impl) GetPresignedURL(ctx context.Context, URL string, resolution ResolutionType) (string, error) {
	if resolutionOrder[resolution] == nil {
		return "", errors.New("invalid resolution type")
	}

	// 解析 URL 为 s3 的 key
	key, err := s.ParseURLToS3Key(URL)
	if err != nil {
		return "", err
	}

	// 从缓存中获取预签名 URL
	cacheKey, err := s.cache.Get(ctx, string(resolution), key)
	if err != nil {
		return "", err
	}
	if cacheKey != "" {
		return cacheKey, nil
	}
	logc.Infof(ctx, "get image presigned url from cache miss")

	presignedURL, err := s.getPresignedURL(ctx, resolution, key)
	if err != nil {
		return "", err
	}

	// 缓存预签名 URL
	err = s.cache.Set(ctx, string(resolution), key, presignedURL, s.cfg.Expires)
	if err != nil {
		logc.Errorf(ctx, "failed to set cache: %v", err)
	}

	return presignedURL, nil
}

func (s *s3Impl) BatchGetPresignedURL(ctx context.Context,
	URLs []string, resolution ResolutionType) (map[string]string, error) {
	if resolutionOrder[resolution] == nil {
		return nil, errors.New("invalid resolution type")
	}
	URLs = lo.Filter(URLs, func(item string, _ int) bool {
		return item != ""
	})
	if len(URLs) == 0 {
		return map[string]string{}, nil
	}

	keys := lo.Map(URLs, func(item string, _ int) string {
		key, err := s.ParseURLToS3Key(item)
		if err != nil {
			logc.Errorf(ctx, "failed to parse image url to s3 key: %v, url: %s", err, item)
			return ""
		}
		return key
	})
	keysToImageURL := lo.SliceToMap(lo.Zip2(keys, URLs), func(item lo.Tuple2[string, string]) (string, string) {
		return item.A, item.B
	})

	// 获取缓存中的预签名 URL
	cacheKeys, err := s.cache.MGet(ctx, string(resolution), keys...)
	if err != nil {
		return nil, err
	}
	results := make(map[string]string)
	missKeys := make([]string, 0)
	for k, v := range cacheKeys {
		if v != "" {
			results[keysToImageURL[k]] = v
		} else {
			missKeys = append(missKeys, k)
		}
	}
	// 如果缓存中没有缺失的 key，则直接返回
	if len(missKeys) == 0 {
		return results, nil
	}

	// 使用 map-reduce 并发获取 presignedURL
	presignedURLs, err := mr.MapReduce(
		func(source chan<- string) {
			for _, key := range missKeys {
				source <- key
			}
		},
		func(key string, writer mr.Writer[lo.Tuple2[string, string]], _ func(error)) {
			presignedURL, err := s.getPresignedURL(ctx, resolution, key)
			if err != nil {
				logc.Errorf(ctx, "failed to get presigned url: %v", err)
				// 不返回错误，仅输出日志，presignedURL 为空即可
			}
			writer.Write(lo.Tuple2[string, string]{A: key, B: presignedURL})
		},
		func(pipe <-chan lo.Tuple2[string, string], writer mr.Writer[map[string]string], _ func(error)) {
			presignedURLs := make(map[string]string)
			for t := range pipe {
				presignedURLs[t.A] = t.B
			}
			writer.Write(presignedURLs)
		},
		mr.WithContext(ctx),
	)
	if err != nil {
		return nil, err
	}

	// 过滤掉空值
	// 缓存预签名 URL
	presignedURLs = lo.PickBy(presignedURLs, func(_ string, value string) bool {
		return value != ""
	})
	err = s.cache.MSet(ctx, string(resolution), presignedURLs, s.cfg.Expires)
	if err != nil {
		logc.Errorf(ctx, "failed to set cache: %v", err)
	}

	// 合并结果
	for k, v := range presignedURLs {
		results[keysToImageURL[k]] = v
	}

	return results, nil
}

// Download 下载 s3 的文件
func (s *s3Impl) Download(ctx context.Context, key, fileName string) error {
	file, err := os.Create(fileName)
	if err != nil {
		return err
	}
	defer file.Close()

	result, err := s.s3Client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(s.cfg.BucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		return err
	}
	defer result.Body.Close()

	_, err = io.Copy(file, result.Body)
	return err
}

// Upload 上传文件到 s3，并返回带签名的 url，域名将被替换为 CloudFront 域名
func (s *s3Impl) Upload(ctx context.Context, fileName, key string) (string, error) {
	file, err := os.Open(fileName)
	if err != nil {
		return "", err
	}
	defer file.Close()

	_, err = s.s3Client.PutObject(ctx, &s3.PutObjectInput{
		Bucket: aws.String(s.cfg.BucketName),
		Key:    aws.String(key),
		Body:   file,
	})
	if err != nil {
		return "", err
	}

	presignedURL, err := s.generatePresignedURL(ctx, key)
	if err != nil {
		return "", err
	}

	return s.coverToCloudFrontURL(presignedURL), nil
}
