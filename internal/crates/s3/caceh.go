package s3

import (
	"context"
	"time"
)

// Cache 缓存接口，此处为接口定义出，具体实现由外部提供
//
//go:generate mockgen -source=caceh.go -destination=internal/mocks/cache.go -package=mocks
type Cache interface {
	Get(ctx context.Context, resolution, key string) (string, error)
	MGet(ctx context.Context, resolution string, keys ...string) (map[string]string, error)
	Set(ctx context.Context, resolution, key string, value string, expiration time.Duration) error
	MSet(ctx context.Context, resolution string, values map[string]string, expiration time.Duration) error
}

type noCache struct{}

func (c *noCache) Get(_ context.Context, _, _ string) (string, error) {
	return "", nil
}

func (c *noCache) MGet(_ context.Context, _ string, _ ...string) (map[string]string, error) {
	return nil, nil
}

func (c *noCache) Set(_ context.Context, _, _ string, _ string, _ time.Duration) error {
	return nil
}

func (c *noCache) MSet(_ context.Context, _ string, _ map[string]string, _ time.Duration) error {
	return nil
}
