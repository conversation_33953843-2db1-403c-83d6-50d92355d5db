// Code generated by MockGen. DO NOT EDIT.
// Source: s3.go
//
// Generated by this command:
//
//	mockgen -source=s3.go -destination=s3mock/mocks3.go -package=s3mock
//

// Package s3mock is a generated GoMock package.
package s3mock

import (
	context "context"
	reflect "reflect"

	s3 "github.com/sekai-app/sekai-go/internal/crates/s3"
	gomock "go.uber.org/mock/gomock"
)

// MockS3 is a mock of S3 interface.
type MockS3 struct {
	ctrl     *gomock.Controller
	recorder *MockS3MockRecorder
	isgomock struct{}
}

// MockS3MockRecorder is the mock recorder for MockS3.
type MockS3MockRecorder struct {
	mock *MockS3
}

// NewMockS3 creates a new mock instance.
func NewMockS3(ctrl *gomock.Controller) *MockS3 {
	mock := &MockS3{ctrl: ctrl}
	mock.recorder = &MockS3MockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockS3) EXPECT() *MockS3MockRecorder {
	return m.recorder
}

// BatchGetPresignedURL mocks base method.
func (m *MockS3) BatchGetPresignedURL(ctx context.Context, URLs []string, resolution s3.ResolutionType) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPresignedURL", ctx, URLs, resolution)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPresignedURL indicates an expected call of BatchGetPresignedURL.
func (mr *MockS3MockRecorder) BatchGetPresignedURL(ctx, URLs, resolution any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPresignedURL", reflect.TypeOf((*MockS3)(nil).BatchGetPresignedURL), ctx, URLs, resolution)
}

// Download mocks base method.
func (m *MockS3) Download(ctx context.Context, key, fileName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Download", ctx, key, fileName)
	ret0, _ := ret[0].(error)
	return ret0
}

// Download indicates an expected call of Download.
func (mr *MockS3MockRecorder) Download(ctx, key, fileName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Download", reflect.TypeOf((*MockS3)(nil).Download), ctx, key, fileName)
}

// GetPresignedURL mocks base method.
func (m *MockS3) GetPresignedURL(ctx context.Context, URL string, resolution s3.ResolutionType) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresignedURL", ctx, URL, resolution)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresignedURL indicates an expected call of GetPresignedURL.
func (mr *MockS3MockRecorder) GetPresignedURL(ctx, URL, resolution any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresignedURL", reflect.TypeOf((*MockS3)(nil).GetPresignedURL), ctx, URL, resolution)
}

// ParseURLToS3Key mocks base method.
func (m *MockS3) ParseURLToS3Key(URL string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseURLToS3Key", URL)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseURLToS3Key indicates an expected call of ParseURLToS3Key.
func (mr *MockS3MockRecorder) ParseURLToS3Key(URL any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseURLToS3Key", reflect.TypeOf((*MockS3)(nil).ParseURLToS3Key), URL)
}

// Upload mocks base method.
func (m *MockS3) Upload(ctx context.Context, fileName, key string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upload", ctx, fileName, key)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upload indicates an expected call of Upload.
func (mr *MockS3MockRecorder) Upload(ctx, fileName, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upload", reflect.TypeOf((*MockS3)(nil).Upload), ctx, fileName, key)
}
