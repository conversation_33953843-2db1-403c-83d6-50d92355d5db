package s3

import (
	"context"
	"errors"
	"io"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/sekai-app/sekai-go/internal/crates/s3/internal/mocks"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestParseURLToS3Key(t *testing.T) {
	tests := []struct {
		name    string
		url     string
		want    string
		wantErr bool
	}{
		{
			name:    "valid http url",
			url:     "http://example.com/path/to/image.jpg",
			want:    "path/to/image.jpg",
			wantErr: false,
		},
		{
			name:    "valid https url",
			url:     "https://example.com/path/to/image.jpg",
			want:    "path/to/image.jpg",
			wantErr: false,
		},
		{
			name:    "valid s3 url",
			url:     "s3://bucket/path/to/image.jpg",
			want:    "path/to/image.jpg",
			wantErr: false,
		},
		{
			name:    "invalid url scheme",
			url:     "ftp://example.com/path/to/image.jpg",
			want:    "",
			wantErr: true,
		},
		{
			name:    "invalid url format",
			url:     "not-a-url",
			want:    "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a new s3Impl instance with empty config
			s := &s3Impl{cfg: Config{}}

			got, err := s.ParseURLToS3Key(tt.url)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func TestGetResolutionS3Key(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockS3Client := mocks.NewMocks3ClientAPI(ctrl)
	s := &s3Impl{
		cfg: Config{
			BucketName: "test-bucket",
		},
		s3Client: mockS3Client,
	}

	ctx := context.Background()

	tests := []struct {
		name          string
		key           string
		resolution    ResolutionType
		mockSetup     func()
		expectedKey   string
		expectedError bool
	}{
		{
			name:          "invalid resolution type",
			key:           "images/test.jpg",
			resolution:    "invalid",
			mockSetup:     func() {},
			expectedError: true,
		},
		{
			name:       "original resolution exists",
			key:        "images/test.jpg",
			resolution: Original,
			mockSetup: func() {
				mockS3Client.EXPECT().
					HeadObject(gomock.Any(), &s3.HeadObjectInput{
						Bucket: aws.String("test-bucket"),
						Key:    aws.String("images/test.jpg"),
					}).
					Return(&s3.HeadObjectOutput{}, nil)
			},
			expectedKey:   "images/test.jpg",
			expectedError: false,
		},
		{
			name:       "high resolution - large.webp exists",
			key:        "images/test.jpg",
			resolution: High,
			mockSetup: func() {
				mockS3Client.EXPECT().
					HeadObject(gomock.Any(), &s3.HeadObjectInput{
						Bucket: aws.String("test-bucket"),
						Key:    aws.String("images/test.large.webp"),
					}).
					Return(&s3.HeadObjectOutput{}, nil)
			},
			expectedKey:   "images/test.large.webp",
			expectedError: false,
		},
		{
			name:       "high resolution - large.webp not found, original exists",
			key:        "images/test.jpg",
			resolution: High,
			mockSetup: func() {
				mockS3Client.EXPECT().
					HeadObject(gomock.Any(), &s3.HeadObjectInput{
						Bucket: aws.String("test-bucket"),
						Key:    aws.String("images/test.large.webp"),
					}).
					Return(nil, &types.NotFound{})

				mockS3Client.EXPECT().
					HeadObject(gomock.Any(), &s3.HeadObjectInput{
						Bucket: aws.String("test-bucket"),
						Key:    aws.String("images/test.jpg"),
					}).
					Return(&s3.HeadObjectOutput{}, nil)
			},
			expectedKey:   "images/test.jpg",
			expectedError: false,
		},
		{
			name:       "error during HeadObject",
			key:        "images/test.jpg",
			resolution: High,
			mockSetup: func() {
				mockS3Client.EXPECT().
					HeadObject(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("unexpected error"))
			},
			expectedError: true,
		},
		{
			name:       "all resolutions not found, fallback to original",
			key:        "images/test.jpg",
			resolution: Low,
			mockSetup: func() {
				// small.webp not found
				mockS3Client.EXPECT().
					HeadObject(gomock.Any(), &s3.HeadObjectInput{
						Bucket: aws.String("test-bucket"),
						Key:    aws.String("images/test.small.webp"),
					}).
					Return(nil, &types.NotFound{})

				// medium.webp not found
				mockS3Client.EXPECT().
					HeadObject(gomock.Any(), &s3.HeadObjectInput{
						Bucket: aws.String("test-bucket"),
						Key:    aws.String("images/test.medium.webp"),
					}).
					Return(nil, &types.NotFound{})

				// large.webp not found
				mockS3Client.EXPECT().
					HeadObject(gomock.Any(), &s3.HeadObjectInput{
						Bucket: aws.String("test-bucket"),
						Key:    aws.String("images/test.large.webp"),
					}).
					Return(nil, &types.NotFound{})

				// original not found (but we return the key anyway)
				mockS3Client.EXPECT().
					HeadObject(gomock.Any(), &s3.HeadObjectInput{
						Bucket: aws.String("test-bucket"),
						Key:    aws.String("images/test.jpg"),
					}).
					Return(nil, &types.NotFound{})
			},
			expectedKey:   "images/test.jpg",
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSetup()

			key, err := s.getResolutionS3Key(ctx, tt.key, tt.resolution)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedKey, key)
			}
		})
	}
}

func TestGeneratePresignedURL(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockPresignClient := mocks.NewMocks3PresignClientAPI(ctrl)

	s := &s3Impl{
		cfg: Config{
			BucketName: "test-bucket",
			Expires:    time.Hour,
		},
		presignCli: mockPresignClient,
	}

	ctx := context.Background()
	key := "images/test.jpg"
	expectedURL := "https://test-bucket.s3.us-west-2.amazonaws.com/images/test.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."

	mockPresignClient.EXPECT().
		PresignGetObject(
			gomock.Any(),
			&s3.GetObjectInput{
				Bucket: aws.String("test-bucket"),
				Key:    aws.String(key),
			},
			gomock.Any(),
		).
		Return(&v4.PresignedHTTPRequest{URL: expectedURL}, nil)

	url, err := s.generatePresignedURL(ctx, key)
	assert.NoError(t, err)
	assert.Equal(t, expectedURL, url)

	// Test error case
	mockPresignClient.EXPECT().
		PresignGetObject(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).
		Return(nil, errors.New("presign error"))

	_, err = s.generatePresignedURL(ctx, key)
	assert.Error(t, err)
}

func TestCoverToCloudFrontURL(t *testing.T) {
	tests := []struct {
		name             string
		url              string
		cloudFrontDomain string
		expected         string
	}{
		{
			name:             "with CloudFront domain",
			url:              "https://test-bucket.s3.us-west-2.amazonaws.com/images/test.jpg",
			cloudFrontDomain: "cdn.example.com",
			expected:         "https://cdn.example.com/images/test.jpg",
		},
		{
			name:             "without CloudFront domain",
			url:              "https://test-bucket.s3.us-west-2.amazonaws.com/images/test.jpg",
			cloudFrontDomain: "",
			expected:         "https://test-bucket.s3.us-west-2.amazonaws.com/images/test.jpg",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &s3Impl{
				cfg: Config{
					BucketName:       "test-bucket",
					Region:           "us-west-2",
					CloudFrontDomain: tt.cloudFrontDomain,
				},
			}

			result := s.coverToCloudFrontURL(tt.url)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetPresignedURL(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockS3Client := mocks.NewMocks3ClientAPI(ctrl)
	mockPresignClient := mocks.NewMocks3PresignClientAPI(ctrl)
	mockCache := mocks.NewMockCache(ctrl)

	// 创建一个自定义的s3Impl结构体，覆盖ParseURLToS3Key方法
	s := &testS3Impl{
		s3Impl: s3Impl{
			cfg: Config{
				BucketName: "test-bucket",
				Region:     "us-west-2",
				Expires:    time.Hour,
			},
			s3Client:   mockS3Client,
			presignCli: mockPresignClient,
			cache:      mockCache,
		},
		parseResult: "images/test.jpg", // 预定义解析结果
	}

	ctx := context.Background()
	url := "https://example.com/images/test.jpg"
	key := "images/test.jpg"
	resolution := High
	expectedURL := "https://test-bucket.s3.us-west-2.amazonaws.com/images/test.large.webp?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."

	// Test cache hit
	t.Run("cache hit", func(t *testing.T) {
		mockCache.EXPECT().
			Get(ctx, string(resolution), key).
			Return(expectedURL, nil)

		// 测试GetPresignedURL方法，而不是内部的getPresignedURL
		result, err := s.GetPresignedURL(ctx, url, resolution)
		assert.NoError(t, err)
		assert.Equal(t, expectedURL, result)
	})

	// Test cache miss
	t.Run("cache miss", func(t *testing.T) {
		mockCache.EXPECT().
			Get(ctx, string(resolution), key).
			Return("", nil)

		// First try large.webp and find it
		mockS3Client.EXPECT().
			HeadObject(gomock.Any(), &s3.HeadObjectInput{
				Bucket: aws.String("test-bucket"),
				Key:    aws.String("images/test.large.webp"),
			}).
			Return(&s3.HeadObjectOutput{}, nil)

		mockPresignClient.EXPECT().
			PresignGetObject(
				gomock.Any(),
				&s3.GetObjectInput{
					Bucket: aws.String("test-bucket"),
					Key:    aws.String("images/test.large.webp"),
				},
				gomock.Any(),
			).
			Return(&v4.PresignedHTTPRequest{URL: expectedURL}, nil)

		mockCache.EXPECT().
			Set(ctx, string(resolution), key, expectedURL, time.Hour).
			Return(nil)

		result, err := s.GetPresignedURL(ctx, url, resolution)
		assert.NoError(t, err)
		assert.Equal(t, expectedURL, result)
	})
}

// testS3Impl 是一个用于测试的s3Impl自定义实现
type testS3Impl struct {
	s3Impl
	parseResult string
}

// ParseURLToS3Key 覆盖原方法，返回预定义的结果
func (s *testS3Impl) ParseURLToS3Key(_ string) (string, error) {
	return s.parseResult, nil
}

func TestBatchGetPresignedURL(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockS3Client := mocks.NewMocks3ClientAPI(ctrl)
	mockPresignClient := mocks.NewMocks3PresignClientAPI(ctrl)
	mockCache := mocks.NewMockCache(ctrl)

	s := &s3Impl{
		cfg: Config{
			BucketName: "test-bucket",
			Region:     "us-west-2",
			Expires:    time.Hour,
		},
		s3Client:   mockS3Client,
		presignCli: mockPresignClient,
		cache:      mockCache,
	}

	ctx := context.Background()
	urls := []string{
		"https://example.com/images/test1.jpg",
		"https://example.com/images/test2.jpg",
	}
	keys := []string{
		"images/test1.jpg",
		"images/test2.jpg",
	}
	resolution := High

	// Test invalid resolution
	t.Run("invalid resolution", func(t *testing.T) {
		_, err := s.BatchGetPresignedURL(ctx, urls, "invalid")
		assert.Error(t, err)
	})

	// Test cache hit for all keys
	t.Run("all cache hits", func(t *testing.T) {
		cacheResults := map[string]string{
			"images/test1.jpg": "https://test-bucket.s3.us-west-2.amazonaws.com/images/test1.jpg?signed",
			"images/test2.jpg": "https://test-bucket.s3.us-west-2.amazonaws.com/images/test2.jpg?signed",
		}
		expectedResults := map[string]string{
			"https://example.com/images/test1.jpg": "https://test-bucket.s3.us-west-2.amazonaws.com/images/test1.jpg?signed",
			"https://example.com/images/test2.jpg": "https://test-bucket.s3.us-west-2.amazonaws.com/images/test2.jpg?signed",
		}

		mockCache.EXPECT().
			MGet(ctx, string(resolution), keys[0], keys[1]).
			Return(cacheResults, nil)

		results, err := s.BatchGetPresignedURL(ctx, urls, resolution)
		assert.NoError(t, err)
		assert.Equal(t, expectedResults, results)
	})

	// Test cache miss for some keys
	t.Run("some cache misses", func(t *testing.T) {
		cacheResults := map[string]string{
			"images/test1.jpg": "https://test-bucket.s3.us-west-2.amazonaws.com/images/test1.jpg?signed",
			"images/test2.jpg": "", // Cache miss
		}

		mockCache.EXPECT().
			MGet(ctx, string(resolution), keys[0], keys[1]).
			Return(cacheResults, nil)

		// Test2 key resolution to large.webp
		mockS3Client.EXPECT().
			HeadObject(gomock.Any(), &s3.HeadObjectInput{
				Bucket: aws.String("test-bucket"),
				Key:    aws.String("images/test2.large.webp"),
			}).
			Return(&s3.HeadObjectOutput{}, nil)

		// Presign for test2 large.webp
		mockPresignClient.EXPECT().
			PresignGetObject(
				gomock.Any(),
				&s3.GetObjectInput{
					Bucket: aws.String("test-bucket"),
					Key:    aws.String("images/test2.large.webp"),
				},
				gomock.Any(),
			).
			Return(&v4.PresignedHTTPRequest{URL: "https://test-bucket.s3.us-west-2.amazonaws.com/images/test2.large.webp?signed"}, nil)

		// Cache set for the missing key
		mockCache.EXPECT().
			MSet(
				ctx,
				string(resolution),
				map[string]string{"images/test2.jpg": "https://test-bucket.s3.us-west-2.amazonaws.com/images/test2.large.webp?signed"},
				time.Hour,
			).
			Return(nil)

		expectedResults := map[string]string{
			"https://example.com/images/test1.jpg": "https://test-bucket.s3.us-west-2.amazonaws.com/images/test1.jpg?signed",
			"https://example.com/images/test2.jpg": "https://test-bucket.s3.us-west-2.amazonaws.com/images/test2.large.webp?signed",
		}

		results, err := s.BatchGetPresignedURL(ctx, urls, resolution)
		assert.NoError(t, err)
		assert.Equal(t, expectedResults, results)
	})
}

func TestDownload(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockS3Client := mocks.NewMocks3ClientAPI(ctrl)
	s := &s3Impl{
		cfg: Config{
			BucketName: "test-bucket",
		},
		s3Client: mockS3Client,
	}

	ctx := context.Background()
	key := "images/test.jpg"
	fileName := "testfile.jpg"

	// Create a temporary file for testing
	tempFile, err := os.CreateTemp("", "s3_test_download_")
	assert.NoError(t, err)
	tempFileName := tempFile.Name()
	tempFile.Close()
	defer os.Remove(tempFileName)

	// Define a reader for mock response
	mockBody := io.NopCloser(strings.NewReader("test file content"))

	mockS3Client.EXPECT().
		GetObject(
			gomock.Any(),
			&s3.GetObjectInput{
				Bucket: aws.String("test-bucket"),
				Key:    aws.String(key),
			},
			gomock.Any(),
		).
		Return(&s3.GetObjectOutput{
			Body: mockBody,
		}, nil)

	err = s.Download(ctx, key, tempFileName)
	assert.NoError(t, err)

	// Verify the file content
	content, err := os.ReadFile(tempFileName)
	assert.NoError(t, err)
	assert.Equal(t, "test file content", string(content))

	// Test error cases
	t.Run("GetObject error", func(t *testing.T) {
		mockS3Client.EXPECT().
			GetObject(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, errors.New("GetObject error"))

		err = s.Download(ctx, key, fileName)
		assert.Error(t, err)
	})
}

func TestUpload(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockS3Client := mocks.NewMocks3ClientAPI(ctrl)
	mockPresignClient := mocks.NewMocks3PresignClientAPI(ctrl)

	s := &s3Impl{
		cfg: Config{
			BucketName:       "test-bucket",
			Region:           "us-west-2",
			CloudFrontDomain: "cdn.example.com",
		},
		s3Client:   mockS3Client,
		presignCli: mockPresignClient,
	}

	ctx := context.Background()
	key := "images/test.jpg"

	// Create a temporary file for testing
	tempFile, err := os.CreateTemp("", "s3_test_upload_")
	assert.NoError(t, err)
	_, err = tempFile.WriteString("test file content")
	assert.NoError(t, err)
	tempFile.Close()
	tempFileName := tempFile.Name()
	defer os.Remove(tempFileName)

	// Mock PutObject
	mockS3Client.EXPECT().
		PutObject(
			gomock.Any(),
			gomock.Any(), // We can't match the file content directly in the test
			gomock.Any(),
		).
		Return(&s3.PutObjectOutput{}, nil)

	// Mock PresignGetObject
	presignedURL := "https://test-bucket.s3.us-west-2.amazonaws.com/images/test.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256"
	mockPresignClient.EXPECT().
		PresignGetObject(
			gomock.Any(),
			&s3.GetObjectInput{
				Bucket: aws.String("test-bucket"),
				Key:    aws.String(key),
			},
			gomock.Any(),
		).
		Return(&v4.PresignedHTTPRequest{URL: presignedURL}, nil)

	// Expected CloudFront URL
	expectedURL := "https://cdn.example.com/images/test.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256"

	url, err := s.Upload(ctx, tempFileName, key)
	assert.NoError(t, err)
	assert.Equal(t, expectedURL, url)

	// Test error cases
	t.Run("file open error", func(t *testing.T) {
		_, err := s.Upload(ctx, "non-existent-file.jpg", key)
		assert.Error(t, err)
	})

	t.Run("PutObject error", func(t *testing.T) {
		mockS3Client.EXPECT().
			PutObject(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, errors.New("PutObject error"))

		_, err := s.Upload(ctx, tempFileName, key)
		assert.Error(t, err)
	})

	t.Run("PresignGetObject error", func(t *testing.T) {
		mockS3Client.EXPECT().
			PutObject(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&s3.PutObjectOutput{}, nil)

		mockPresignClient.EXPECT().
			PresignGetObject(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, errors.New("PresignGetObject error"))

		_, err := s.Upload(ctx, tempFileName, key)
		assert.Error(t, err)
	})
}
