package s3

import (
	"context"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

// 此文件用于定义此包用到的 aws s3 的接口，方便生成 mock 代码进行单元测试，执行下面注释命令可自动生成 mock 代码
//go:generate mockgen -source=s3clientiface.go -destination=internal/mocks/s3clientiface.go -package=mocks

// s3ClientAPI is a subset of the s3.Client interface for unit testing
type s3ClientAPI interface {
	GetObject(ctx context.Context, params *s3.GetObjectInput, optFns ...func(*s3.Options)) (*s3.GetObjectOutput, error)
	HeadObject(ctx context.Context, params *s3.HeadObjectInput, optFns ...func(*s3.Options)) (*s3.HeadObjectOutput, error)
	PutObject(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error)
}

var _ s3ClientAPI = &s3.Client{}

// s3PresignClientAPI is a subset of the s3.PresignClient interface for unit testing
type s3PresignClientAPI interface {
	PresignGetObject(ctx context.Context,
		params *s3.GetObjectInput, optFns ...func(*s3.PresignOptions)) (*v4.PresignedHTTPRequest, error)
}

var _ s3PresignClientAPI = &s3.PresignClient{}
