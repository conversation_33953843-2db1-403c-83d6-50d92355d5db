// Code generated by MockGen. DO NOT EDIT.
// Source: s3clientiface.go
//
// Generated by this command:
//
//	mockgen -source=s3clientiface.go -destination=internal/mocks/s3clientiface.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	s3 "github.com/aws/aws-sdk-go-v2/service/s3"
	gomock "go.uber.org/mock/gomock"
)

// Mocks3ClientAPI is a mock of s3ClientAPI interface.
type Mocks3ClientAPI struct {
	ctrl     *gomock.Controller
	recorder *Mocks3ClientAPIMockRecorder
	isgomock struct{}
}

// Mocks3ClientAPIMockRecorder is the mock recorder for Mocks3ClientAPI.
type Mocks3ClientAPIMockRecorder struct {
	mock *Mocks3ClientAPI
}

// NewMocks3ClientAPI creates a new mock instance.
func NewMocks3ClientAPI(ctrl *gomock.Controller) *Mocks3ClientAPI {
	mock := &Mocks3ClientAPI{ctrl: ctrl}
	mock.recorder = &Mocks3ClientAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *Mocks3ClientAPI) EXPECT() *Mocks3ClientAPIMockRecorder {
	return m.recorder
}

// GetObject mocks base method.
func (m *Mocks3ClientAPI) GetObject(ctx context.Context, params *s3.GetObjectInput, optFns ...func(*s3.Options)) (*s3.GetObjectOutput, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, params}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetObject", varargs...)
	ret0, _ := ret[0].(*s3.GetObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObject indicates an expected call of GetObject.
func (mr *Mocks3ClientAPIMockRecorder) GetObject(ctx, params any, optFns ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, params}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObject", reflect.TypeOf((*Mocks3ClientAPI)(nil).GetObject), varargs...)
}

// HeadObject mocks base method.
func (m *Mocks3ClientAPI) HeadObject(ctx context.Context, params *s3.HeadObjectInput, optFns ...func(*s3.Options)) (*s3.HeadObjectOutput, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, params}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HeadObject", varargs...)
	ret0, _ := ret[0].(*s3.HeadObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HeadObject indicates an expected call of HeadObject.
func (mr *Mocks3ClientAPIMockRecorder) HeadObject(ctx, params any, optFns ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, params}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HeadObject", reflect.TypeOf((*Mocks3ClientAPI)(nil).HeadObject), varargs...)
}

// PutObject mocks base method.
func (m *Mocks3ClientAPI) PutObject(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, params}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutObject", varargs...)
	ret0, _ := ret[0].(*s3.PutObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutObject indicates an expected call of PutObject.
func (mr *Mocks3ClientAPIMockRecorder) PutObject(ctx, params any, optFns ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, params}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObject", reflect.TypeOf((*Mocks3ClientAPI)(nil).PutObject), varargs...)
}

// Mocks3PresignClientAPI is a mock of s3PresignClientAPI interface.
type Mocks3PresignClientAPI struct {
	ctrl     *gomock.Controller
	recorder *Mocks3PresignClientAPIMockRecorder
	isgomock struct{}
}

// Mocks3PresignClientAPIMockRecorder is the mock recorder for Mocks3PresignClientAPI.
type Mocks3PresignClientAPIMockRecorder struct {
	mock *Mocks3PresignClientAPI
}

// NewMocks3PresignClientAPI creates a new mock instance.
func NewMocks3PresignClientAPI(ctrl *gomock.Controller) *Mocks3PresignClientAPI {
	mock := &Mocks3PresignClientAPI{ctrl: ctrl}
	mock.recorder = &Mocks3PresignClientAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *Mocks3PresignClientAPI) EXPECT() *Mocks3PresignClientAPIMockRecorder {
	return m.recorder
}

// PresignGetObject mocks base method.
func (m *Mocks3PresignClientAPI) PresignGetObject(ctx context.Context, params *s3.GetObjectInput, optFns ...func(*s3.PresignOptions)) (*v4.PresignedHTTPRequest, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, params}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PresignGetObject", varargs...)
	ret0, _ := ret[0].(*v4.PresignedHTTPRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PresignGetObject indicates an expected call of PresignGetObject.
func (mr *Mocks3PresignClientAPIMockRecorder) PresignGetObject(ctx, params any, optFns ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, params}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PresignGetObject", reflect.TypeOf((*Mocks3PresignClientAPI)(nil).PresignGetObject), varargs...)
}
