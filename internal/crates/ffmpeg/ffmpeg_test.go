package ffmpeg

import (
	"math"
	"path/filepath"
	"testing"
)

const (
	testAudioFile = "sound of silence.mp3"
)

func getTestFilePath() string {
	return filepath.Join("testdata", testAudioFile)
}

func TestFFmpegGetAudioRMS(t *testing.T) {
	ffmpeg := MustNewFFmpeg()
	audioPath := getTestFilePath()

	rms, err := ffmpeg.GetAudioRMS(audioPath)
	if err != nil {
		t.Fatalf("GetAudioRMS failed: %v", err)
	}

	if rms <= 0 {
		t.<PERSON>rrorf("Expected RMS to be > 0, got %f", rms)
	}

	t.Logf("Audio RMS value: %f", rms)
}

func TestFFmpegGetAudioDuration(t *testing.T) {
	ffmpeg := MustNewFFmpeg()
	audioPath := getTestFilePath()

	duration, err := ffmpeg.GetAudioDuration(audioPath)
	if err != nil {
		t.Fatalf("GetAudioDuration failed: %v", err)
	}

	if duration <= 0 {
		t.<PERSON><PERSON><PERSON>("Expected duration to be > 0, got %f", duration)
	}

	t.Logf("Audio duration: %f seconds", duration)
}

func TestFFmpegNormalizeAudio(t *testing.T) {
	ffmpeg := MustNewFFmpeg()
	inputPath := getTestFilePath()
	outputPath := filepath.Join(t.TempDir(), "normalized_"+testAudioFile)

	// First get the original RMS
	originalRMS, err := ffmpeg.GetAudioRMS(inputPath)
	if err != nil {
		t.Fatalf("Failed to get original RMS: %v", err)
	}

	// Apply a volume change of +3dB (approximately doubles the volume)
	volumeChangeDB := 3.0
	err = ffmpeg.NormalizeAudio(inputPath, outputPath, volumeChangeDB)
	if err != nil {
		t.Fatalf("NormalizeAudio failed: %v", err)
	}

	// Get the new RMS
	newRMS, err := ffmpeg.GetAudioRMS(outputPath)
	if err != nil {
		t.Fatalf("Failed to get new RMS: %v", err)
	}

	// A +3dB change should approximately double the amplitude
	expectedRatio := math.Pow(10, volumeChangeDB/20)
	actualRatio := newRMS / originalRMS

	// Allow for some variance in the result (±20%)
	if actualRatio < expectedRatio*0.8 || actualRatio > expectedRatio*1.2 {
		t.Errorf("Expected volume ratio around %f, got %f", expectedRatio, actualRatio)
	}

	t.Logf("Original RMS: %f, New RMS: %f, Ratio: %f (expected ~%f)",
		originalRMS, newRMS, actualRatio, expectedRatio)
}
