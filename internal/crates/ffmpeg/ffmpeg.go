// ffmpeg 封装了业务上对 ffmpeg 的操作
package ffmpeg

import (
	"fmt"
	"math"
	"os/exec"
	"strconv"
	"strings"
)

// FFmpeg 定义了业务上对 ffmpeg 的操作接口
type FFmpeg interface {
	// GetAudioRMS 获取音频的均方根音量(RMS)值
	GetAudioRMS(fileName string) (float64, error)
	// GetAudioDuration 获取音频的时长
	GetAudioDuration(fileName string) (float64, error)
	// NormalizeAudio 标准化音频的音量
	NormalizeAudio(inputFile, outputFile string, volumeChange float64) error
}

type ffmpegImpl struct {
}

// MustNewFFmpeg 创建一个 ffmpeg 接口实例
func MustNewFFmpeg() FFmpeg {
	// 检查ffmpeg是否安装
	cmd := exec.Command("ffmpeg", "-version")
	if err := cmd.Run(); err != nil {
		panic("ffmpeg is not installed or not in PATH: " + err.Error())
	}

	return &ffmpegImpl{}
}

// GetAudioRMS 获取音频的均方根音量(RMS)值
func (f *ffmpegImpl) GetAudioRMS(fileName string) (float64, error) {
	// 使用ffmpeg的volumedetect过滤器分析音频
	cmd := exec.Command("ffmpeg", "-i", fileName, "-filter:a", "volumedetect", "-f", "null", "/dev/null")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return 0, fmt.Errorf("ffmpeg analysis failed: %w, output: %s", err, output)
	}

	// 将输出转换为字符串并按行分割
	outputStr := string(output)
	lines := strings.Split(outputStr, "\n")

	// 查找包含"mean_volume"或"rms"的行
	var rmsValue float64
	found := false

	for _, line := range lines {
		// 查找均方根音量(RMS)值
		if strings.Contains(line, "RMS level") || strings.Contains(line, "mean_volume") {
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				// 提取数值部分
				valueStr := strings.TrimSpace(parts[1])
				// 移除"dB"后缀
				valueStr = strings.Replace(valueStr, "dB", "", -1)
				valueStr = strings.TrimSpace(valueStr)

				// 解析为浮点数
				dBValue, err := strconv.ParseFloat(valueStr, 64)
				if err != nil {
					return 0, fmt.Errorf("failed to parse RMS value '%s': %w", valueStr, err)
				}

				// 将dB值转换为线性振幅
				// RMS振幅 = 10^(dB/20)
				rmsValue = math.Pow(10, dBValue/20) * 1000 // 缩放到与Python代码相似的范围
				found = true
				break
			}
		}
	}

	if !found {
		return 0, fmt.Errorf("could not find RMS level in ffmpeg output: %s", outputStr)
	}

	return rmsValue, nil
}

// GetAudioDuration 获取音频的时长
func (f *ffmpegImpl) GetAudioDuration(fileName string) (float64, error) {
	// 使用ffprobe获取音频时长
	cmd := exec.Command("ffprobe",
		"-v", "error",
		"-show_entries", "format=duration",
		"-of", "default=noprint_wrappers=1:nokey=1",
		fileName)

	output, err := cmd.Output()
	if err != nil {
		return 0, fmt.Errorf("ffprobe failed: %w", err)
	}

	// 移除任何空白字符并解析为浮点数
	durationStr := strings.TrimSpace(string(output))
	duration, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse duration '%s': %w", durationStr, err)
	}

	return duration, nil
}

// NormalizeAudio 标准化音频的音量
func (f *ffmpegImpl) NormalizeAudio(inputFile, outputFile string, volumeChange float64) error {
	cmd := exec.Command("ffmpeg", "-i", inputFile, "-filter:a",
		fmt.Sprintf("volume=%fdB", volumeChange), outputFile)
	return cmd.Run()
}
