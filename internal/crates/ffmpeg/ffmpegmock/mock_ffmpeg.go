// Code generated by MockGen. DO NOT EDIT.
// Source: internal/crates/ffmpeg/ffmpeg.go

// Package ffmpegmock is a generated GoMock package.
package ffmpegmock

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockFFmpeg is a mock of FFmpeg interface.
type MockFFmpeg struct {
	ctrl     *gomock.Controller
	recorder *MockFFmpegMockRecorder
}

// MockFFmpegMockRecorder is the mock recorder for MockFFmpeg.
type MockFFmpegMockRecorder struct {
	mock *MockFFmpeg
}

// NewMockFFmpeg creates a new mock instance.
func NewMockFFmpeg(ctrl *gomock.Controller) *MockFFmpeg {
	mock := &MockFFmpeg{ctrl: ctrl}
	mock.recorder = &MockFFmpegMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFFmpeg) EXPECT() *MockFFmpegMockRecorder {
	return m.recorder
}

// GetAudioDuration mocks base method.
func (m *MockFFmpeg) GetAudioDuration(fileName string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAudioDuration", fileName)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAudioDuration indicates an expected call of GetAudioDuration.
func (mr *MockFFmpegMockRecorder) GetAudioDuration(fileName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAudioDuration", reflect.TypeOf((*MockFFmpeg)(nil).GetAudioDuration), fileName)
}

// GetAudioRMS mocks base method.
func (m *MockFFmpeg) GetAudioRMS(fileName string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAudioRMS", fileName)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAudioRMS indicates an expected call of GetAudioRMS.
func (mr *MockFFmpegMockRecorder) GetAudioRMS(fileName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAudioRMS", reflect.TypeOf((*MockFFmpeg)(nil).GetAudioRMS), fileName)
}

// NormalizeAudio mocks base method.
func (m *MockFFmpeg) NormalizeAudio(inputFile, outputFile string, volumeChange float64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NormalizeAudio", inputFile, outputFile, volumeChange)
	ret0, _ := ret[0].(error)
	return ret0
}

// NormalizeAudio indicates an expected call of NormalizeAudio.
func (mr *MockFFmpegMockRecorder) NormalizeAudio(inputFile, outputFile, volumeChange interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NormalizeAudio", reflect.TypeOf((*MockFFmpeg)(nil).NormalizeAudio), inputFile, outputFile, volumeChange)
}
