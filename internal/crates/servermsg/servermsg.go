// Package servermsg provides functionality for extracting and accessing request context information,
// such as user details, device information, and request metadata.
package servermsg

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"

	"github.com/zeromicro/go-zero/rest/httpx"
)

type serverMsgContextKeyType string

const (
	// ServerMsgContextKey is the key used to store ServerMsg in the request context
	ServerMsgContextKey serverMsgContextKeyType = "sekai-go/internal/utils/servermsg"
)

const (
	userIDContextKey   string = "user_id"
	userNameContextKey string = "user_name"
)

// ServerMsg contains common information extracted from requests,
// including business fields from headers, login information, etc.
type ServerMsg struct {
	CurrentUser CurrentUser // Current user information
	AppVersion  string      // Client version
	DeviceID    string      // Device ID
	RequestID   string      // Client request ID
	RemoteAddr  string      // Client IP:port
	UserAgent   string      // Client user agent
}

// CurrentUser represents the currently authenticated user's information
type CurrentUser struct {
	UserID   int
	UserName string
}

// Middleware extracts common information from requests and stores it in the context
type Middleware struct {
}

// NewMiddleware creates a new servermsg Middleware
func NewMiddleware() *Middleware {
	return &Middleware{}
}

// Handle is the HTTP middleware handler that extracts and stores request information
func (s *Middleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var userID int64
		v, ok := r.Context().Value(userIDContextKey).(json.Number)
		if ok {
			userID, _ = v.Int64()
		}
		userName, _ := r.Context().Value(userNameContextKey).(string)

		remoteAddr := ""
		s := strings.Split(httpx.GetRemoteAddr(r), ",")
		if len(s) > 0 {
			remoteAddr = strings.TrimSpace(s[0])
		}

		serverMsg := &ServerMsg{
			CurrentUser: CurrentUser{
				UserID:   int(userID),
				UserName: userName,
			},
			RequestID:  r.Header.Get("requestid"), // 客户端请求ID,没有使用http规范：X-Request-ID
			RemoteAddr: remoteAddr,
			UserAgent:  r.UserAgent(),
			AppVersion: r.Header.Get("app-version"), // 客户端版本
			DeviceID:   r.Header.Get("device_id"),   // 设备ID
		}

		ctx := context.WithValue(r.Context(), ServerMsgContextKey, serverMsg)
		next(w, r.WithContext(ctx))
	}
}

// Get retrieves the ServerMsg from the context
// Returns a default ServerMsg if none exists in the context
func Get(ctx context.Context) *ServerMsg {
	v, ok := ctx.Value(ServerMsgContextKey).(*ServerMsg)
	if !ok {
		return &ServerMsg{}
	}
	return v
}

// GetCurrentUser retrieves the current user information from the context
func GetCurrentUser(ctx context.Context) *CurrentUser {
	return &Get(ctx).CurrentUser
}
