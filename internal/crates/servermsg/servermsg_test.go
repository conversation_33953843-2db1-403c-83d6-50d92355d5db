package servermsg

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGet(t *testing.T) {
	tests := []struct {
		name           string
		setupCtx       func() context.Context
		expectedUserID int
		expectedName   string
	}{
		{
			name: "with servermsg in context",
			setupCtx: func() context.Context {
				serverMsg := &ServerMsg{
					CurrentUser: CurrentUser{
						UserID:   123,
						UserName: "testuser",
					},
					AppVersion: "1.0.0",
					DeviceID:   "device-123",
					RequestID:  "req-123",
					RemoteAddr: "127.0.0.1:8080",
					UserAgent:  "Test Agent",
				}
				return context.WithValue(context.Background(), ServerMsgContextKey, serverMsg)
			},
			expectedUserID: 123,
			expectedName:   "testuser",
		},
		{
			name: "without servermsg in context",
			setupCtx: func() context.Context {
				return context.Background()
			},
			expectedUserID: 0,
			expectedName:   "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := tt.setupCtx()
			msg := Get(ctx)

			assert.NotNil(t, msg)
			assert.Equal(t, tt.expectedUserID, msg.CurrentUser.UserID)
			assert.Equal(t, tt.expectedName, msg.CurrentUser.UserName)
		})
	}
}

func TestGetCurrentUser(t *testing.T) {
	tests := []struct {
		name           string
		setupCtx       func() context.Context
		expectedUserID int
		expectedName   string
	}{
		{
			name: "with servermsg in context",
			setupCtx: func() context.Context {
				serverMsg := &ServerMsg{
					CurrentUser: CurrentUser{
						UserID:   456,
						UserName: "johndoe",
					},
				}
				return context.WithValue(context.Background(), ServerMsgContextKey, serverMsg)
			},
			expectedUserID: 456,
			expectedName:   "johndoe",
		},
		{
			name: "without servermsg in context",
			setupCtx: func() context.Context {
				return context.Background()
			},
			expectedUserID: 0,
			expectedName:   "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := tt.setupCtx()
			user := GetCurrentUser(ctx)

			assert.NotNil(t, user)
			assert.Equal(t, tt.expectedUserID, user.UserID)
			assert.Equal(t, tt.expectedName, user.UserName)
		})
	}
}

func TestMiddleware_Handle(t *testing.T) {
	tests := []struct {
		name          string
		requestSetup  func(*http.Request)
		expectedUser  CurrentUser
		expectedExtra map[string]string
	}{
		{
			name: "with user context and headers",
			requestSetup: func(r *http.Request) {
				// Use string keys directly instead of custom context key types
				ctx := context.WithValue(r.Context(), userIDContextKey, json.Number("789"))
				ctx = context.WithValue(ctx, userNameContextKey, "alice")
				*r = *r.WithContext(ctx)
				r.Header.Set("requestid", "req-789")
				r.Header.Set("app-version", "2.0.0")
				r.Header.Set("device_id", "device-789")
				r.Header.Set("User-Agent", "Test Browser")
			},
			expectedUser: CurrentUser{
				UserID:   789,
				UserName: "alice",
			},
			expectedExtra: map[string]string{
				"RequestID":  "req-789",
				"AppVersion": "2.0.0",
				"DeviceID":   "device-789",
				"UserAgent":  "Test Browser",
			},
		},
		{
			name: "without user context",
			requestSetup: func(r *http.Request) {
				// No context values set
				r.Header.Set("requestid", "req-000")
			},
			expectedUser: CurrentUser{
				UserID:   0,
				UserName: "",
			},
			expectedExtra: map[string]string{
				"RequestID":  "req-000",
				"AppVersion": "",
				"DeviceID":   "",
				"UserAgent":  "",
			},
		},
		{
			name: "with invalid user_id",
			requestSetup: func(r *http.Request) {
				// Use string key directly
				ctx := context.WithValue(r.Context(), userIDContextKey, "not-a-number")
				*r = *r.WithContext(ctx)
			},
			expectedUser: CurrentUser{
				UserID:   0,
				UserName: "",
			},
			expectedExtra: map[string]string{
				"RequestID":  "",
				"AppVersion": "",
				"DeviceID":   "",
				"UserAgent":  "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			middleware := NewMiddleware()
			var capturedServerMsg *ServerMsg

			// Create a test handler that will capture the ServerMsg from context
			nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				capturedServerMsg = Get(r.Context())
				w.WriteHeader(http.StatusOK)
			})

			// Create test request
			req := httptest.NewRequest("GET", "http://example.com/foo", nil)
			if tt.requestSetup != nil {
				tt.requestSetup(req)
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute middleware
			handler := middleware.Handle(nextHandler)
			handler.ServeHTTP(rr, req)

			// Assert results
			assert.NotNil(t, capturedServerMsg)
			assert.Equal(t, tt.expectedUser.UserID, capturedServerMsg.CurrentUser.UserID)
			assert.Equal(t, tt.expectedUser.UserName, capturedServerMsg.CurrentUser.UserName)

			if tt.expectedExtra != nil {
				if v, ok := tt.expectedExtra["RequestID"]; ok {
					assert.Equal(t, v, capturedServerMsg.RequestID)
				}
				if v, ok := tt.expectedExtra["AppVersion"]; ok {
					assert.Equal(t, v, capturedServerMsg.AppVersion)
				}
				if v, ok := tt.expectedExtra["DeviceID"]; ok {
					assert.Equal(t, v, capturedServerMsg.DeviceID)
				}
				if v, ok := tt.expectedExtra["UserAgent"]; ok {
					assert.Equal(t, v, capturedServerMsg.UserAgent)
				}
			}
		})
	}
}

func TestNewMiddleware(t *testing.T) {
	middleware := NewMiddleware()
	assert.NotNil(t, middleware)
	assert.IsType(t, &Middleware{}, middleware)
}
