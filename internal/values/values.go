// Package values provides constants used throughout the application,
// including error codes that follow a structured format.
package values

// Error code convention:
// - Format: XXX_YYY where XXX is the HTTP status code and YYY is the specific error code
// - Example: 400_000 returns HTTP status 400 with code 400_000 in the response body
// - First 3 digits determine the HTTP status code returned to the client
const (
	// ErrorCodeSuccess represents a successful operation with code 0
	ErrorCodeSuccess = 0

	// ErrorCodeBadRequest represents a generic bad request error
	ErrorCodeBadRequest = 400_000

	// ErrorCodeForbidden represents a forbidden access error
	ErrorCodeForbidden = 403_000

	// ErrorCodeNotFound represents a generic resource not found error
	ErrorCodeNotFound = 404_000
	// ErrorCodeNotFoundS3 represents a resource not found in S3 storage
	ErrorCodeNotFoundS3 = 404_001

	// ErrorCodeInternalServerError represents a generic internal server error
	ErrorCodeInternalServerError = 500_000
	// ErrorCodeInternalDBError represents a database-related internal error
	ErrorCodeInternalDBError = 500_001
	// ErrorCodeInternalS3Error represents an S3 storage-related internal error
	ErrorCodeInternalS3Error = 500_002
	// ErrorCodeInternalDynamoError represents a DynamoDB-related internal error
	ErrorCodeInternalDynamoError = 500_003
)
