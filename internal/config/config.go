// Package config defines the configuration structures for the application,
// including server settings, database connections, and service integrations.
package config

import (
	"context"
	"fmt"
	"time"

	"github.com/amirsalarsafaei/sqlc-pgx-monitoring/dbtracer"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/opensearch"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
	"go.opentelemetry.io/otel/exporters/prometheus"
	"go.opentelemetry.io/otel/sdk/metric"
)

// Config represents the root configuration structure for the application
type Config struct {
	Server     rest.RestConf
	Auth       AuthConfig
	DB         DBConfig
	Dynamo     dynamo.Config
	Redis      redis.RedisConf
	S3         s3.Config
	OpenSearch opensearch.Config
}

// AuthConfig holds authentication settings for JWT tokens
type AuthConfig struct {
	AccessSecret  string
	AccessExpire  time.Duration
	RefreshSecret string
	RefreshExpire time.Duration
}

// DBConfig contains PostgreSQL database connection settings
type DBConfig struct {
	Host        string
	Port        int
	User        string
	Password    string
	DBName      string
	SSLMode     string
	MaxConns    int32         // Maximum number of connections
	MinConns    int32         // Minimum number of connections
	MaxIdleTime time.Duration // Maximum idle time for connections
	MaxLifetime time.Duration // Maximum lifetime of connections
	HealthCheck bool          // Whether to enable health checks
}

// GetPool creates and returns a PostgreSQL connection pool based on the configuration
func (c *DBConfig) GetPool(ctx context.Context) (*pgxpool.Pool, error) {
	// 构建连接字符串
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.User, c.Password, c.DBName, c.SSLMode)

	// 创建连接池配置
	config, err := pgxpool.ParseConfig(dsn)
	if err != nil {
		return nil, fmt.Errorf("unable to parse config: %v", err)
	}

	// 设置连接池参数
	config.MaxConns = c.MaxConns
	config.MinConns = c.MinConns
	config.MaxConnIdleTime = c.MaxIdleTime
	config.MaxConnLifetime = c.MaxLifetime
	config.HealthCheckPeriod = 1 * time.Minute // 如果启用健康检查

	exporter, err := prometheus.New()
	if err != nil {
		return nil, fmt.Errorf("unable to create prometheus exporter: %v", err)
	}

	provider := metric.NewMeterProvider(
		metric.WithReader(exporter),
		metric.WithView(metric.NewView(
			metric.Instrument{Name: "*"},
			metric.Stream{
				Aggregation: metric.AggregationExplicitBucketHistogram{
					// 单位是 s
					Boundaries: []float64{0.001, 0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.5, 1, 2, 5, 10, 25},
				},
			},
		)),
	)

	config.ConnConfig.Tracer, err = dbtracer.NewDBTracer("postgresql",
		dbtracer.WithShouldLog(func(err error) bool {
			return err != nil
		}),
		dbtracer.WithMeterProvider(provider),
	)
	if err != nil {
		return nil, fmt.Errorf("unable to create db tracer: %v", err)
	}

	// 创建连接池
	pool, err := pgxpool.NewWithConfig(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("unable to create connection pool: %v", err)
	}

	// 测试连接
	if err := pool.Ping(ctx); err != nil {
		return nil, fmt.Errorf("unable to ping database: %v", err)
	}

	return pool, nil
}
