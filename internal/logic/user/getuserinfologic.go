package user

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserInfoLogic {
	return &GetUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserInfoLogic) GetUserInfo(req *types.UserInfoReq) (resp *types.UserInfoRsp, err error) {
	// todo: add your logic here and delete this line
	logc.Infof(l.ctx, "servermsg: %+v", servermsg.Get(l.ctx))

	user, err := l.svcCtx.Dynamo.GetUser(l.ctx, int(servermsg.Get(l.ctx).CurrentUser.UserID))
	if err != nil {
		return nil, err
	}

	return &types.UserInfoRsp{
		ResponseBase: types.ResponseBase{
			Code: 0,
			Msg:  "success",
		},
		Name: user.UserName,
	}, nil
}
