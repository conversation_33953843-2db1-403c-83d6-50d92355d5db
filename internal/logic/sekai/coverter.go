package sekai

import (
	"context"

	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/mr"

	"github.com/sekai-app/sekai-go/internal/crates/utils"
)

type coverter struct {
	svcCtx *svc.ServiceContext
}

func newCoverter(svcCtx *svc.ServiceContext) *coverter {
	return &coverter{
		svcCtx: svcCtx,
	}
}

// BatchToSekaiBaseInfo 批量转换sekai基础信息
func (c *coverter) BathToSekaiBaseInfo(ctx context.Context,
	sekaiInfos []*pg.AiuSekaiInfoTable) ([]*types.SekaiBaseInfo, error) {
	if len(sekaiInfos) == 0 {
		return []*types.SekaiBaseInfo{}, nil
	}

	data, err := c.getBatchData(ctx, sekaiInfos)
	if err != nil {
		return nil, err
	}

	return c.buildBatchSekaiBaseInfo(ctx, sekaiInfos, data), nil
}

type batchData struct {
	coverURLMap      map[string]string
	newCoverURLs     map[string]string
	backgroundURLMap map[string]string
	userMap          map[int]*dynamo.User
	viewCountMap     map[int]int
	labelMap         map[int]*pg.AiuLabelTable
}

func (c *coverter) getBatchData(ctx context.Context,
	sekaiBases []*pg.AiuSekaiInfoTable) (*batchData, error) {

	var (
		coverURLMap      map[string]string
		newCoverURLMap   map[string]string
		backgroundURLMap map[string]string
		userMap          map[int]*dynamo.User
		viewCountMap     map[int]int
		labelMap         map[int]*pg.AiuLabelTable
	)
	err := mr.Finish(
		func() error {
			var err error
			coverURLMap, err = c.getBatchCoverURLs(ctx, sekaiBases)
			if err != nil {
				return err
			}
			return nil
		},
		func() error {
			var err error
			newCoverURLMap, err = c.getBatchNewCoverURLs(ctx, sekaiBases)
			if err != nil {
				return err
			}
			return nil
		},
		func() error {
			var err error
			backgroundURLMap, err = c.getBatchBackgroundURLs(ctx, sekaiBases)
			if err != nil {
				return err
			}
			return nil
		},
		func() error {
			var err error
			userMap, err = c.getBatchUsers(ctx, sekaiBases)
			if err != nil {
				return err
			}
			return nil
		},
		func() error {
			var err error
			viewCountMap, err = c.getBatchViewCount(ctx, sekaiBases)
			if err != nil {
				return err
			}
			return nil
		},
		func() error {
			var err error
			labelMap, err = c.getBatchLabels(ctx, sekaiBases)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return nil, err
	}

	return &batchData{
		coverURLMap:      coverURLMap,
		newCoverURLs:     newCoverURLMap,
		backgroundURLMap: backgroundURLMap,
		userMap:          userMap,
		viewCountMap:     viewCountMap,
		labelMap:         labelMap,
	}, nil
}

func (c *coverter) getBatchCoverURLs(ctx context.Context,
	sekaiBases []*pg.AiuSekaiInfoTable) (map[string]string, error) {
	coverURLs := lo.Map(sekaiBases, func(sekaiBase *pg.AiuSekaiInfoTable, _ int) string {
		return sekaiBase.CoverUrl.String
	})
	coverURLMap, err := c.svcCtx.S3.BatchGetPresignedURL(ctx, coverURLs, s3.Original)
	if err != nil {
		logc.Errorf(ctx, "failed to get presigned URLs for cover: %v", err)
		return nil, err
	}
	return coverURLMap, nil
}

func (c *coverter) getBatchNewCoverURLs(ctx context.Context,
	sekaiBases []*pg.AiuSekaiInfoTable) (map[string]string, error) {
	newCoverURLs := lo.Map(sekaiBases, func(sekaiBase *pg.AiuSekaiInfoTable, _ int) string {
		return sekaiBase.CoverImage.String
	})
	newCoverURLMap, err := c.svcCtx.S3.BatchGetPresignedURL(ctx, newCoverURLs, s3.Original)
	if err != nil {
		logc.Errorf(ctx, "failed to get presigned URLs for new cover: %v", err)
		return nil, err
	}
	return newCoverURLMap, nil
}

func (c *coverter) getBatchBackgroundURLs(ctx context.Context,
	sekaiBases []*pg.AiuSekaiInfoTable) (map[string]string, error) {
	backgroundURLs := lo.Map(sekaiBases, func(sekaiBase *pg.AiuSekaiInfoTable, _ int) string {
		return sekaiBase.BackgroundImage.String
	})
	backgroundURLMap, err := c.svcCtx.S3.BatchGetPresignedURL(ctx, backgroundURLs, s3.Original)
	if err != nil {
		logc.Errorf(ctx, "failed to get presigned URLs for background: %v", err)
		return nil, err
	}
	return backgroundURLMap, nil
}

func (c *coverter) getBatchUsers(ctx context.Context,
	sekaiBases []*pg.AiuSekaiInfoTable) (map[int]*dynamo.User, error) {
	userIDs := lo.Map(sekaiBases, func(sekaiBase *pg.AiuSekaiInfoTable, _ int) int {
		return int(sekaiBase.CreatorUserID.Int64)
	})
	userIDs = lo.Uniq(userIDs)

	userMap, err := c.svcCtx.Dynamo.BatchGetUsers(ctx, userIDs)
	if err != nil {
		logc.Errorf(ctx, "failed to get users: %v", err)
		return nil, err
	}
	return userMap, nil
}

func (c *coverter) getBatchViewCount(ctx context.Context, sekaiBases []*pg.AiuSekaiInfoTable) (map[int]int, error) {
	viewCountMap := make(map[int]int)
	sekaiIDs := lo.Map(sekaiBases, func(sekaiBase *pg.AiuSekaiInfoTable, _ int) int64 {
		return int64(sekaiBase.SekaiID)
	})

	viewInfos, err := c.svcCtx.DB.GetSekaiViewCountByIDs(ctx, sekaiIDs)
	if err != nil {
		logc.Errorf(ctx, "failed to get view count: %v", err)
		return nil, err
	}
	for _, viewInfo := range viewInfos {
		viewCountMap[int(viewInfo.SekaiID)] = int(viewInfo.ViewCount)
	}

	return viewCountMap, nil
}

func (c *coverter) getBatchLabels(ctx context.Context,
	sekaiBases []*pg.AiuSekaiInfoTable) (map[int]*pg.AiuLabelTable, error) {

	labelMap := make(map[int]*pg.AiuLabelTable)

	var labelIDs []int32
	for _, sekaiBase := range sekaiBases {
		labelIDs = append(labelIDs, lo.Map(sekaiBase.UserCategory, func(category int64, _ int) int32 {
			return int32(category)
		})...)
		labelIDs = append(labelIDs, lo.Map(sekaiBase.AiCategory, func(category int64, _ int) int32 {
			return int32(category)
		})...)
	}
	labelIDs = lo.Uniq(labelIDs)

	labels, err := c.svcCtx.DB.GetSekaiCategoriesByLabelIDs(ctx, labelIDs)
	if err != nil {
		logc.Errorf(ctx, "failed to get labels: %v", err)
		return nil, err
	}
	for _, label := range labels {
		labelMap[int(label.ID)] = label
	}
	return labelMap, nil
}

func (c *coverter) buildBatchSekaiBaseInfo(ctx context.Context,
	sekaiBases []*pg.AiuSekaiInfoTable, data *batchData) []*types.SekaiBaseInfo {
	sekaiBaseInfos := make([]*types.SekaiBaseInfo, len(sekaiBases))
	for i, sekaiBase := range sekaiBases {
		coverURL := data.coverURLMap[sekaiBase.CoverUrl.String]
		newCoverURL := data.newCoverURLs[sekaiBase.CoverImage.String]
		backgroundURL := data.backgroundURLMap[sekaiBase.BackgroundImage.String]

		// 如果coverURL为空，则使用backgroundURL
		coverURL = utils.GetWithDefault(coverURL, backgroundURL)
		// 如果newCoverURL为空，则使用coverURL
		newCoverURL = utils.GetWithDefault(newCoverURL, coverURL)

		creatorUserName := utils.GetWithDefault(
			data.userMap[int(sekaiBase.CreatorUserID.Int64)], &dynamo.User{}).UserName
		sekaiBaseInfos[i] = &types.SekaiBaseInfo{
			SekaiID:         int(sekaiBase.SekaiID),
			Title:           sekaiBase.Title.String,
			CoverURL:        coverURL,
			NewCoverURL:     newCoverURL,
			CreatorUserID:   int(sekaiBase.CreatorUserID.Int64),
			CreatorUserName: creatorUserName,
			Public:          sekaiBase.IsPublic.Bool,
			Intro:           sekaiBase.Intro.String,
			BackgroundURL:   backgroundURL,
			Categories:      c.buildCategories(ctx, sekaiBase, data.labelMap),
			ViewCount:       data.viewCountMap[int(sekaiBase.SekaiID)],
			TemplateID:      int(sekaiBase.TemplateID.Int64),
			MinAppVersion:   int(sekaiBase.MinAppVersion.Int32),
		}
	}

	return sekaiBaseInfos
}

func (c *coverter) buildCategories(_ context.Context,
	sekaiBase *pg.AiuSekaiInfoTable, labelMap map[int]*pg.AiuLabelTable) []*types.LableInfo {

	if len(labelMap) == 0 {
		return []*types.LableInfo{}
	}

	categoryIDs := sekaiBase.UserCategory
	if len(categoryIDs) == 0 {
		categoryIDs = sekaiBase.AiCategory
	}

	categories := make([]*types.LableInfo, 0, len(categoryIDs))
	for _, categoryID := range categoryIDs {
		label := labelMap[int(categoryID)]
		categories = append(categories, &types.LableInfo{
			LableID:     int(label.ID),
			LabelName:   label.LabelName,
			DisplayName: label.DisplayName.String,
			Emoji:       label.Emoji,
		})
	}
	return categories
}
