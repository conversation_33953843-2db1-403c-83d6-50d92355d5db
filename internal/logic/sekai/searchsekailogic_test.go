package sekai

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/opensearch"
	"github.com/sekai-app/sekai-go/internal/db/opensearch/opensearchmock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupSearchSekaiTest(t *testing.T) (*gomock.Controller, *SearchSekaiLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3, *opensearchmock.MockOpenSearch) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)
	mockOpsearch := opensearchmock.NewMockOpenSearch(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:       mockDB,
		Dynamo:   mockDynamo,
		S3:       mockS3,
		Opsearch: mockOpsearch,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
		AppVersion: "1.0.0",
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewSearchSekaiLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3, mockOpsearch
}

func createSearchResult(totalValue int, sekaiIDs ...int) *opensearch.SearchResult[opensearch.Sekai] {
	result := &opensearch.SearchResult[opensearch.Sekai]{}
	result.Hits.Total.Value = totalValue

	for _, id := range sekaiIDs {
		result.Hits.Hits = append(result.Hits.Hits, opensearch.SearchHit[opensearch.Sekai]{
			Source: opensearch.Sekai{ID: id},
		})
	}

	return result
}

func TestSearchSekai(t *testing.T) {
	tests := []struct {
		name             string
		setupMocks       func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3, *opensearchmock.MockOpenSearch)
		request          *types.SearchSekaiReq
		expectedCode     int
		expectedErrMsg   string
		expectedItemsLen int
		expectedTotal    int
	}{
		{
			name: "successful search with results",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchSekaiByKeyword 的期望
				searchResult := createSearchResult(3, 1, 2, 3)

				mockOpsearch.EXPECT().
					SearchSekaiByKeyword(gomock.Any(), opensearch.SearchSekaiReq{
						Keyword:    "test",
						AppVersion: "1.0.0",
						UserID:     123,
						Page:       1,
						Size:       10,
					}).
					Return(searchResult, nil)

				// 设置 DB.GetSekaisByIDsWithOrder 的期望
				mockDB.EXPECT().
					GetSekaisByIDsWithOrder(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuSekaiInfoTable{
						{
							SekaiID:         1,
							Title:           pgtype.Text{String: "Sekai 1", Valid: true},
							CoverUrl:        pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
							CoverImage:      pgtype.Text{String: "http://example.com/newcover1.jpg", Valid: true},
							BackgroundImage: pgtype.Text{String: "http://example.com/bg1.jpg", Valid: true},
							CreatorUserID:   pgtype.Int8{Int64: 123, Valid: true},
							IsPublic:        pgtype.Bool{Bool: true, Valid: true},
							Intro:           pgtype.Text{String: "Intro 1", Valid: true},
							UserCategory:    []int64{1, 2},
							AiCategory:      []int64{},
							MinAppVersion:   pgtype.Int4{Int32: 100, Valid: true},
						},
						{
							SekaiID:       2,
							Title:         pgtype.Text{String: "Sekai 2", Valid: true},
							CoverUrl:      pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
							CreatorUserID: pgtype.Int8{Int64: 456, Valid: true},
							IsPublic:      pgtype.Bool{Bool: true, Valid: true},
							UserCategory:  []int64{3},
							MinAppVersion: pgtype.Int4{Int32: 100, Valid: true},
						},
					}, nil)

				// 设置 coverter 相关依赖的 mock
				// 设置 S3.BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/cover1.jpg":    "https://presigned.example.com/cover1.jpg",
						"http://example.com/newcover1.jpg": "https://presigned.example.com/newcover1.jpg",
						"http://example.com/bg1.jpg":       "https://presigned.example.com/bg1.jpg",
						"http://example.com/cover2.jpg":    "https://presigned.example.com/cover2.jpg",
					}, nil).
					AnyTimes()

				// 设置 Dynamo.BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "creator1"},
						456: {ID: 456, UserName: "creator2"},
					}, nil)

				// 设置 DB.GetSekaiViewCountByIDs 的期望
				mockDB.EXPECT().
					GetSekaiViewCountByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.GetSekaiViewCountByIDsRow{
						{SekaiID: 1, ViewCount: 100},
						{SekaiID: 2, ViewCount: 200},
					}, nil)

				// 设置 DB.GetSekaiCategoriesByLabelIDs 的期望
				mockDB.EXPECT().
					GetSekaiCategoriesByLabelIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuLabelTable{
						{ID: 1, LabelName: "Label 1", DisplayName: pgtype.Text{String: "Display 1", Valid: true}, Emoji: "🚀"},
						{ID: 2, LabelName: "Label 2", DisplayName: pgtype.Text{String: "Display 2", Valid: true}, Emoji: "🔥"},
						{ID: 3, LabelName: "Label 3", DisplayName: pgtype.Text{String: "Display 3", Valid: true}, Emoji: "✨"},
					}, nil)
			},
			request: &types.SearchSekaiReq{
				Keywords: "test",
				Page:     1,
				Size:     10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 2,
			expectedTotal:    3,
		},
		{
			name: "empty keyword",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, _ *opensearchmock.MockOpenSearch) {
				// 空关键词不会调用任何依赖
			},
			request: &types.SearchSekaiReq{
				Keywords: "",
				Page:     1,
				Size:     10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 0,
			expectedTotal:    0,
		},
		{
			name: "page beyond max results",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, _ *opensearchmock.MockOpenSearch) {
				// 超出最大结果数，不会调用任何依赖
			},
			request: &types.SearchSekaiReq{
				Keywords: "test",
				Page:     21,
				Size:     10, // 21 * 10 > 200 (maxSearchResultSize)
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 0,
			expectedTotal:    0,
		},
		{
			name: "opensearch error",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchSekaiByKeyword 返回错误
				mockOpsearch.EXPECT().
					SearchSekaiByKeyword(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(500, "opensearch error"))
			},
			request: &types.SearchSekaiReq{
				Keywords: "error",
				Page:     1,
				Size:     10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "search failed",
		},
		{
			name: "opensearch returns empty results",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchSekaiByKeyword 返回空结果
				searchResult := createSearchResult(0)

				mockOpsearch.EXPECT().
					SearchSekaiByKeyword(gomock.Any(), gomock.Any()).
					Return(searchResult, nil)
			},
			request: &types.SearchSekaiReq{
				Keywords: "nonexistent",
				Page:     1,
				Size:     10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 0,
			expectedTotal:    0,
		},
		{
			name: "database error",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchSekaiByKeyword 的期望
				searchResult := createSearchResult(1, 1)

				mockOpsearch.EXPECT().
					SearchSekaiByKeyword(gomock.Any(), gomock.Any()).
					Return(searchResult, nil)

				// 设置 DB.GetSekaisByIDsWithOrder 返回错误
				mockDB.EXPECT().
					GetSekaisByIDsWithOrder(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(500, "database error"))
			},
			request: &types.SearchSekaiReq{
				Keywords: "dberror",
				Page:     1,
				Size:     10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get sekais",
		},
		{
			name: "missing app version",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, _ *opensearchmock.MockOpenSearch) {
				// 不设置 mock，因为 buildSearchRequest 会直接返回错误
			},
			request: &types.SearchSekaiReq{
				Keywords: "test",
				Page:     1,
				Size:     10,
			},
			expectedCode:   values.ErrorCodeBadRequest,
			expectedErrMsg: "app version is empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3, mockOpsearch := setupSearchSekaiTest(t)
			defer ctrl.Finish()

			// 处理特殊情况：测试缺少 app 版本
			if tt.name == "missing app version" {
				// 创建一个没有 AppVersion 的上下文
				serverMsg := &servermsg.ServerMsg{
					CurrentUser: servermsg.CurrentUser{
						UserID:   123,
						UserName: "testuser",
					},
					AppVersion: "", // 空 AppVersion
				}
				ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)
				logic = NewSearchSekaiLogic(ctx, logic.svcCtx)
			}

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3, mockOpsearch)
			}

			// 执行测试
			resp, err := logic.SearchSekai(tt.request)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证分页信息
				assert.Equal(t, tt.expectedTotal, resp.Data.Total)
				assert.Equal(t, tt.request.Page, resp.Data.Page)
				assert.Equal(t, tt.request.Size, resp.Data.Size)

				// 验证返回的 sekai 列表长度
				assert.Equal(t, tt.expectedItemsLen, len(resp.Data.Items))
			}
		})
	}
}

func TestBuildEmptyResponse(t *testing.T) {
	ctrl, logic, _, _, _, _ := setupSearchSekaiTest(t)
	defer ctrl.Finish()

	resp := logic.buildEmptyResponse(1, 10, 0)

	assert.Equal(t, values.ErrorCodeSuccess, resp.Code)
	assert.Equal(t, "success", resp.Msg)
	assert.Equal(t, 0, resp.Data.Total)
	assert.Equal(t, 1, resp.Data.Page)
	assert.Equal(t, 10, resp.Data.Size)
	assert.Equal(t, 0, len(resp.Data.Items))
}

func TestBuildSearchRequest(t *testing.T) {
	ctrl, logic, _, _, _, _ := setupSearchSekaiTest(t)
	defer ctrl.Finish()

	req := &types.SearchSekaiReq{
		Keywords: "test",
		Page:     2,
		Size:     20,
	}

	searchReq, err := logic.buildSearchRequest(req)

	assert.NoError(t, err)
	assert.Equal(t, "test", searchReq.Keyword)
	assert.Equal(t, "1.0.0", searchReq.AppVersion)
	assert.Equal(t, 123, searchReq.UserID)
	assert.Equal(t, 2, searchReq.Page)
	assert.Equal(t, 20, searchReq.Size)
}

func TestExecuteSearch(t *testing.T) {
	ctrl, logic, _, _, _, mockOpsearch := setupSearchSekaiTest(t)
	defer ctrl.Finish()

	searchReq := opensearch.SearchSekaiReq{
		Keyword:    "test",
		AppVersion: "1.0.0",
		UserID:     123,
		Page:       1,
		Size:       10,
	}

	searchResult := createSearchResult(1, 1)

	mockOpsearch.EXPECT().
		SearchSekaiByKeyword(gomock.Any(), searchReq).
		Return(searchResult, nil)

	result, err := logic.executeSearch(searchReq)

	assert.NoError(t, err)
	assert.Equal(t, 1, result.Hits.Total.Value)
	assert.Equal(t, 1, len(result.Hits.Hits))
	assert.Equal(t, 1, result.Hits.Hits[0].Source.ID)
}

func TestFetchSekaiDetails(t *testing.T) {
	ctrl, logic, mockDB, _, _, _ := setupSearchSekaiTest(t)
	defer ctrl.Finish()

	sekaiIDs := []int64{1, 2}

	mockDB.EXPECT().
		GetSekaisByIDsWithOrder(gomock.Any(), &pg.GetSekaisByIDsWithOrderParams{
			SekaiIds: sekaiIDs,
		}).
		Return([]*pg.AiuSekaiInfoTable{
			{SekaiID: 1, Title: pgtype.Text{String: "Sekai 1", Valid: true}},
			{SekaiID: 2, Title: pgtype.Text{String: "Sekai 2", Valid: true}},
		}, nil)

	result, err := logic.fetchSekaiDetails(sekaiIDs)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))
	assert.Equal(t, int64(1), result[0].SekaiID)
	assert.Equal(t, "Sekai 1", result[0].Title.String)
	assert.Equal(t, int64(2), result[1].SekaiID)
	assert.Equal(t, "Sekai 2", result[1].Title.String)
}
