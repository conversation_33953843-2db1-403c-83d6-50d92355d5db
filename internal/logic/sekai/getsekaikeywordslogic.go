package sekai

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

// KeywordEntity represents a keyword entity in OpenSearch
type KeywordEntity struct {
	KeywordName string `json:"keywordName"`
	KeywordType string `json:"keywordType"`
	UseCount    int    `json:"useCount"`
}

type GetSekaiKeywordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetSekaiKeywordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSekaiKeywordsLogic {
	return &GetSekaiKeywordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSekaiKeywordsLogic) GetSekaiKeywords(req *types.GetSekaiKeywordsReq) (*types.GetSekaiKeywordsResp, error) {
	resp := &types.GetSekaiKeywordsResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.SekaiKeywordsData{
			Items: []string{},
		},
	}

	// 查询大批量关键词（50个），允许客户端分页和排序
	querySize := 50
	keywords, _, err := l.svcCtx.Opsearch.SearchSekaiKeywords(l.ctx, req.Prefix, querySize)
	if err != nil {
		logc.Errorf(l.ctx, "OpenSearch search error: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "Failed to search keywords")
	}

	// 应用分页
	total := len(keywords)
	resp.Data.Pagination = utils.GetPageInfo(total, req.Page, req.Size)

	start := (req.Page - 1) * req.Size
	if start >= total {
		// 如果起始索引超出总数，返回空结果
		return resp, nil
	}

	end := start + req.Size
	resp.Data.Items = keywords[start:min(end, total)]
	return resp, nil
}
