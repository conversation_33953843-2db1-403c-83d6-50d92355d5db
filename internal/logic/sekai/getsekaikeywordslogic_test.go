package sekai

import (
	"context"
	"testing"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/opensearch/opensearchmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupGetSekaiKeywordsTest(t *testing.T) (*gomock.Controller, *GetSekaiKeywordsLogic, *opensearchmock.MockOpenSearch) {
	ctrl := gomock.NewController(t)

	mockOpsearch := opensearchmock.NewMockOpenSearch(ctrl)

	svcCtx := &svc.ServiceContext{
		Opsearch: mockOpsearch,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewGetSekaiKeywordsLogic(ctx, svcCtx)

	return ctrl, logic, mockOpsearch
}

func TestGetSekaiKeywords(t *testing.T) {
	tests := []struct {
		name             string
		setupMocks       func(*opensearchmock.MockOpenSearch)
		request          *types.GetSekaiKeywordsReq
		expectedCode     int
		expectedErrMsg   string
		expectedItemsLen int
		expectedTotal    int
	}{
		{
			name: "successful search with results",
			setupMocks: func(mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchSekaiKeywords 的期望
				mockOpsearch.EXPECT().
					SearchSekaiKeywords(gomock.Any(), "test", 50).
					Return([]string{"test1", "test2", "test3"}, 3, nil)
			},
			request: &types.GetSekaiKeywordsReq{
				Prefix: "test",
				Page:   1,
				Size:   10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 3,
			expectedTotal:    3,
		},
		{
			name: "successful search with no results",
			setupMocks: func(mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchSekaiKeywords 的期望返回空结果
				mockOpsearch.EXPECT().
					SearchSekaiKeywords(gomock.Any(), "nonexistent", 50).
					Return([]string{}, 0, nil)
			},
			request: &types.GetSekaiKeywordsReq{
				Prefix: "nonexistent",
				Page:   1,
				Size:   10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 0,
			expectedTotal:    0,
		},
		{
			name: "pagination test - page 1",
			setupMocks: func(mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchSekaiKeywords 的期望返回多个结果
				keywords := []string{
					"keyword1", "keyword2", "keyword3", "keyword4", "keyword5",
				}
				mockOpsearch.EXPECT().
					SearchSekaiKeywords(gomock.Any(), "key", 50).
					Return(keywords, 5, nil)
			},
			request: &types.GetSekaiKeywordsReq{
				Prefix: "key",
				Page:   1,
				Size:   2, // 每页2个，共5个结果
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 2, // 第一页应该有2个
			expectedTotal:    5, // 总共5个结果
		},
		{
			name: "pagination test - page 2",
			setupMocks: func(mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchSekaiKeywords 的期望返回多个结果
				keywords := []string{
					"keyword1", "keyword2", "keyword3", "keyword4", "keyword5",
				}
				mockOpsearch.EXPECT().
					SearchSekaiKeywords(gomock.Any(), "key", 50).
					Return(keywords, 5, nil)
			},
			request: &types.GetSekaiKeywordsReq{
				Prefix: "key",
				Page:   2,
				Size:   2, // 每页2个，共5个结果
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 2, // 第二页应该有2个
			expectedTotal:    5, // 总共5个结果
		},
		{
			name: "pagination test - last page",
			setupMocks: func(mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchSekaiKeywords 的期望返回多个结果
				keywords := []string{
					"keyword1", "keyword2", "keyword3", "keyword4", "keyword5",
				}
				mockOpsearch.EXPECT().
					SearchSekaiKeywords(gomock.Any(), "key", 50).
					Return(keywords, 5, nil)
			},
			request: &types.GetSekaiKeywordsReq{
				Prefix: "key",
				Page:   3,
				Size:   2, // 每页2个，共5个结果
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 1, // 第三页应该只有1个
			expectedTotal:    5, // 总共5个结果
		},
		{
			name: "pagination test - out of range",
			setupMocks: func(mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchSekaiKeywords 的期望返回多个结果
				keywords := []string{
					"keyword1", "keyword2", "keyword3", "keyword4", "keyword5",
				}
				mockOpsearch.EXPECT().
					SearchSekaiKeywords(gomock.Any(), "key", 50).
					Return(keywords, 5, nil)
			},
			request: &types.GetSekaiKeywordsReq{
				Prefix: "key",
				Page:   4, // 超出范围的页码
				Size:   2,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 0, // 应该返回空结果
			expectedTotal:    5, // 总共5个结果
		},
		{
			name: "opensearch error",
			setupMocks: func(mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchSekaiKeywords 的期望返回错误
				mockOpsearch.EXPECT().
					SearchSekaiKeywords(gomock.Any(), "error", 50).
					Return(nil, 0, xerrors.New(500, "opensearch error"))
			},
			request: &types.GetSekaiKeywordsReq{
				Prefix: "error",
				Page:   1,
				Size:   10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "Failed to search keywords",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockOpsearch := setupGetSekaiKeywordsTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockOpsearch)
			}

			// 执行测试
			resp, err := logic.GetSekaiKeywords(tt.request)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证分页信息
				assert.Equal(t, tt.expectedTotal, resp.Data.Total)
				assert.Equal(t, tt.request.Page, resp.Data.Page)
				assert.Equal(t, tt.request.Size, resp.Data.Size)

				// 验证返回的关键词列表长度
				assert.Equal(t, tt.expectedItemsLen, len(resp.Data.Items))

				// 对于分页测试，进一步验证返回的关键词正确性
				if tt.name == "pagination test - page 1" {
					assert.Equal(t, "keyword1", resp.Data.Items[0])
					assert.Equal(t, "keyword2", resp.Data.Items[1])
				} else if tt.name == "pagination test - page 2" {
					assert.Equal(t, "keyword3", resp.Data.Items[0])
					assert.Equal(t, "keyword4", resp.Data.Items[1])
				} else if tt.name == "pagination test - last page" {
					assert.Equal(t, "keyword5", resp.Data.Items[0])
				}
			}
		})
	}
}
