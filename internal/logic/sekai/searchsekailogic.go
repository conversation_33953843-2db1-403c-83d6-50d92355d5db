package sekai

import (
	"context"

	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/opensearch"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

const (
	// maxSearchResultSize 搜索结果最大数量
	maxSearchResultSize = 200
)

type SearchSekaiLogic struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *coverter
}

func NewSearchSekaiLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchSekaiLogic {
	return &SearchSekaiLogic{
		Logger:   logx.WithContext(ctx),
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: newCoverter(svcCtx),
	}
}

func (l *SearchSekaiLogic) SearchSekai(req *types.SearchSekaiReq) (resp *types.SearchSekaiResp, err error) {
	// 处理空关键词情况
	if req.Keywords == "" {
		return l.buildEmptyResponse(req.Page, req.Size, 0), nil
	}

	// 限制最多搜索200条
	if req.Page*req.Size > maxSearchResultSize {
		return l.buildEmptyResponse(req.Page, req.Size, 0), nil
	}

	// 获取搜索参数
	searchReq, err := l.buildSearchRequest(req)
	if err != nil {
		return nil, err
	}

	// 执行搜索
	searchResult, err := l.executeSearch(searchReq)
	if err != nil {
		return nil, err
	}

	// 如果没有搜索结果
	if len(searchResult.Hits.Hits) == 0 {
		return l.buildEmptyResponse(req.Page, req.Size, searchResult.Hits.Total.Value), nil
	}

	// 获取并处理搜索结果
	items, err := l.processSearchResults(searchResult)
	if err != nil {
		return nil, err
	}

	// 计算总数，同时考虑最大限制
	total := min(searchResult.Hits.Total.Value, maxSearchResultSize)

	// 构建并返回结果
	return &types.SearchSekaiResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.SekaiBasePagination{
			Pagination: utils.GetPageInfo(total, req.Page, req.Size),
			Items:      items,
		},
	}, nil
}

// searchResult 是opensearch.SearchResult[opensearch.Sekai]的别名
type searchResult = opensearch.SearchResult[opensearch.Sekai]

// buildEmptyResponse 构建空结果响应
func (l *SearchSekaiLogic) buildEmptyResponse(page, size int, total int) *types.SearchSekaiResp {
	return &types.SearchSekaiResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.SekaiBasePagination{
			Pagination: utils.GetPageInfo(total, page, size),
			Items:      []*types.SekaiBaseInfo{},
		},
	}
}

// buildSearchRequest 构建搜索请求参数
func (l *SearchSekaiLogic) buildSearchRequest(req *types.SearchSekaiReq) (opensearch.SearchSekaiReq, error) {
	// 从上下文中获取当前用户ID和app版本信息
	currentUser := servermsg.GetCurrentUser(l.ctx)
	appVersion := servermsg.Get(l.ctx).AppVersion
	if appVersion == "" {
		return opensearch.SearchSekaiReq{}, xerrors.New(values.ErrorCodeBadRequest, "app version is empty")
	}

	// 构建OpenSearch查询参数
	return opensearch.SearchSekaiReq{
		Keyword:    req.Keywords,
		AppVersion: appVersion,
		UserID:     currentUser.UserID,
		Page:       req.Page,
		Size:       req.Size,
	}, nil
}

// executeSearch 执行搜索操作
func (l *SearchSekaiLogic) executeSearch(searchReq opensearch.SearchSekaiReq) (*searchResult, error) {
	searchResult, err := l.svcCtx.Opsearch.SearchSekaiByKeyword(l.ctx, searchReq)
	if err != nil {
		l.Logger.Errorf("failed to search sekai: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "search failed")
	}
	return searchResult, nil
}

// processSearchResults 处理搜索结果
func (l *SearchSekaiLogic) processSearchResults(searchResult *searchResult) ([]*types.SekaiBaseInfo, error) {
	// 提取搜索结果中的SekaiID列表
	sekaiIDs := lo.Map(searchResult.Hits.Hits, func(hit opensearch.SearchHit[opensearch.Sekai], _ int) int64 {
		return int64(hit.Source.ID)
	})

	// 从数据库中获取详细的Sekai信息
	sekaiBases, err := l.fetchSekaiDetails(sekaiIDs)
	if err != nil {
		return nil, err
	}

	// 将数据库中的Sekai信息转换为前端需要的格式
	items, err := l.coverter.BathToSekaiBaseInfo(l.ctx, sekaiBases)
	if err != nil {
		l.Logger.Errorf("failed to convert sekai info: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to convert sekai info")
	}

	return items, nil
}

// fetchSekaiDetails 从数据库获取Sekai详细信息
func (l *SearchSekaiLogic) fetchSekaiDetails(sekaiIDs []int64) ([]*pg.AiuSekaiInfoTable, error) {
	sekaiBases, err := l.svcCtx.DB.GetSekaisByIDsWithOrder(l.ctx, &pg.GetSekaisByIDsWithOrderParams{
		SekaiIds: sekaiIDs,
	})
	if err != nil {
		l.Logger.Errorf("failed to get sekais from database: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get sekais")
	}
	return sekaiBases, nil
}
