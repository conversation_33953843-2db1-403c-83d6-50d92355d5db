package sekai

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/hashicorp/go-version"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type GetHotSekaiLogic struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *coverter
}

func NewGetHotSekaiLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetHotSekaiLogic {
	return &GetHotSekaiLogic{
		Logger:   logx.WithContext(ctx),
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: newCoverter(svcCtx),
	}
}

// GetHotSekai 获取热门sekai列表
func (l *GetHotSekaiLogic) GetHotSekai(req *types.GetHotSekaiReq) (*types.GetHotSekaiResp, error) {
	// 获取热门sekai ID列表
	sekaiIDs, err := l.getHotSekaiIDs(l.ctx, servermsg.Get(l.ctx).AppVersion)
	if err != nil {
		logc.Errorf(l.ctx, "Failed to get hot sekai IDs from redis: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "get hot sekai failed")
	}

	// 计算总数
	total := len(sekaiIDs)

	// 构建分页数据
	pagination := utils.GetPageInfo(total, req.Page, req.Size)

	// 如果没有热门sekai或分页超出范围，返回空列表
	if total == 0 || (req.Page-1)*req.Size >= total {
		return createSuccessResponse(pagination, []*types.HotSekaiResult{}), nil
	}

	// 计算分页范围并获取当前页的sekaiIDs
	start := (req.Page - 1) * req.Size
	end := min(start+req.Size, total)
	pagedSekaiIDs := sekaiIDs[start:end]

	// 从数据库获取详细信息
	sekaiBases, err := l.svcCtx.DB.GetSekaisByIDsWithOrder(l.ctx, &pg.GetSekaisByIDsWithOrderParams{
		SekaiIds:          pagedSekaiIDs,
		Published:         pgtype.Bool{Bool: true, Valid: true},
		FilterByPublished: true,
		IsPublic:          pgtype.Bool{Bool: true, Valid: true},
		FilterByPublic:    true,
	})
	if err != nil {
		logc.Errorf(l.ctx, "Failed to get sekai details from database: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "get hot sekai details failed")
	}
	logc.Infof(l.ctx, "sekaiBases length: %d", len(sekaiBases))

	// 转换为接口需要的数据结构
	items := lo.Map(sekaiBases, func(sekai *pg.AiuSekaiInfoTable, _ int) *types.HotSekaiResult {
		return &types.HotSekaiResult{
			SekaiID:       int(sekai.SekaiID),
			Title:         sekai.Title.String,
			MinAppVersion: int(sekai.MinAppVersion.Int32),
		}
	})

	// 返回结果
	return createSuccessResponse(pagination, items), nil
}

type sekaiHotList struct {
	ID         int64 `json:"id"`
	MinVersion int   `json:"min_ver"`
}

func (l *GetHotSekaiLogic) getHotSekaiIDs(ctx context.Context, appVersion string) ([]int64, error) {
	topInfo, err := l.svcCtx.Dynamo.GetTopInfo(l.ctx, "sekai_hot_list")
	if err != nil {
		logc.Errorf(l.ctx, "Failed to get top info from dynamo: %v", err)
		return nil, fmt.Errorf("failed to get top info from dynamo: %v", err)
	}
	logc.Infof(l.ctx, "topInfo: %s", topInfo)

	// 尝试解析为JSON数组
	var hotList []*sekaiHotList
	if err := json.Unmarshal([]byte(topInfo), &hotList); err != nil {
		// 如果解析JSON失败，记录日志
		logc.Infof(ctx, "Failed to parse hot sekai IDs as JSON array: %v, topInfo: %s", err, topInfo)
		return nil, err
	}

	// 过滤掉appVersion小于等于当前版本的sekai
	ids := make([]int64, 0, len(hotList))
	for _, item := range hotList {
		minVersionStr := utils.ToAppVersionString(item.MinVersion)
		v1, err := version.NewVersion(minVersionStr)
		if err != nil {
			logc.Infof(ctx, "Failed to parse min version: %v, value: %v, minVersionStr: %s", err, item.MinVersion, minVersionStr)
			continue
		}
		v2, err := version.NewVersion(appVersion)
		if err != nil {
			logc.Infof(ctx, "Failed to parse app version: %v, value: %s", err, appVersion)
			continue
		}
		if v1.LessThanOrEqual(v2) {
			ids = append(ids, item.ID)
		}
	}

	return ids, nil
}

// 创建成功响应
func createSuccessResponse(pagination types.Pagination, items []*types.HotSekaiResult) *types.GetHotSekaiResp {
	return &types.GetHotSekaiResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.HotSekaiPagination{
			Pagination: pagination,
			Items:      items,
		},
	}
}
