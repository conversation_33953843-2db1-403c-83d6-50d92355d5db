package sekai

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func setupCoverterTest(t *testing.T) (*gomock.Controller, *coverter, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	coverter := newCoverter(svcCtx)

	return ctrl, coverter, mockDB, mockDynamo, mockS3
}

func TestBuildCategories(t *testing.T) {
	ctrl, converter, _, _, _ := setupCoverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	labelMap := map[int]*pg.AiuLabelTable{
		1: {ID: 1, LabelName: "Label 1", DisplayName: pgtype.Text{String: "Display 1", Valid: true}, Emoji: "🚀"},
		2: {ID: 2, LabelName: "Label 2", DisplayName: pgtype.Text{String: "Display 2", Valid: true}, Emoji: "🔥"},
	}

	sekaiBase := &pg.AiuSekaiInfoTable{
		UserCategory: []int64{1, 2},
	}

	result := converter.buildCategories(ctx, sekaiBase, labelMap)

	assert.Equal(t, 2, len(result))
	assert.Equal(t, 1, result[0].LableID)
	assert.Equal(t, "Label 1", result[0].LabelName)
	assert.Equal(t, "Display 1", result[0].DisplayName)
	assert.Equal(t, "🚀", result[0].Emoji)
	assert.Equal(t, 2, result[1].LableID)
	assert.Equal(t, "Label 2", result[1].LabelName)
	assert.Equal(t, "Display 2", result[1].DisplayName)
	assert.Equal(t, "🔥", result[1].Emoji)
}

func TestGetBatchCoverURLs(t *testing.T) {
	ctrl, converter, _, _, mockS3 := setupCoverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	sekaiBases := []*pg.AiuSekaiInfoTable{
		{CoverUrl: pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true}},
		{CoverUrl: pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true}},
	}

	coverURLs := []string{
		"http://example.com/cover1.jpg",
		"http://example.com/cover2.jpg",
	}

	coverURLMap := map[string]string{
		"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
		"http://example.com/cover2.jpg": "https://presigned.example.com/cover2.jpg",
	}

	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), coverURLs, s3.Original).
		Return(coverURLMap, nil)

	result, err := converter.getBatchCoverURLs(ctx, sekaiBases)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))
	assert.Equal(t, "https://presigned.example.com/cover1.jpg", result["http://example.com/cover1.jpg"])
	assert.Equal(t, "https://presigned.example.com/cover2.jpg", result["http://example.com/cover2.jpg"])
}

func TestGetBatchNewCoverURLs(t *testing.T) {
	ctrl, converter, _, _, mockS3 := setupCoverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	sekaiBases := []*pg.AiuSekaiInfoTable{
		{CoverImage: pgtype.Text{String: "http://example.com/newcover1.jpg", Valid: true}},
		{CoverImage: pgtype.Text{String: "http://example.com/newcover2.jpg", Valid: true}},
	}

	coverURLs := []string{
		"http://example.com/newcover1.jpg",
		"http://example.com/newcover2.jpg",
	}

	coverURLMap := map[string]string{
		"http://example.com/newcover1.jpg": "https://presigned.example.com/newcover1.jpg",
		"http://example.com/newcover2.jpg": "https://presigned.example.com/newcover2.jpg",
	}

	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), coverURLs, s3.Original).
		Return(coverURLMap, nil)

	result, err := converter.getBatchNewCoverURLs(ctx, sekaiBases)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))
	assert.Equal(t, "https://presigned.example.com/newcover1.jpg", result["http://example.com/newcover1.jpg"])
	assert.Equal(t, "https://presigned.example.com/newcover2.jpg", result["http://example.com/newcover2.jpg"])
}

func TestGetBatchBackgroundURLs(t *testing.T) {
	ctrl, converter, _, _, mockS3 := setupCoverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	sekaiBases := []*pg.AiuSekaiInfoTable{
		{BackgroundImage: pgtype.Text{String: "http://example.com/bg1.jpg", Valid: true}},
		{BackgroundImage: pgtype.Text{String: "http://example.com/bg2.jpg", Valid: true}},
	}

	bgURLs := []string{
		"http://example.com/bg1.jpg",
		"http://example.com/bg2.jpg",
	}

	bgURLMap := map[string]string{
		"http://example.com/bg1.jpg": "https://presigned.example.com/bg1.jpg",
		"http://example.com/bg2.jpg": "https://presigned.example.com/bg2.jpg",
	}

	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), bgURLs, s3.Original).
		Return(bgURLMap, nil)

	result, err := converter.getBatchBackgroundURLs(ctx, sekaiBases)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))
	assert.Equal(t, "https://presigned.example.com/bg1.jpg", result["http://example.com/bg1.jpg"])
	assert.Equal(t, "https://presigned.example.com/bg2.jpg", result["http://example.com/bg2.jpg"])
}

func TestGetBatchUsers(t *testing.T) {
	ctrl, converter, _, mockDynamo, _ := setupCoverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	sekaiBases := []*pg.AiuSekaiInfoTable{
		{CreatorUserID: pgtype.Int8{Int64: 123, Valid: true}},
		{CreatorUserID: pgtype.Int8{Int64: 456, Valid: true}},
		{CreatorUserID: pgtype.Int8{Int64: 123, Valid: true}}, // 重复的用户ID
	}

	mockDynamo.EXPECT().
		BatchGetUsers(gomock.Any(), []int{123, 456}).
		Return(map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
			456: {ID: 456, UserName: "creator2"},
		}, nil)

	result, err := converter.getBatchUsers(ctx, sekaiBases)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))
	assert.Equal(t, 123, result[123].ID)
	assert.Equal(t, "creator1", result[123].UserName)
	assert.Equal(t, 456, result[456].ID)
	assert.Equal(t, "creator2", result[456].UserName)
}

func TestGetBatchViewCount(t *testing.T) {
	ctrl, converter, mockDB, _, _ := setupCoverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	sekaiBases := []*pg.AiuSekaiInfoTable{
		{SekaiID: 1},
		{SekaiID: 2},
	}

	mockDB.EXPECT().
		GetSekaiViewCountByIDs(gomock.Any(), []int64{1, 2}).
		Return([]*pg.GetSekaiViewCountByIDsRow{
			{SekaiID: 1, ViewCount: 10},
			{SekaiID: 2, ViewCount: 20},
		}, nil)

	result, err := converter.getBatchViewCount(ctx, sekaiBases)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))
	assert.Equal(t, 10, result[1])
	assert.Equal(t, 20, result[2])
}

func TestGetBatchLabels(t *testing.T) {
	ctrl, converter, mockDB, _, _ := setupCoverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	sekaiBases := []*pg.AiuSekaiInfoTable{
		{
			UserCategory: []int64{1, 2},
			AiCategory:   []int64{},
		},
		{
			UserCategory: []int64{},
			AiCategory:   []int64{3, 4},
		},
	}

	mockDB.EXPECT().
		GetSekaiCategoriesByLabelIDs(gomock.Any(), []int32{1, 2, 3, 4}).
		Return([]*pg.AiuLabelTable{
			{ID: 1, LabelName: "Label 1", DisplayName: pgtype.Text{String: "Display 1", Valid: true}, Emoji: "🚀"},
			{ID: 2, LabelName: "Label 2", DisplayName: pgtype.Text{String: "Display 2", Valid: true}, Emoji: "🔥"},
			{ID: 3, LabelName: "Label 3", DisplayName: pgtype.Text{String: "Display 3", Valid: true}, Emoji: "✨"},
			{ID: 4, LabelName: "Label 4", DisplayName: pgtype.Text{String: "Display 4", Valid: true}, Emoji: "🌟"},
		}, nil)

	result, err := converter.getBatchLabels(ctx, sekaiBases)

	assert.NoError(t, err)
	assert.Equal(t, 4, len(result))
	assert.Equal(t, "Label 1", result[1].LabelName)
	assert.Equal(t, "Label 2", result[2].LabelName)
	assert.Equal(t, "Label 3", result[3].LabelName)
	assert.Equal(t, "Label 4", result[4].LabelName)
}

func TestBuildBatchSekaiBaseInfo(t *testing.T) {
	ctrl, converter, _, _, _ := setupCoverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	sekaiBases := []*pg.AiuSekaiInfoTable{
		{
			SekaiID:         1,
			Title:           pgtype.Text{String: "Sekai 1", Valid: true},
			CoverUrl:        pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
			CoverImage:      pgtype.Text{String: "http://example.com/newcover1.jpg", Valid: true},
			BackgroundImage: pgtype.Text{String: "http://example.com/bg1.jpg", Valid: true},
			CreatorUserID:   pgtype.Int8{Int64: 123, Valid: true},
			IsPublic:        pgtype.Bool{Bool: true, Valid: true},
			Intro:           pgtype.Text{String: "Intro 1", Valid: true},
			UserCategory:    []int64{1, 2},
			AiCategory:      []int64{},
			TemplateID:      pgtype.Int8{Int64: 10, Valid: true},
			MinAppVersion:   pgtype.Int4{Int32: 100, Valid: true},
		},
	}

	data := &batchData{
		coverURLMap: map[string]string{
			"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
		},
		newCoverURLs: map[string]string{
			"http://example.com/newcover1.jpg": "https://presigned.example.com/newcover1.jpg",
		},
		backgroundURLMap: map[string]string{
			"http://example.com/bg1.jpg": "https://presigned.example.com/bg1.jpg",
		},
		userMap: map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
		},
		viewCountMap: map[int]int{
			1: 100,
		},
		labelMap: map[int]*pg.AiuLabelTable{
			1: {ID: 1, LabelName: "Label 1", DisplayName: pgtype.Text{String: "Display 1", Valid: true}, Emoji: "🚀"},
			2: {ID: 2, LabelName: "Label 2", DisplayName: pgtype.Text{String: "Display 2", Valid: true}, Emoji: "🔥"},
		},
	}

	result := converter.buildBatchSekaiBaseInfo(ctx, sekaiBases, data)

	assert.Equal(t, 1, len(result))
	assert.Equal(t, 1, result[0].SekaiID)
	assert.Equal(t, "Sekai 1", result[0].Title)
	assert.Equal(t, "https://presigned.example.com/cover1.jpg", result[0].CoverURL)
	assert.Equal(t, "https://presigned.example.com/newcover1.jpg", result[0].NewCoverURL)
	assert.Equal(t, "https://presigned.example.com/bg1.jpg", result[0].BackgroundURL)
	assert.Equal(t, 123, result[0].CreatorUserID)
	assert.Equal(t, "creator1", result[0].CreatorUserName)
	assert.True(t, result[0].Public)
	assert.Equal(t, "Intro 1", result[0].Intro)
	assert.Equal(t, 100, result[0].ViewCount)
	assert.Equal(t, 10, result[0].TemplateID)
	assert.Equal(t, 100, result[0].MinAppVersion)
	assert.Equal(t, 2, len(result[0].Categories))
}

func TestBathToSekaiBaseInfo(t *testing.T) {
	ctrl, converter, mockDB, mockDynamo, mockS3 := setupCoverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	sekaiBases := []*pg.AiuSekaiInfoTable{
		{
			SekaiID:         1,
			Title:           pgtype.Text{String: "Sekai 1", Valid: true},
			CoverUrl:        pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
			CoverImage:      pgtype.Text{String: "http://example.com/newcover1.jpg", Valid: true},
			BackgroundImage: pgtype.Text{String: "http://example.com/bg1.jpg", Valid: true},
			CreatorUserID:   pgtype.Int8{Int64: 123, Valid: true},
			IsPublic:        pgtype.Bool{Bool: true, Valid: true},
			Intro:           pgtype.Text{String: "Intro 1", Valid: true},
			UserCategory:    []int64{1, 2},
			AiCategory:      []int64{},
			TemplateID:      pgtype.Int8{Int64: 10, Valid: true},
			MinAppVersion:   pgtype.Int4{Int32: 100, Valid: true},
		},
	}

	// Mock for getBatchCoverURLs
	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/cover1.jpg"}, s3.Original).
		Return(map[string]string{
			"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
		}, nil)

	// Mock for getBatchNewCoverURLs
	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/newcover1.jpg"}, s3.Original).
		Return(map[string]string{
			"http://example.com/newcover1.jpg": "https://presigned.example.com/newcover1.jpg",
		}, nil)

	// Mock for getBatchBackgroundURLs
	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/bg1.jpg"}, s3.Original).
		Return(map[string]string{
			"http://example.com/bg1.jpg": "https://presigned.example.com/bg1.jpg",
		}, nil)

	// Mock for getBatchUsers
	mockDynamo.EXPECT().
		BatchGetUsers(gomock.Any(), []int{123}).
		Return(map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
		}, nil)

	// Mock for getBatchViewCount
	mockDB.EXPECT().
		GetSekaiViewCountByIDs(gomock.Any(), []int64{1}).
		Return([]*pg.GetSekaiViewCountByIDsRow{
			{SekaiID: 1, ViewCount: 100},
		}, nil)

	// Mock for getBatchLabels
	mockDB.EXPECT().
		GetSekaiCategoriesByLabelIDs(gomock.Any(), []int32{1, 2}).
		Return([]*pg.AiuLabelTable{
			{ID: 1, LabelName: "Label 1", DisplayName: pgtype.Text{String: "Display 1", Valid: true}, Emoji: "🚀"},
			{ID: 2, LabelName: "Label 2", DisplayName: pgtype.Text{String: "Display 2", Valid: true}, Emoji: "🔥"},
		}, nil)

	result, err := converter.BathToSekaiBaseInfo(ctx, sekaiBases)

	assert.NoError(t, err)
	assert.Equal(t, 1, len(result))
	assert.Equal(t, 1, result[0].SekaiID)
	assert.Equal(t, "Sekai 1", result[0].Title)
	assert.Equal(t, "https://presigned.example.com/cover1.jpg", result[0].CoverURL)
	assert.Equal(t, "https://presigned.example.com/newcover1.jpg", result[0].NewCoverURL)
	assert.Equal(t, "https://presigned.example.com/bg1.jpg", result[0].BackgroundURL)
	assert.Equal(t, 123, result[0].CreatorUserID)
	assert.Equal(t, "creator1", result[0].CreatorUserName)
	assert.True(t, result[0].Public)
	assert.Equal(t, "Intro 1", result[0].Intro)
	assert.Equal(t, 100, result[0].ViewCount)
	assert.Equal(t, 10, result[0].TemplateID)
	assert.Equal(t, 100, result[0].MinAppVersion)
	assert.Equal(t, 2, len(result[0].Categories))
}

func TestEmptyBathToSekaiBaseInfo(t *testing.T) {
	ctrl, converter, _, _, _ := setupCoverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	// Test with empty slice
	sekaiBases := []*pg.AiuSekaiInfoTable{}

	result, err := converter.BathToSekaiBaseInfo(ctx, sekaiBases)

	assert.NoError(t, err)
	assert.Equal(t, 0, len(result))
}
