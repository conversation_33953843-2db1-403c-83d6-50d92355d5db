package voice

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/opensearch"
	"github.com/sekai-app/sekai-go/internal/db/opensearch/opensearchmock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupSearchTest(t *testing.T) (*gomock.Controller, *SearchLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3, *opensearchmock.MockOpenSearch) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)
	mockOpsearch := opensearchmock.NewMockOpenSearch(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:       mockDB,
		Dynamo:   mockDynamo,
		S3:       mockS3,
		Opsearch: mockOpsearch,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewSearchLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3, mockOpsearch
}

func TestSearch(t *testing.T) {
	tests := []struct {
		name             string
		setupMocks       func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3, *opensearchmock.MockOpenSearch)
		request          *types.SearchReq
		expectedCode     int
		expectedErrMsg   string
		expectedItemsLen int
		expectedTotal    int
	}{
		{
			name: "successful search with results",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchVoiceByKeyword 的期望
				searchResult := &opensearch.SearchResult[opensearch.Voice]{
					Hits: struct {
						Total struct {
							Value int `json:"value"`
						} `json:"total"`
						MaxScore float64                                  `json:"max_score"`
						Hits     []opensearch.SearchHit[opensearch.Voice] `json:"hits"`
					}{
						Total: struct {
							Value int `json:"value"`
						}{Value: 3},
						Hits: []opensearch.SearchHit[opensearch.Voice]{
							{
								Score: 1.5,
								Source: opensearch.Voice{
									VoiceID:     1,
									DisplayName: "Test Voice 1",
								},
							},
							{
								Score: 1.2,
								Source: opensearch.Voice{
									VoiceID:     2,
									DisplayName: "Test Voice 2",
								},
							},
							{
								Score: 0.8,
								Source: opensearch.Voice{
									VoiceID:     3,
									DisplayName: "Test Voice 3",
								},
							},
						},
					},
				}
				mockOpsearch.EXPECT().
					SearchVoiceByKeyword(gomock.Any(), opensearch.SearchVoiceReq{
						Keyword: "test",
						UserID:  123,
						Page:    1,
						Size:    100,
					}).
					Return(searchResult, nil)

				// 设置 Dynamo.BatchGetVoiceMetaInfo 的期望
				mockDynamo.EXPECT().
					BatchGetVoiceMetaInfo(gomock.Any(), []int{1, 2, 3}).
					Return(map[int]*dynamo.VoiceMetaInfo{
						1: {UseCount: 10},
						2: {UseCount: 5},
						3: {UseCount: 3},
					}, nil)

				// 设置 DB.GetVoicesWithLikeStatus 的期望
				mockDB.EXPECT().
					GetVoicesWithLikeStatus(gomock.Any(), gomock.Any()).
					Return([]*pg.GetVoicesWithLikeStatusRow{
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       1,
								DisplayName:   pgtype.Text{String: "Test Voice 1", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 180, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{1},
								Nsfw:          false,
							},
							IsLiked: false,
						},
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       2,
								DisplayName:   pgtype.Text{String: "Test Voice 2", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice2.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 200, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 456, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{2},
								Nsfw:          false,
							},
							IsLiked: true,
						},
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       3,
								DisplayName:   pgtype.Text{String: "Test Voice 3", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice3.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 150, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 789, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{3},
								Nsfw:          false,
							},
							IsLiked: false,
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/voice1.mp3": "https://presigned.example.com/voice1.mp3",
						"http://example.com/voice2.mp3": "https://presigned.example.com/voice2.mp3",
						"http://example.com/voice3.mp3": "https://presigned.example.com/voice3.mp3",
					}, nil)

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "creator1"},
						456: {ID: 456, UserName: "creator2"},
						789: {ID: 789, UserName: "creator3"},
					}, nil)

				// 设置 GetVoiceCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
						{ID: 3, Name: "Category 3", CoverUrl: pgtype.Text{String: "http://example.com/cat3.jpg", Valid: true}},
					}, nil)

				// 设置 GetAccentsByIDs 的期望
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceAccentTable{}, nil)
			},
			request: &types.SearchReq{
				Keyword: "test",
				Page:    1,
				Size:    10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 3,
			expectedTotal:    3,
		},
		{
			name: "search with no results",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchVoiceByKeyword 的期望返回空结果
				searchResult := &opensearch.SearchResult[opensearch.Voice]{
					Hits: struct {
						Total struct {
							Value int `json:"value"`
						} `json:"total"`
						MaxScore float64                                  `json:"max_score"`
						Hits     []opensearch.SearchHit[opensearch.Voice] `json:"hits"`
					}{
						Total: struct {
							Value int `json:"value"`
						}{Value: 0},
						Hits: []opensearch.SearchHit[opensearch.Voice]{},
					},
				}
				mockOpsearch.EXPECT().
					SearchVoiceByKeyword(gomock.Any(), opensearch.SearchVoiceReq{
						Keyword: "nonexistent",
						UserID:  123,
						Page:    1,
						Size:    100,
					}).
					Return(searchResult, nil)
			},
			request: &types.SearchReq{
				Keyword: "nonexistent",
				Page:    1,
				Size:    10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 0,
			expectedTotal:    0,
		},
		{
			name: "search error in OpenSearch",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchVoiceByKeyword 的期望返回错误
				mockOpsearch.EXPECT().
					SearchVoiceByKeyword(gomock.Any(), opensearch.SearchVoiceReq{
						Keyword: "test",
						UserID:  123,
						Page:    1,
						Size:    100,
					}).
					Return(nil, xerrors.New(500, "opensearch error"))
			},
			request: &types.SearchReq{
				Keyword: "test",
				Page:    1,
				Size:    10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "Search Voice failed",
		},
		{
			name: "error in BatchGetVoiceMetaInfo",
			setupMocks: func(_ *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchVoiceByKeyword 的期望
				searchResult := &opensearch.SearchResult[opensearch.Voice]{
					Hits: struct {
						Total struct {
							Value int `json:"value"`
						} `json:"total"`
						MaxScore float64                                  `json:"max_score"`
						Hits     []opensearch.SearchHit[opensearch.Voice] `json:"hits"`
					}{
						Total: struct {
							Value int `json:"value"`
						}{Value: 1},
						Hits: []opensearch.SearchHit[opensearch.Voice]{
							{
								Score: 1.5,
								Source: opensearch.Voice{
									VoiceID:     1,
									DisplayName: "Test Voice 1",
								},
							},
						},
					},
				}
				mockOpsearch.EXPECT().
					SearchVoiceByKeyword(gomock.Any(), opensearch.SearchVoiceReq{
						Keyword: "test",
						UserID:  123,
						Page:    1,
						Size:    100,
					}).
					Return(searchResult, nil)

				// 设置 Dynamo.BatchGetVoiceMetaInfo 的期望返回错误
				mockDynamo.EXPECT().
					BatchGetVoiceMetaInfo(gomock.Any(), []int{1}).
					Return(nil, xerrors.New(500, "dynamo error"))
			},
			request: &types.SearchReq{
				Keyword: "test",
				Page:    1,
				Size:    10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "Search Voice failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl, logic, mockDB, mockDynamo, mockS3, mockOpsearch := setupSearchTest(t)
			defer ctrl.Finish()

			tt.setupMocks(mockDB, mockDynamo, mockS3, mockOpsearch)

			resp, err := logic.Search(tt.request)

			if tt.expectedErrMsg != "" {
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Contains(t, xError.Msg, tt.expectedErrMsg)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				if resp.Data != nil {
					assert.Equal(t, tt.expectedItemsLen, len(resp.Data.Items))
					assert.Equal(t, tt.expectedTotal, resp.Data.Pagination.Total)
				}
			}
		})
	}
}

func TestSortSearchResult(t *testing.T) {
	ctrl, logic, _, _, _, _ := setupSearchTest(t)
	defer ctrl.Finish()

	voices := []opensearch.SearchHit[opensearch.Voice]{
		{
			Score: 1.5,
			Source: opensearch.Voice{
				VoiceID:     1,
				DisplayName: "Voice 1",
			},
		},
		{
			Score: 1.2,
			Source: opensearch.Voice{
				VoiceID:     2,
				DisplayName: "Voice 2",
			},
		},
		{
			Score: 0.8,
			Source: opensearch.Voice{
				VoiceID:     3,
				DisplayName: "Voice 3",
			},
		},
	}

	voiceMetaInfoMap := map[int]*dynamo.VoiceMetaInfo{
		1: {UseCount: 5},
		2: {UseCount: 20}, // 高人气，应该排在前面
		3: {UseCount: 1},
	}

	voiceIDs, err := logic.sortSearchResult(logic.ctx, voices, voiceMetaInfoMap)

	assert.NoError(t, err)
	assert.Equal(t, 3, len(voiceIDs))
	// Voice 2 应该排在第一位，因为它有更高的人气分数
	assert.Equal(t, 2, voiceIDs[0])
}

func TestGetVoiceByIDs(t *testing.T) {
	tests := []struct {
		name           string
		setupMocks     func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3)
		voiceIDs       []int
		page           int
		size           int
		expectedLen    int
		expectedErrMsg string
	}{
		{
			name: "successful get voices",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				mockDB.EXPECT().
					GetVoicesWithLikeStatus(gomock.Any(), gomock.Any()).
					Return([]*pg.GetVoicesWithLikeStatusRow{
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       1,
								DisplayName:   pgtype.Text{String: "Test Voice 1", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 180, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{1},
								Nsfw:          false,
							},
							IsLiked: false,
						},
					}, nil)

				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/voice1.mp3": "https://presigned.example.com/voice1.mp3",
					}, nil)

				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "creator1"},
					}, nil)

				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
					}, nil)

				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceAccentTable{}, nil)
			},
			voiceIDs:    []int{1},
			page:        1,
			size:        10,
			expectedLen: 1,
		},
		{
			name: "database error",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				mockDB.EXPECT().
					GetVoicesWithLikeStatus(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(500, "database error"))
			},
			voiceIDs:       []int{1},
			page:           1,
			size:           10,
			expectedErrMsg: "internal server error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl, logic, mockDB, mockDynamo, mockS3, _ := setupSearchTest(t)
			defer ctrl.Finish()

			tt.setupMocks(mockDB, mockDynamo, mockS3)

			result, err := logic.getVoiceByIDs(logic.ctx, tt.voiceIDs, tt.page, tt.size)

			if tt.expectedErrMsg != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErrMsg)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedLen, len(result))
			}
		})
	}
}
