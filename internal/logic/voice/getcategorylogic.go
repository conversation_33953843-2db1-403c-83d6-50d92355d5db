package voice

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	xerrors "github.com/zeromicro/x/errors"
)

type GetCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取分类
func NewGetCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCategoryLogic {
	return &GetCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCategoryLogic) GetCategory(req *types.GetCategoryReq) (*types.GetCategoryResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	creationType := int32(0)
	categories, err := l.svcCtx.DB.GetVoiceCategories(l.ctx, &pg.GetVoiceCategoriesParams{
		CreationType: creationType,
		Page:         int32(req.Page),
		PageSize:     int32(req.Size),
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voice categories: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voice categories")
	}

	// Get total count for pagination
	count, err := l.svcCtx.DB.GetVoiceCategoriesCount(l.ctx, creationType)
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voice categories count: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voice categories count")
	}

	// Convert database records to response types
	var items []*types.VoiceCategory
	for _, category := range categories {
		items = append(items, &types.VoiceCategory{
			ID:       int(category.ID),
			Name:     category.Name,
			CoverURL: category.CoverUrl.String,
		})
	}

	return &types.GetCategoryResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: &types.GetCategoryPagination{
			Pagination: utils.GetPageInfo(int(count), req.Page, req.Size),
			Items:      items,
		},
	}, nil
}
