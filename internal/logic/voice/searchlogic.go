package voice

import (
	"context"
	"fmt"
	"math"
	"sort"

	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/opensearch"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	xerrors "github.com/zeromicro/x/errors"
)

type SearchLogic struct {
	ctx       context.Context
	svcCtx    *svc.ServiceContext
	converter *converter
}

// 搜索语音
func NewSearchLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchLogic {
	return &SearchLogic{
		ctx:       ctx,
		svcCtx:    svcCtx,
		converter: newConverter(svcCtx),
	}
}

func (l *SearchLogic) Search(req *types.SearchReq) (*types.SearchResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	userID := servermsg.GetCurrentUser(l.ctx).UserID
	// 搜索100条，然后排序，只返回前50条
	searchResult, err := l.svcCtx.Opsearch.SearchVoiceByKeyword(l.ctx, opensearch.SearchVoiceReq{
		Keyword: req.Keyword,
		UserID:  userID,
		Page:    1,
		Size:    100,
	})
	if err != nil {
		logc.Errorf(l.ctx, "SearchVoiceByKeyword failed: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "Search Voice failed")
	}

	logc.Debugf(l.ctx, "total: %d", searchResult.Hits.Total.Value)
	if len(searchResult.Hits.Hits) == 0 {
		logc.Infof(l.ctx, "no voice found")
		return &types.SearchResp{
			ResponseBase: types.ResponseBase{
				Code: values.ErrorCodeSuccess,
				Msg:  "success",
			},
			Data: &types.VoicePagination{
				Pagination: utils.GetPageInfo(searchResult.Hits.Total.Value, req.Page, req.Size),
				Items:      nil,
			},
		}, nil
	}

	// 获取voiceID列表
	voiceIDs := lo.Map(searchResult.Hits.Hits, func(voice opensearch.SearchHit[opensearch.Voice], _ int) int {
		return voice.Source.VoiceID
	})
	voiceMetaInfoMap, err := l.svcCtx.Dynamo.BatchGetVoiceMetaInfo(l.ctx, voiceIDs)
	if err != nil {
		logc.Errorf(l.ctx, "BatchGetVoiceMetaInfo failed: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "Search Voice failed")
	}

	// 排序
	voiceIDs, err = l.sortSearchResult(l.ctx, searchResult.Hits.Hits, voiceMetaInfoMap)
	if err != nil {
		logc.Errorf(l.ctx, "sortSearchResult failed: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "Search Voice failed")
	}

	// 获取排序后的voice
	data, err := l.getVoiceByIDs(l.ctx, voiceIDs[:min(len(voiceIDs), 50)], req.Page, req.Size)
	if err != nil {
		logc.Errorf(l.ctx, "getVoiceByIDs failed: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "Search Voice failed")
	}

	return &types.SearchResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: &types.VoicePagination{
			Pagination: utils.GetPageInfo(searchResult.Hits.Total.Value, req.Page, req.Size),
			Items:      data,
		},
	}, nil
}

// VoiceSearchModel represents a Voice search result with scoring
type voiceSearchModel struct {
	voiceItem  opensearch.SearchHit[opensearch.Voice]
	maxScore   float64
	popularity int
}

// score calculates normalized search score including popularity
func (m *voiceSearchModel) score() float64 {
	maxScore := float64(1)
	if m.maxScore != 0 {
		maxScore = m.maxScore
	}
	return (m.voiceItem.Score / maxScore) * math.Log(float64(m.popularity+2))
}

// String returns debug string representation
func (m *voiceSearchModel) String() string {
	return fmt.Sprintf("voice_name: %s, id: %d, score: %f, es_score: %f, popularity: %d",
		m.voiceItem.Source.DisplayName,
		m.voiceItem.Source.VoiceID,
		m.score(),
		m.voiceItem.Score,
		m.popularity)
}

// SearchVoice is a type alias for opensearch.SearchHit[opensearch.Voice]
type SearchVoice = opensearch.SearchHit[opensearch.Voice]

// sortSearchResult 返回排序后的voiceID列表
func (l *SearchLogic) sortSearchResult(ctx context.Context,
	voices []SearchVoice, voiceMetaInfoMap map[int]*dynamo.VoiceMetaInfo) ([]int, error) {

	// Create search models
	searchModels := make([]*voiceSearchModel, 0, len(voices))
	for _, voice := range voices {
		popularity := 0
		if meta, ok := voiceMetaInfoMap[voice.Source.VoiceID]; ok {
			popularity = meta.UseCount
		}
		searchModels = append(searchModels, &voiceSearchModel{
			voiceItem:  voice,
			maxScore:   voices[0].Score, // First hit has max score
			popularity: popularity,
		})
	}

	// Sort by score descending
	sort.Slice(searchModels, func(i, j int) bool {
		return searchModels[i].score() > searchModels[j].score()
	})

	// Log top 10 results
	for i, model := range searchModels {
		if i >= 10 {
			break
		}
		logc.Infof(ctx, "search top 10 voice, %d: %s", i+1, model.String())
	}

	voiceIDs := lo.Map(searchModels, func(model *voiceSearchModel, _ int) int {
		return model.voiceItem.Source.VoiceID
	})
	return voiceIDs, nil
}

func (l *SearchLogic) getVoiceByIDs(ctx context.Context,
	voiceIDs []int, page int, size int) ([]*types.VoiceInfo, error) {

	voices, err := l.svcCtx.DB.GetVoicesWithLikeStatus(l.ctx, &pg.GetVoicesWithLikeStatusParams{
		VoiceIds: utils.CovertNumberSlice[int, int32](voiceIDs),
		UserID:   int64(servermsg.GetCurrentUser(l.ctx).UserID),
	})
	if err != nil {
		logc.Errorf(ctx, "GetVoicesWithLikeStatus error: %+v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "internal server error")
	}

	// 批量转换为VoiceInfo
	start := (page - 1) * size
	end := min(page*size, len(voices))
	var data []*types.VoiceInfo
	if start < len(voices) {
		data, err = l.converter.BatchToVoiceWithLikeStatus(l.ctx, voices[start:end])
		if err != nil {
			logc.Errorf(ctx, "BatchToVoiceWithLikeStatus error: %+v", err)
			return nil, xerrors.New(values.ErrorCodeInternalServerError, "internal server error")
		}
	}

	return data, nil
}
