package voice

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupLikeVoiceTest(t *testing.T) (*gomock.Controller, *LikeVoiceLogic, *pgmock.MockQuerier) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)

	svcCtx := &svc.ServiceContext{
		DB: mockDB,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewLikeVoiceLogic(ctx, svcCtx)

	return ctrl, logic, mockDB
}

func TestLikeVoice(t *testing.T) {
	tests := []struct {
		name           string
		setupMocks     func(*pgmock.MockQuerier)
		request        *types.LikeVoiceReq
		expectedCode   int
		expectedErrMsg string
	}{
		{
			name: "successful like voice",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				mockDB.EXPECT().
					GetVoiceByID(gomock.Any(), int32(1)).
					Return(&pg.AiuVoiceTable{
						VoiceId:     1,
						DisplayName: pgtype.Text{String: "Test Voice", Valid: true},
						Enable:      pgtype.Bool{Bool: true, Valid: true},
					}, nil)

				mockDB.EXPECT().
					UpsertUserVoiceLike(gomock.Any(), &pg.UpsertUserVoiceLikeParams{
						UserID:  123,
						VoiceID: 1,
					}).
					Return(int64(1), nil)
			},
			request: &types.LikeVoiceReq{
				VoiceID: 1,
				Like:    true,
			},
			expectedCode: values.ErrorCodeSuccess,
		},
		{
			name: "successful unlike voice",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				mockDB.EXPECT().
					GetVoiceByID(gomock.Any(), int32(1)).
					Return(&pg.AiuVoiceTable{
						VoiceId:     1,
						DisplayName: pgtype.Text{String: "Test Voice", Valid: true},
						Enable:      pgtype.Bool{Bool: true, Valid: true},
					}, nil)

				mockDB.EXPECT().
					DeleteUserVoiceLike(gomock.Any(), &pg.DeleteUserVoiceLikeParams{
						UserID:  123,
						VoiceID: 1,
					}).
					Return(int64(1), nil)
			},
			request: &types.LikeVoiceReq{
				VoiceID: 1,
				Like:    false,
			},
			expectedCode: values.ErrorCodeSuccess,
		},
		{
			name: "voice not found",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				mockDB.EXPECT().
					GetVoiceByID(gomock.Any(), int32(999)).
					Return(nil, xerrors.New(404, "voice not found"))
			},
			request: &types.LikeVoiceReq{
				VoiceID: 999,
				Like:    true,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get voice by id",
		},
		{
			name: "like operation failed",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				mockDB.EXPECT().
					GetVoiceByID(gomock.Any(), int32(1)).
					Return(&pg.AiuVoiceTable{
						VoiceId:     1,
						DisplayName: pgtype.Text{String: "Test Voice", Valid: true},
						Enable:      pgtype.Bool{Bool: true, Valid: true},
					}, nil)

				mockDB.EXPECT().
					UpsertUserVoiceLike(gomock.Any(), &pg.UpsertUserVoiceLikeParams{
						UserID:  123,
						VoiceID: 1,
					}).
					Return(int64(0), nil)
			},
			request: &types.LikeVoiceReq{
				VoiceID: 1,
				Like:    true,
			},
			expectedCode:   values.ErrorCodeNotFound,
			expectedErrMsg: "liked voice not found",
		},
		{
			name: "database error on like operation",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				mockDB.EXPECT().
					GetVoiceByID(gomock.Any(), int32(1)).
					Return(&pg.AiuVoiceTable{
						VoiceId:     1,
						DisplayName: pgtype.Text{String: "Test Voice", Valid: true},
						Enable:      pgtype.Bool{Bool: true, Valid: true},
					}, nil)

				mockDB.EXPECT().
					UpsertUserVoiceLike(gomock.Any(), &pg.UpsertUserVoiceLikeParams{
						UserID:  123,
						VoiceID: 1,
					}).
					Return(int64(0), xerrors.New(500, "database error"))
			},
			request: &types.LikeVoiceReq{
				VoiceID: 1,
				Like:    true,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to like/unlike voice",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl, logic, mockDB := setupLikeVoiceTest(t)
			defer ctrl.Finish()

			tt.setupMocks(mockDB)

			resp, err := logic.LikeVoice(tt.request)

			if tt.expectedErrMsg != "" {
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Contains(t, xError.Msg, tt.expectedErrMsg)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)
			}
		})
	}
}
