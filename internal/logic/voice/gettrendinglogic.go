package voice

import (
	"context"
	"encoding/json"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	xerrors "github.com/zeromicro/x/errors"
)

type GetTrendingLogic struct {
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *converter
}

// 获取热门语音
func NewGetTrendingLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTrendingLogic {
	return &GetTrendingLogic{
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: newConverter(svcCtx),
	}
}

func (l *GetTrendingLogic) GetTrending(req *types.GetTrendingReq) (*types.GetTrendingResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	topInfo, err := l.svcCtx.Dynamo.GetTopInfo(l.ctx, "sekai_backend_voice_top30")
	if err != nil {
		logc.Errorf(l.ctx, "get top info failed: %+v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDynamoError, "get top info failed")
	}

	var voiceIDs []int32
	err = json.Unmarshal([]byte(topInfo), &voiceIDs)
	if err != nil {
		logc.Errorf(l.ctx, "unmarshal voice ids failed: %+v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "unmarshal voice ids failed")
	}
	if len(voiceIDs) == 0 {
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to get trending voices")
	}

	voices, err := l.svcCtx.DB.GetVoicesWithLikeStatus(l.ctx, &pg.GetVoicesWithLikeStatusParams{
		VoiceIds:              voiceIDs,
		UserID:                int64(servermsg.GetCurrentUser(l.ctx).UserID),
		FilterByDisplayEnable: true,
		FilterByEnable:        true,
	})
	if err != nil {
		logc.Errorf(l.ctx, "get voices with like status failed: %+v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "get voices with like status failed")
	}
	logc.Debugf(l.ctx, "voices: %+v", voices)

	start := (req.Page - 1) * req.Size
	end := min(req.Page*req.Size, len(voices))
	var data []*types.VoiceInfo
	if start < len(voices) {
		data, err = l.coverter.BatchToVoiceWithLikeStatus(l.ctx, voices[start:end])
		if err != nil {
			logc.Errorf(l.ctx, "batch to voice base info failed: %+v", err)
			return nil, xerrors.New(values.ErrorCodeInternalServerError, "batch to voice base info failed")
		}
	}

	return &types.GetTrendingResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: &types.VoicePagination{
			Pagination: utils.GetPageInfo(len(voices), req.Page, req.Size),
			Items:      data,
		},
	}, nil
}
