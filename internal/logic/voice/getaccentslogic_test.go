package voice

import (
	"context"
	"testing"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupGetAccentsTest(t *testing.T) (*gomock.Controller, *GetAccentsLogic, *pgmock.MockQuerier) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)

	svcCtx := &svc.ServiceContext{
		DB: mockDB,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewGetAccentsLogic(ctx, svcCtx)

	return ctrl, logic, mockDB
}

func TestGetAccents(t *testing.T) {
	tests := []struct {
		name              string
		setupMocks        func(*pgmock.MockQuerier)
		input             *types.GetAccentsReq
		expectedCode      int
		expectedErrMsg    string
		expectedItemCount int
		expectedTotal     int
	}{
		{
			name: "success",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				// 设置 DB.GetAccents 的期望
				mockDB.EXPECT().
					GetAccents(gomock.Any(), &pg.GetAccentsParams{
						Page:     1,
						PageSize: 10,
					}).
					Return([]*pg.AiuVoiceAccentTable{
						{
							ID:         1,
							AccentName: "American",
							VoiceName:  "Voice1",
						},
						{
							ID:         2,
							AccentName: "British",
							VoiceName:  "Voice2",
						},
						{
							ID:         3,
							AccentName: "Australian",
							VoiceName:  "Voice3",
						},
					}, nil)

				// 设置 DB.GetAccentsCount 的期望
				mockDB.EXPECT().
					GetAccentsCount(gomock.Any()).
					Return(int64(3), nil)
			},
			input: &types.GetAccentsReq{
				Page: 1,
				Size: 10,
			},
			expectedCode:      values.ErrorCodeSuccess,
			expectedItemCount: 3,
			expectedTotal:     3,
		},
		{
			name: "empty accents",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				// 设置 DB.GetAccents 的期望
				mockDB.EXPECT().
					GetAccents(gomock.Any(), &pg.GetAccentsParams{
						Page:     1,
						PageSize: 10,
					}).
					Return([]*pg.AiuVoiceAccentTable{}, nil)

				// 设置 DB.GetAccentsCount 的期望
				mockDB.EXPECT().
					GetAccentsCount(gomock.Any()).
					Return(int64(0), nil)
			},
			input: &types.GetAccentsReq{
				Page: 1,
				Size: 10,
			},
			expectedCode:      values.ErrorCodeSuccess,
			expectedItemCount: 0,
			expectedTotal:     0,
		},
		{
			name: "error on get accents",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				// 设置 DB.GetAccents 返回错误
				mockDB.EXPECT().
					GetAccents(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(500, "database error"))
			},
			input: &types.GetAccentsReq{
				Page: 1,
				Size: 10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get accents",
		},
		{
			name: "error on count",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				// 设置 DB.GetAccents 成功
				mockDB.EXPECT().
					GetAccents(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceAccentTable{}, nil)

				// 设置 DB.GetAccentsCount 返回错误
				mockDB.EXPECT().
					GetAccentsCount(gomock.Any()).
					Return(int64(0), xerrors.New(500, "database error"))
			},
			input: &types.GetAccentsReq{
				Page: 1,
				Size: 10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get accents count",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB := setupGetAccentsTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB)
			}

			// 执行测试
			resp, err := logic.GetAccents(tt.input)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证分页信息
				assert.Equal(t, tt.expectedTotal, resp.Data.Total)
				assert.Equal(t, tt.input.Page, resp.Data.Page)
				assert.Equal(t, tt.input.Size, resp.Data.Size)

				// 验证返回的口音列表长度
				assert.Equal(t, tt.expectedItemCount, len(resp.Data.Items))

				// 如果有返回口音，验证第一个口音
				if tt.expectedItemCount > 0 {
					assert.Equal(t, 1, resp.Data.Items[0].ID)
					assert.Equal(t, "American", resp.Data.Items[0].Name)
					assert.Equal(t, "Voice1", resp.Data.Items[0].VoiceName)
				}
			}
		})
	}
}
