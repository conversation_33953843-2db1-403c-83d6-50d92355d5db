package voice

import (
	"context"
	"math/rand"
	"time"

	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
)

type GetQuestionLogic struct {
	r      *rand.Rand
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取问题
func NewGetQuestionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetQuestionLogic {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	return &GetQuestionLogic{
		r:      r,
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

var questions = []string{
	"Hey, how about a little adventure today? " +
		"Let's head to the park, grab some snacks, " +
		"and just hang out. You bring some snacks, " +
		"I'll bring the blankets and drinks. We'll " +
		"find a good spot to kick back. Later, " +
		"maybe we could try out that new ice cream " +
		"place everyone's talking about. Sound good?",
}

func (l *GetQuestionLogic) GetQuestion(_ *types.GetQuestionReq) (resp *types.GetQuestionResp, err error) {
	// Get random question from questions array
	randomIndex := l.r.Intn(len(questions))
	question := questions[randomIndex]

	return &types.GetQuestionResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: question,
	}, nil
}
