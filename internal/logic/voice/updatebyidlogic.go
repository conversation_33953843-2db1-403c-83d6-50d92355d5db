package voice

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	xerrors "github.com/zeromicro/x/errors"
)

type UpdateByIDLogic struct {
	ctx       context.Context
	svcCtx    *svc.ServiceContext
	converter *converter
}

// 更新语音
func NewUpdateByIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateByIDLogic {
	return &UpdateByIDLogic{
		ctx:       ctx,
		svcCtx:    svcCtx,
		converter: newConverter(svcCtx),
	}
}

func (l *UpdateByIDLogic) UpdateByID(req *types.UpdateVoiceByIDReq) (*types.UpdateVoiceByIDResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	voice, err := l.svcCtx.DB.GetVoiceByID(l.ctx, int32(req.VoiceID))
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voice by id: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voice by id")
	}
	if voice == nil {
		return nil, xerrors.New(values.ErrorCodeNotFound, "voice not found")
	}

	if voice.CreatorUserId.Int64 != int64(servermsg.GetCurrentUser(l.ctx).UserID) {
		logc.Infof(l.ctx, "you are not the creator of this voice")
		return nil, xerrors.New(values.ErrorCodeForbidden, "you are not the creator of this voice")
	}

	// 更新语音
	voice, err = l.svcCtx.DB.UpdateVoice(l.ctx, &pg.UpdateVoiceParams{
		VoiceID:     int32(req.VoiceID),
		DisplayName: utils.ToPGText(req.UpdateInfo.DisplayName),
		Enable:      utils.ToPGBool(req.UpdateInfo.Enable),
		Public:      utils.ToPGBool(req.UpdateInfo.Public),
		AccentId:    utils.ToPGInt8(req.UpdateInfo.AccentID),
		Categories:  utils.CovertNumberSlice[int, int32](req.UpdateInfo.Categories),
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to update voice: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to update voice")
	}

	data, err := l.converter.BatchToVoiceWithLikeStatus(l.ctx, []*pg.GetVoicesWithLikeStatusRow{
		{AiuVoiceTable: *voice},
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to convert voice to voice info: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to convert voice to voice info")
	}

	return &types.UpdateVoiceByIDResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: data[0],
	}, nil
}
