package voice

import (
	"context"

	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/mr"
)

type converter struct {
	svcCtx *svc.ServiceContext
}

func newConverter(svcCtx *svc.ServiceContext) *converter {
	return &converter{
		svcCtx: svcCtx,
	}
}

// BatchToVoiceWithLikeStatus 批量转换为带点赞状态的语音信息
func (c *converter) BatchToVoiceWithLikeStatus(ctx context.Context,
	voices []*pg.GetVoicesWithLikeStatusRow) ([]*types.VoiceInfo, error) {

	if len(voices) == 0 {
		return nil, nil
	}

	rawVoices := lo.Map(voices, func(voice *pg.GetVoicesWithLikeStatusRow, _ int) *pg.AiuVoiceTable {
		return &voice.AiuVoiceTable
	})
	data, err := c.getBatchData(ctx, rawVoices)
	if err != nil {
		return nil, err
	}

	return c.buildBatchVoiceWithLikeStatus(ctx, voices, data)
}

type batchData struct {
	sampleURLMap map[string]string
	userMap      map[int]*dynamo.User
	categoryMap  map[int32]*pg.AiuVoiceCategory
	accentMap    map[int32]*pg.AiuVoiceAccentTable
}

func (c *converter) getBatchData(ctx context.Context, voices []*pg.AiuVoiceTable) (*batchData, error) {
	data := &batchData{}
	err := mr.Finish(
		func() error {
			var err error
			data.sampleURLMap, err = c.getBatchSampleURLs(ctx, voices)
			return err
		},
		func() error {
			var err error
			data.userMap, err = c.getBatchUsers(ctx, voices)
			return err
		},
		func() error {
			var err error
			data.categoryMap, err = c.getBatchCategories(ctx, voices)
			return err
		},
		func() error {
			var err error
			data.accentMap, err = c.getBatchAccents(ctx, voices)
			return err
		},
	)
	if err != nil {
		logc.Errorf(ctx, "failed to get batch data: %v", err)
		return nil, err
	}
	return data, nil
}

func (c *converter) getBatchSampleURLs(ctx context.Context, voices []*pg.AiuVoiceTable) (map[string]string, error) {
	sampleURLs := lo.Map(voices, func(voice *pg.AiuVoiceTable, _ int) string {
		return voice.SampleUrl.String
	})
	sampleURLMap, err := c.svcCtx.S3.BatchGetPresignedURL(ctx, sampleURLs, s3.Original)
	if err != nil {
		logc.Errorf(ctx, "failed to get presigned URLs: %v", err)
		return nil, err
	}
	return sampleURLMap, nil
}

func (c *converter) getBatchUsers(ctx context.Context, voices []*pg.AiuVoiceTable) (map[int]*dynamo.User, error) {
	voiceUserIDs := lo.Map(voices, func(voice *pg.AiuVoiceTable, _ int) int {
		return int(voice.CreatorUserId.Int64)
	})
	voiceUserIDs = lo.Uniq(voiceUserIDs)
	logc.Debugf(ctx, "voiceUserIDs: %v", voiceUserIDs)
	userMap, err := c.svcCtx.Dynamo.BatchGetUsers(ctx, voiceUserIDs)
	if err != nil {
		logc.Errorf(ctx, "failed to get users: %v", err)
		return nil, err
	}
	return userMap, nil
}

func (c *converter) getBatchCategories(ctx context.Context,
	voices []*pg.AiuVoiceTable) (map[int32]*pg.AiuVoiceCategory, error) {

	var categoryIDs []int32
	for _, voice := range voices {
		categoryIDs = append(categoryIDs, voice.Categories...)
	}
	categoryIDs = lo.Uniq(categoryIDs)
	logc.Debugf(ctx, "categoryIDs: %v", categoryIDs)
	categories, err := c.svcCtx.DB.GetVoiceCategoriesByIDs(ctx, categoryIDs)
	if err != nil {
		logc.Errorf(ctx, "failed to get voice categories: %v", err)
		return nil, err
	}
	return lo.SliceToMap(categories, func(category *pg.AiuVoiceCategory) (int32, *pg.AiuVoiceCategory) {
		return int32(category.ID), category
	}), nil
}

func (c *converter) getBatchAccents(ctx context.Context,
	voices []*pg.AiuVoiceTable) (map[int32]*pg.AiuVoiceAccentTable, error) {
	var accentIDs []int32
	for _, voice := range voices {
		if voice.AccentId.Int64 == 0 {
			continue
		}
		accentIDs = append(accentIDs, int32(voice.AccentId.Int64))
	}
	accentIDs = lo.Uniq(accentIDs)
	logc.Debugf(ctx, "accentIDs: %v", accentIDs)

	accents, err := c.svcCtx.DB.GetAccentsByIDs(ctx, accentIDs)
	if err != nil {
		logc.Errorf(ctx, "failed to get voice accents: %v", err)
		return nil, err
	}
	return lo.SliceToMap(accents, func(accent *pg.AiuVoiceAccentTable) (int32, *pg.AiuVoiceAccentTable) {
		return int32(accent.ID), accent
	}), nil
}

func (c *converter) buildBatchVoiceWithLikeStatus(ctx context.Context,
	voices []*pg.GetVoicesWithLikeStatusRow, data *batchData) ([]*types.VoiceInfo, error) {

	voiceInfos := make([]*types.VoiceInfo, 0, len(voices))
	for _, voice := range voices {
		public := false
		if voice.AiuVoiceTable.CreationType == 0 { // 用户创建的、能够展示的、没有删除的
			public = voice.AiuVoiceTable.DisplayEnable && voice.AiuVoiceTable.Enable.Bool
		}
		if voice.AiuVoiceTable.CreationType == 2 { // 官方声音，能够展示的
			public = voice.AiuVoiceTable.DisplayEnable
		}

		voiceInfos = append(voiceInfos, &types.VoiceInfo{
			VoiceID:            int(voice.AiuVoiceTable.VoiceId),
			DisplayName:        voice.AiuVoiceTable.DisplayName.String,
			SampleURL:          data.sampleURLMap[voice.AiuVoiceTable.SampleUrl.String],
			RecordText:         voice.AiuVoiceTable.RecordText.String,
			ProcessedRecordURL: voice.AiuVoiceTable.ProcessedRecordUrl.String,
			DisplayEnable:      voice.AiuVoiceTable.DisplayEnable,
			CharName:           voice.AiuVoiceTable.CharName.String,
			LabsTag:            voice.AiuVoiceTable.LabsTag,
			LabsID:             voice.AiuVoiceTable.LabsId.String,
			RvcTag:             voice.AiuVoiceTable.RvcTag,
			RvcID:              voice.AiuVoiceTable.RvcId.String,
			RvcTranspose:       int(voice.AiuVoiceTable.RvcTranspose.Int32),
			CreatorUserID:      int(voice.AiuVoiceTable.CreatorUserId.Int64),
			CreatorUserName: utils.GetWithDefault(
				data.userMap[int(voice.AiuVoiceTable.CreatorUserId.Int64)],
				&dynamo.User{},
			).UserName,
			CreationType: voice.AiuVoiceTable.CreationType,
			Enable:       voice.AiuVoiceTable.Enable.Bool,
			CreateTime:   voice.AiuVoiceTable.CreateTime.Int64,
			Duration:     int(voice.AiuVoiceTable.Duration.Int32),
			Public:       public,
			Nsfw:         voice.AiuVoiceTable.Nsfw,
			Liked:        voice.IsLiked,
			Categories:   c.getVoiceCategories(ctx, data.categoryMap, voice.AiuVoiceTable.Categories),
			Accent:       c.getVoiceAccent(ctx, data.accentMap, voice.AiuVoiceTable.AccentId.Int64),
		})
	}
	return voiceInfos, nil
}

func (c *converter) getVoiceCategories(ctx context.Context,
	categoryMap map[int32]*pg.AiuVoiceCategory, categories []int32) []*types.VoiceCategory {
	voiceCategories := make([]*types.VoiceCategory, 0, len(categories))
	for _, categoryID := range categories {
		category, ok := categoryMap[categoryID]
		if !ok {
			logc.Infof(ctx, "category not found: %v", categoryID)
			continue
		}
		voiceCategories = append(voiceCategories, &types.VoiceCategory{
			ID:       int(category.ID),
			Name:     category.Name,
			CoverURL: category.CoverUrl.String,
		})
	}
	return voiceCategories
}

func (c *converter) getVoiceAccent(_ context.Context,
	accentMap map[int32]*pg.AiuVoiceAccentTable, accentID int64) types.Accent {
	accent, ok := accentMap[int32(accentID)]
	if !ok {
		return types.Accent{}
	}
	return types.Accent{
		ID:        int(accent.ID),
		Name:      accent.AccentName,
		VoiceName: accent.VoiceName,
	}
}
