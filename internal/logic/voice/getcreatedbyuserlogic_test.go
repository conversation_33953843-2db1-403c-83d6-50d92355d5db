package voice

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupGetCreatedByUserTest(t *testing.T) (*gomock.Controller, *GetCreatedByUserLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewGetCreatedByUserLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3
}

func TestGetCreatedByUser(t *testing.T) {
	tests := []struct {
		name              string
		setupMocks        func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3)
		input             *types.GetVoiceCreatedByUserReq
		expectedCode      int
		expectedErrMsg    string
		expectedItemCount int
		expectedTotal     int
	}{
		{
			name: "success with user's own voices",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.GetVoicesByCreatorUserIDCount 的期望
				mockDB.EXPECT().
					GetVoicesByCreatorUserIDCount(gomock.Any(), &pg.GetVoicesByCreatorUserIDCountParams{
						UserID:                123,
						QueryUserID:           123,
						FilterByDisplayEnable: false, // 查看自己的声音，不过滤
					}).
					Return(int64(2), nil)

				// 设置 DB.GetVoicesByCreatorUserID 的期望
				mockDB.EXPECT().
					GetVoicesByCreatorUserID(gomock.Any(), &pg.GetVoicesByCreatorUserIDParams{
						UserID:                123,
						QueryUserID:           123,
						FilterByDisplayEnable: false,
						Page:                  1,
						PageSize:              10,
					}).
					Return([]*pg.GetVoicesByCreatorUserIDRow{
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       1,
								DisplayName:   pgtype.Text{String: "My Voice 1", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 180, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{1},
								Nsfw:          false,
							},
							IsLiked: false,
						},
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       2,
								DisplayName:   pgtype.Text{String: "My Voice 2", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice2.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 200, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{2},
								Nsfw:          false,
							},
							IsLiked: true,
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/voice1.mp3": "https://presigned.example.com/voice1.mp3",
						"http://example.com/voice2.mp3": "https://presigned.example.com/voice2.mp3",
					}, nil)

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "testuser"},
					}, nil)

				// 设置 GetVoiceCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
					}, nil)

				// 设置 GetAccentsByIDs 的期望
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceAccentTable{}, nil)
			},
			input: &types.GetVoiceCreatedByUserReq{
				UserID: 0, // 0表示查看自己的
				Page:   1,
				Size:   10,
			},
			expectedCode:      values.ErrorCodeSuccess,
			expectedItemCount: 2,
			expectedTotal:     2,
		},
		{
			name: "success with other user's voices",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.GetVoicesByCreatorUserIDCount 的期望
				mockDB.EXPECT().
					GetVoicesByCreatorUserIDCount(gomock.Any(), &pg.GetVoicesByCreatorUserIDCountParams{
						UserID:                123,
						QueryUserID:           456,
						FilterByDisplayEnable: true, // 查看别人的声音，需要过滤
					}).
					Return(int64(1), nil)

				// 设置 DB.GetVoicesByCreatorUserID 的期望
				mockDB.EXPECT().
					GetVoicesByCreatorUserID(gomock.Any(), &pg.GetVoicesByCreatorUserIDParams{
						UserID:                123,
						QueryUserID:           456,
						FilterByDisplayEnable: true,
						Page:                  1,
						PageSize:              10,
					}).
					Return([]*pg.GetVoicesByCreatorUserIDRow{
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       3,
								DisplayName:   pgtype.Text{String: "Other User Voice", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice3.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 150, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 456, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{1},
								Nsfw:          false,
							},
							IsLiked: false,
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/voice3.mp3": "https://presigned.example.com/voice3.mp3",
					}, nil)

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						456: {ID: 456, UserName: "otheruser"},
					}, nil)

				// 设置 GetVoiceCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
					}, nil)

				// 设置 GetAccentsByIDs 的期望
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceAccentTable{}, nil)
			},
			input: &types.GetVoiceCreatedByUserReq{
				UserID: 456,
				Page:   1,
				Size:   10,
			},
			expectedCode:      values.ErrorCodeSuccess,
			expectedItemCount: 1,
			expectedTotal:     1,
		},
		{
			name: "empty result",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetVoicesByCreatorUserIDCount 的期望
				mockDB.EXPECT().
					GetVoicesByCreatorUserIDCount(gomock.Any(), &pg.GetVoicesByCreatorUserIDCountParams{
						UserID:                123,
						QueryUserID:           456,
						FilterByDisplayEnable: true,
					}).
					Return(int64(0), nil)
			},
			input: &types.GetVoiceCreatedByUserReq{
				UserID: 456,
				Page:   1,
				Size:   10,
			},
			expectedCode:      values.ErrorCodeSuccess,
			expectedItemCount: 0,
			expectedTotal:     0,
		},
		{
			name: "error on count",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetVoicesByCreatorUserIDCount 返回错误
				mockDB.EXPECT().
					GetVoicesByCreatorUserIDCount(gomock.Any(), gomock.Any()).
					Return(int64(0), xerrors.New(500, "database error"))
			},
			input: &types.GetVoiceCreatedByUserReq{
				UserID: 456,
				Page:   1,
				Size:   10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get voices by creator user id count",
		},
		{
			name: "error on get voices",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetVoicesByCreatorUserIDCount 成功
				mockDB.EXPECT().
					GetVoicesByCreatorUserIDCount(gomock.Any(), gomock.Any()).
					Return(int64(1), nil)

				// 设置 DB.GetVoicesByCreatorUserID 返回错误
				mockDB.EXPECT().
					GetVoicesByCreatorUserID(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(500, "database error"))
			},
			input: &types.GetVoiceCreatedByUserReq{
				UserID: 456,
				Page:   1,
				Size:   10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get voices by creator user id",
		},
		{
			name: "error on converter",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.GetVoicesByCreatorUserIDCount 成功
				mockDB.EXPECT().
					GetVoicesByCreatorUserIDCount(gomock.Any(), gomock.Any()).
					Return(int64(1), nil)

				// 设置 DB.GetVoicesByCreatorUserID 成功
				mockDB.EXPECT().
					GetVoicesByCreatorUserID(gomock.Any(), gomock.Any()).
					Return([]*pg.GetVoicesByCreatorUserIDRow{
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       1,
								DisplayName:   pgtype.Text{String: "Voice 1", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 180, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{1},
								Nsfw:          false,
							},
							IsLiked: false,
						},
					}, nil)

				// 设置 BatchGetPresignedURL 返回错误
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(nil, xerrors.New(500, "s3 error"))

				// These may or may not be called depending on mr.Finish implementation
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "testuser"},
					}, nil).AnyTimes()

				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
					}, nil).AnyTimes()

				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceAccentTable{}, nil).AnyTimes()
			},
			input: &types.GetVoiceCreatedByUserReq{
				UserID: 456,
				Page:   1,
				Size:   10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "failed to convert voices to voice info",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3 := setupGetCreatedByUserTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3)
			}

			// 执行测试
			resp, err := logic.GetCreatedByUser(tt.input)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证分页信息
				assert.Equal(t, tt.expectedTotal, resp.Data.Total)
				assert.Equal(t, tt.input.Page, resp.Data.Page)
				assert.Equal(t, tt.input.Size, resp.Data.Size)

				// 验证返回的语音列表长度
				if tt.expectedItemCount > 0 {
					assert.Equal(t, tt.expectedItemCount, len(resp.Data.Items))
				} else {
					assert.Nil(t, resp.Data.Items)
				}
			}
		})
	}
}
