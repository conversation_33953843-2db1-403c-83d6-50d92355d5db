package voice

import (
	"context"
	"errors"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func setupConverterTest(t *testing.T) (*gomock.Controller, *converter, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	converter := newConverter(svcCtx)

	return ctrl, converter, mockDB, mockDynamo, mockS3
}

func createTestVoiceWithLikeStatus() *pg.GetVoicesWithLikeStatusRow {
	return &pg.GetVoicesWithLikeStatusRow{
		AiuVoiceTable: pg.AiuVoiceTable{
			VoiceId:            1,
			DisplayName:        pgtype.Text{String: "Test Voice", Valid: true},
			SampleUrl:          pgtype.Text{String: "http://example.com/voice.mp3", Valid: true},
			RecordText:         pgtype.Text{String: "Hello world", Valid: true},
			ProcessedRecordUrl: pgtype.Text{String: "http://example.com/processed.mp3", Valid: true},
			DisplayEnable:      true,
			CharName:           pgtype.Text{String: "Test Character", Valid: true},
			LabsTag:            []string{"tag1", "tag2"},
			LabsId:             pgtype.Text{String: "labs123", Valid: true},
			RvcTag:             []string{"rvc1", "rvc2"},
			RvcId:              pgtype.Text{String: "rvc123", Valid: true},
			RvcTranspose:       pgtype.Int4{Int32: 2, Valid: true},
			CreatorUserId:      pgtype.Int8{Int64: 123, Valid: true},
			CreationType:       0,
			Enable:             pgtype.Bool{Bool: true, Valid: true},
			CreateTime:         pgtype.Int8{Int64: 1640995200, Valid: true},
			Duration:           pgtype.Int4{Int32: 180, Valid: true},
			Categories:         []int32{1, 2},
			Nsfw:               false,
			AccentId:           pgtype.Int8{Int64: 1, Valid: true},
		},
		IsLiked: true,
	}
}

func TestConverter_BatchToVoiceWithLikeStatus(t *testing.T) {
	tests := []struct {
		name       string
		voices     []*pg.GetVoicesWithLikeStatusRow
		setupMocks func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3)
		wantErr    bool
		wantCount  int
	}{
		{
			name:   "empty voices list",
			voices: []*pg.GetVoicesWithLikeStatusRow{},
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// No mocks needed for empty list
			},
			wantErr:   false,
			wantCount: 0,
		},
		{
			name: "successful conversion with user created voice",
			voices: []*pg.GetVoicesWithLikeStatusRow{
				createTestVoiceWithLikeStatus(),
			},
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// Mock S3 batch get presigned URLs
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/voice.mp3"}, s3.Original).
					Return(map[string]string{
						"http://example.com/voice.mp3": "https://presigned.example.com/voice.mp3",
					}, nil)

				// Mock Dynamo batch get users
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), []int{123}).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "testuser"},
					}, nil)

				// Mock DB get categories
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), []int32{1, 2}).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
					}, nil)

				// Mock DB get accents
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), []int32{1}).
					Return([]*pg.AiuVoiceAccentTable{
						{ID: 1, AccentName: "Test Accent", VoiceName: "Test Voice"},
					}, nil)
			},
			wantErr:   false,
			wantCount: 1,
		},
		{
			name: "successful conversion with official voice",
			voices: []*pg.GetVoicesWithLikeStatusRow{
				func() *pg.GetVoicesWithLikeStatusRow {
					voice := createTestVoiceWithLikeStatus()
					voice.AiuVoiceTable.CreationType = 2 // Official voice
					return voice
				}(),
			},
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// Mock S3 batch get presigned URLs
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/voice.mp3"}, s3.Original).
					Return(map[string]string{
						"http://example.com/voice.mp3": "https://presigned.example.com/voice.mp3",
					}, nil)

				// Mock Dynamo batch get users
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), []int{123}).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "testuser"},
					}, nil)

				// Mock DB get categories
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), []int32{1, 2}).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
					}, nil)

				// Mock DB get accents
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), []int32{1}).
					Return([]*pg.AiuVoiceAccentTable{
						{ID: 1, AccentName: "Test Accent", VoiceName: "Test Voice"},
					}, nil)
			},
			wantErr:   false,
			wantCount: 1,
		},
		{
			name: "voice without accent",
			voices: []*pg.GetVoicesWithLikeStatusRow{
				func() *pg.GetVoicesWithLikeStatusRow {
					voice := createTestVoiceWithLikeStatus()
					voice.AiuVoiceTable.AccentId = pgtype.Int8{Int64: 0, Valid: false}
					return voice
				}(),
			},
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// Mock S3 batch get presigned URLs
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/voice.mp3"}, s3.Original).
					Return(map[string]string{
						"http://example.com/voice.mp3": "https://presigned.example.com/voice.mp3",
					}, nil)

				// Mock Dynamo batch get users
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), []int{123}).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "testuser"},
					}, nil)

				// Mock DB get categories
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), []int32{1, 2}).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
					}, nil)

				// Mock DB get accents - should not be called for accent ID 0
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), []int32{}).
					Return([]*pg.AiuVoiceAccentTable{}, nil)
			},
			wantErr:   false,
			wantCount: 1,
		},
		{
			name: "S3 error",
			voices: []*pg.GetVoicesWithLikeStatusRow{
				createTestVoiceWithLikeStatus(),
			},
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// Mock S3 batch get presigned URLs with error
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/voice.mp3"}, s3.Original).
					Return(nil, errors.New("S3 error"))

				// These may or may not be called depending on mr.Finish implementation
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), []int{123}).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "testuser"},
					}, nil).AnyTimes()

				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), []int32{1, 2}).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
					}, nil).AnyTimes()

				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), []int32{1}).
					Return([]*pg.AiuVoiceAccentTable{
						{ID: 1, AccentName: "Test Accent", VoiceName: "Test Voice"},
					}, nil).AnyTimes()
			},
			wantErr:   true,
			wantCount: 0,
		},
		{
			name: "Dynamo error",
			voices: []*pg.GetVoicesWithLikeStatusRow{
				createTestVoiceWithLikeStatus(),
			},
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// Mock S3 batch get presigned URLs
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/voice.mp3"}, s3.Original).
					Return(map[string]string{
						"http://example.com/voice.mp3": "https://presigned.example.com/voice.mp3",
					}, nil).AnyTimes()

				// Mock Dynamo batch get users with error
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), []int{123}).
					Return(nil, errors.New("Dynamo error"))

				// These may or may not be called depending on mr.Finish implementation
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), []int32{1, 2}).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
					}, nil).AnyTimes()

				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), []int32{1}).
					Return([]*pg.AiuVoiceAccentTable{
						{ID: 1, AccentName: "Test Accent", VoiceName: "Test Voice"},
					}, nil).AnyTimes()
			},
			wantErr:   true,
			wantCount: 0,
		},
		{
			name: "DB categories error",
			voices: []*pg.GetVoicesWithLikeStatusRow{
				createTestVoiceWithLikeStatus(),
			},
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// Mock S3 batch get presigned URLs
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/voice.mp3"}, s3.Original).
					Return(map[string]string{
						"http://example.com/voice.mp3": "https://presigned.example.com/voice.mp3",
					}, nil).AnyTimes()

				// Mock Dynamo batch get users
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), []int{123}).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "testuser"},
					}, nil).AnyTimes()

				// Mock DB get categories with error
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), []int32{1, 2}).
					Return(nil, errors.New("DB error"))

				// This may or may not be called depending on mr.Finish implementation
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), []int32{1}).
					Return([]*pg.AiuVoiceAccentTable{
						{ID: 1, AccentName: "Test Accent", VoiceName: "Test Voice"},
					}, nil).AnyTimes()
			},
			wantErr:   true,
			wantCount: 0,
		},
		{
			name: "DB accents error",
			voices: []*pg.GetVoicesWithLikeStatusRow{
				createTestVoiceWithLikeStatus(),
			},
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// Mock S3 batch get presigned URLs
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/voice.mp3"}, s3.Original).
					Return(map[string]string{
						"http://example.com/voice.mp3": "https://presigned.example.com/voice.mp3",
					}, nil).AnyTimes()

				// Mock Dynamo batch get users
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), []int{123}).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "testuser"},
					}, nil).AnyTimes()

				// Mock DB get categories - might not be called due to concurrent execution
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), []int32{1, 2}).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
					}, nil).AnyTimes()

				// Mock DB get accents with error
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), []int32{1}).
					Return(nil, errors.New("DB error"))
			},
			wantErr:   true,
			wantCount: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl, converter, mockDB, mockDynamo, mockS3 := setupConverterTest(t)
			defer ctrl.Finish()

			tt.setupMocks(mockDB, mockDynamo, mockS3)

			ctx := context.Background()
			result, err := converter.BatchToVoiceWithLikeStatus(ctx, tt.voices)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Len(t, result, tt.wantCount)

				if tt.wantCount > 0 {
					voice := result[0]
					assert.Equal(t, 1, voice.VoiceID)
					assert.Equal(t, "Test Voice", voice.DisplayName)
					assert.Equal(t, "https://presigned.example.com/voice.mp3", voice.SampleURL)
					assert.Equal(t, "Hello world", voice.RecordText)
					assert.Equal(t, "testuser", voice.CreatorUserName)
					assert.True(t, voice.Liked)

					// Check public status based on creation type
					if tt.voices[0].AiuVoiceTable.CreationType == 0 {
						// User created voice: public = displayEnable && enable
						expectedPublic := tt.voices[0].AiuVoiceTable.DisplayEnable && tt.voices[0].AiuVoiceTable.Enable.Bool
						assert.Equal(t, expectedPublic, voice.Public)
					} else if tt.voices[0].AiuVoiceTable.CreationType == 2 {
						// Official voice: public = displayEnable
						assert.Equal(t, tt.voices[0].AiuVoiceTable.DisplayEnable, voice.Public)
					}

					// Check categories
					if len(tt.voices[0].AiuVoiceTable.Categories) > 0 {
						assert.Len(t, voice.Categories, 2)
						assert.Equal(t, "Category 1", voice.Categories[0].Name)
						assert.Equal(t, "Category 2", voice.Categories[1].Name)
					}

					// Check accent
					if tt.voices[0].AiuVoiceTable.AccentId.Int64 > 0 {
						assert.Equal(t, 1, voice.Accent.ID)
						assert.Equal(t, "Test Accent", voice.Accent.Name)
						assert.Equal(t, "Test Voice", voice.Accent.VoiceName)
					} else {
						assert.Equal(t, types.Accent{}, voice.Accent)
					}
				}
			}
		})
	}
}

func TestConverter_getBatchSampleURLs(t *testing.T) {
	tests := []struct {
		name       string
		voices     []*pg.AiuVoiceTable
		setupMocks func(*s3mock.MockS3)
		wantErr    bool
		wantURLs   map[string]string
	}{
		{
			name: "successful get sample URLs",
			voices: []*pg.AiuVoiceTable{
				{SampleUrl: pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true}},
				{SampleUrl: pgtype.Text{String: "http://example.com/voice2.mp3", Valid: true}},
			},
			setupMocks: func(mockS3 *s3mock.MockS3) {
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/voice1.mp3", "http://example.com/voice2.mp3"}, s3.Original).
					Return(map[string]string{
						"http://example.com/voice1.mp3": "https://presigned1.example.com/voice1.mp3",
						"http://example.com/voice2.mp3": "https://presigned2.example.com/voice2.mp3",
					}, nil)
			},
			wantErr: false,
			wantURLs: map[string]string{
				"http://example.com/voice1.mp3": "https://presigned1.example.com/voice1.mp3",
				"http://example.com/voice2.mp3": "https://presigned2.example.com/voice2.mp3",
			},
		},
		{
			name: "S3 error",
			voices: []*pg.AiuVoiceTable{
				{SampleUrl: pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true}},
			},
			setupMocks: func(mockS3 *s3mock.MockS3) {
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), []string{"http://example.com/voice1.mp3"}, s3.Original).
					Return(nil, errors.New("S3 error"))
			},
			wantErr:  true,
			wantURLs: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl, converter, _, _, mockS3 := setupConverterTest(t)
			defer ctrl.Finish()

			tt.setupMocks(mockS3)

			ctx := context.Background()
			result, err := converter.getBatchSampleURLs(ctx, tt.voices)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantURLs, result)
			}
		})
	}
}

func TestConverter_getBatchUsers(t *testing.T) {
	tests := []struct {
		name       string
		voices     []*pg.AiuVoiceTable
		setupMocks func(*dynamomock.MockDB)
		wantErr    bool
		wantUsers  map[int]*dynamo.User
	}{
		{
			name: "successful get users",
			voices: []*pg.AiuVoiceTable{
				{CreatorUserId: pgtype.Int8{Int64: 123, Valid: true}},
				{CreatorUserId: pgtype.Int8{Int64: 456, Valid: true}},
				{CreatorUserId: pgtype.Int8{Int64: 123, Valid: true}}, // Duplicate should be deduplicated
			},
			setupMocks: func(mockDynamo *dynamomock.MockDB) {
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), []int{123, 456}).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "user123"},
						456: {ID: 456, UserName: "user456"},
					}, nil)
			},
			wantErr: false,
			wantUsers: map[int]*dynamo.User{
				123: {ID: 123, UserName: "user123"},
				456: {ID: 456, UserName: "user456"},
			},
		},
		{
			name: "Dynamo error",
			voices: []*pg.AiuVoiceTable{
				{CreatorUserId: pgtype.Int8{Int64: 123, Valid: true}},
			},
			setupMocks: func(mockDynamo *dynamomock.MockDB) {
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), []int{123}).
					Return(nil, errors.New("Dynamo error"))
			},
			wantErr:   true,
			wantUsers: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl, converter, _, mockDynamo, _ := setupConverterTest(t)
			defer ctrl.Finish()

			tt.setupMocks(mockDynamo)

			ctx := context.Background()
			result, err := converter.getBatchUsers(ctx, tt.voices)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantUsers, result)
			}
		})
	}
}

func TestConverter_getBatchCategories(t *testing.T) {
	tests := []struct {
		name           string
		voices         []*pg.AiuVoiceTable
		setupMocks     func(*pgmock.MockQuerier)
		wantErr        bool
		wantCategories map[int32]*pg.AiuVoiceCategory
	}{
		{
			name: "successful get categories",
			voices: []*pg.AiuVoiceTable{
				{Categories: []int32{1, 2}},
				{Categories: []int32{2, 3}},
				{Categories: []int32{1}}, // Duplicate should be deduplicated
			},
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), []int32{1, 2, 3}).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
						{ID: 3, Name: "Category 3", CoverUrl: pgtype.Text{String: "http://example.com/cat3.jpg", Valid: true}},
					}, nil)
			},
			wantErr: false,
			wantCategories: map[int32]*pg.AiuVoiceCategory{
				1: {ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
				2: {ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
				3: {ID: 3, Name: "Category 3", CoverUrl: pgtype.Text{String: "http://example.com/cat3.jpg", Valid: true}},
			},
		},
		{
			name: "DB error",
			voices: []*pg.AiuVoiceTable{
				{Categories: []int32{1, 2}},
			},
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), []int32{1, 2}).
					Return(nil, errors.New("DB error"))
			},
			wantErr:        true,
			wantCategories: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl, converter, mockDB, _, _ := setupConverterTest(t)
			defer ctrl.Finish()

			tt.setupMocks(mockDB)

			ctx := context.Background()
			result, err := converter.getBatchCategories(ctx, tt.voices)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantCategories, result)
			}
		})
	}
}

func TestConverter_getBatchAccents(t *testing.T) {
	tests := []struct {
		name        string
		voices      []*pg.AiuVoiceTable
		setupMocks  func(*pgmock.MockQuerier)
		wantErr     bool
		wantAccents map[int32]*pg.AiuVoiceAccentTable
	}{
		{
			name: "successful get accents",
			voices: []*pg.AiuVoiceTable{
				{AccentId: pgtype.Int8{Int64: 1, Valid: true}},
				{AccentId: pgtype.Int8{Int64: 2, Valid: true}},
				{AccentId: pgtype.Int8{Int64: 0, Valid: false}}, // Should be skipped
				{AccentId: pgtype.Int8{Int64: 1, Valid: true}},  // Duplicate should be deduplicated
			},
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), []int32{1, 2}).
					Return([]*pg.AiuVoiceAccentTable{
						{ID: 1, AccentName: "Accent 1", VoiceName: "Voice 1"},
						{ID: 2, AccentName: "Accent 2", VoiceName: "Voice 2"},
					}, nil)
			},
			wantErr: false,
			wantAccents: map[int32]*pg.AiuVoiceAccentTable{
				1: {ID: 1, AccentName: "Accent 1", VoiceName: "Voice 1"},
				2: {ID: 2, AccentName: "Accent 2", VoiceName: "Voice 2"},
			},
		},
		{
			name: "no accents (all zero)",
			voices: []*pg.AiuVoiceTable{
				{AccentId: pgtype.Int8{Int64: 0, Valid: false}},
				{AccentId: pgtype.Int8{Int64: 0, Valid: false}},
			},
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), []int32{}).
					Return([]*pg.AiuVoiceAccentTable{}, nil)
			},
			wantErr:     false,
			wantAccents: map[int32]*pg.AiuVoiceAccentTable{},
		},
		{
			name: "DB error",
			voices: []*pg.AiuVoiceTable{
				{AccentId: pgtype.Int8{Int64: 1, Valid: true}},
			},
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), []int32{1}).
					Return(nil, errors.New("DB error"))
			},
			wantErr:     true,
			wantAccents: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl, converter, mockDB, _, _ := setupConverterTest(t)
			defer ctrl.Finish()

			tt.setupMocks(mockDB)

			ctx := context.Background()
			result, err := converter.getBatchAccents(ctx, tt.voices)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantAccents, result)
			}
		})
	}
}

func TestConverter_getVoiceCategories(t *testing.T) {
	tests := []struct {
		name           string
		categoryMap    map[int32]*pg.AiuVoiceCategory
		categories     []int32
		wantCategories []*types.VoiceCategory
	}{
		{
			name: "successful get voice categories",
			categoryMap: map[int32]*pg.AiuVoiceCategory{
				1: {ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
				2: {ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
			},
			categories: []int32{1, 2},
			wantCategories: []*types.VoiceCategory{
				{ID: 1, Name: "Category 1", CoverURL: "http://example.com/cat1.jpg"},
				{ID: 2, Name: "Category 2", CoverURL: "http://example.com/cat2.jpg"},
			},
		},
		{
			name: "category not found in map",
			categoryMap: map[int32]*pg.AiuVoiceCategory{
				1: {ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
			},
			categories: []int32{1, 999}, // 999 doesn't exist in map
			wantCategories: []*types.VoiceCategory{
				{ID: 1, Name: "Category 1", CoverURL: "http://example.com/cat1.jpg"},
			},
		},
		{
			name:           "empty categories",
			categoryMap:    map[int32]*pg.AiuVoiceCategory{},
			categories:     []int32{},
			wantCategories: []*types.VoiceCategory{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl, converter, _, _, _ := setupConverterTest(t)
			defer ctrl.Finish()

			ctx := context.Background()
			result := converter.getVoiceCategories(ctx, tt.categoryMap, tt.categories)

			assert.Equal(t, tt.wantCategories, result)
		})
	}
}

func TestConverter_getVoiceAccent(t *testing.T) {
	tests := []struct {
		name       string
		accentMap  map[int32]*pg.AiuVoiceAccentTable
		accentID   int64
		wantAccent types.Accent
	}{
		{
			name: "successful get voice accent",
			accentMap: map[int32]*pg.AiuVoiceAccentTable{
				1: {ID: 1, AccentName: "Test Accent", VoiceName: "Test Voice"},
			},
			accentID: 1,
			wantAccent: types.Accent{
				ID:        1,
				Name:      "Test Accent",
				VoiceName: "Test Voice",
			},
		},
		{
			name:       "accent not found in map",
			accentMap:  map[int32]*pg.AiuVoiceAccentTable{},
			accentID:   999,
			wantAccent: types.Accent{},
		},
		{
			name: "accent ID zero",
			accentMap: map[int32]*pg.AiuVoiceAccentTable{
				1: {ID: 1, AccentName: "Test Accent", VoiceName: "Test Voice"},
			},
			accentID:   0,
			wantAccent: types.Accent{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl, converter, _, _, _ := setupConverterTest(t)
			defer ctrl.Finish()

			ctx := context.Background()
			result := converter.getVoiceAccent(ctx, tt.accentMap, tt.accentID)

			assert.Equal(t, tt.wantAccent, result)
		})
	}
}

func TestNewConverter(t *testing.T) {
	svcCtx := &svc.ServiceContext{}
	converter := newConverter(svcCtx)

	assert.NotNil(t, converter)
	assert.Equal(t, svcCtx, converter.svcCtx)
}
