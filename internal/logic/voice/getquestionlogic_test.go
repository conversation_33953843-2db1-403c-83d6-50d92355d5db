package voice

import (
	"context"
	"testing"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
)

func setupGetQuestionTest(_ *testing.T) (*GetQuestionLogic, context.Context) {
	svcCtx := &svc.ServiceContext{}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewGetQuestionLogic(ctx, svcCtx)

	return logic, ctx
}

func TestGetQuestion(t *testing.T) {
	tests := []struct {
		name         string
		input        *types.GetQuestionReq
		expectedCode int
	}{
		{
			name:         "success",
			input:        &types.GetQuestionReq{},
			expectedCode: values.ErrorCodeSuccess,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试环境
			logic, _ := setupGetQuestionTest(t)

			// 执行测试
			resp, err := logic.GetQuestion(tt.input)

			// 验证测试结果
			assert.NoError(t, err)
			assert.NotNil(t, resp)
			assert.Equal(t, tt.expectedCode, resp.Code)
			assert.Equal(t, "success", resp.Msg)

			// 验证返回的问题不为空
			assert.NotEmpty(t, resp.Data)

			// 验证返回的问题是预定义问题中的一个
			found := false
			for _, question := range questions {
				if resp.Data == question {
					found = true
					break
				}
			}
			assert.True(t, found, "Returned question should be one of the predefined questions")
		})
	}

	// 测试多次调用确保随机性
	t.Run("randomness test", func(t *testing.T) {
		logic, _ := setupGetQuestionTest(t)

		// 由于只有一个问题，每次都应该返回相同的问题
		// 但我们仍然测试多次调用不会出错
		for i := 0; i < 10; i++ {
			resp, err := logic.GetQuestion(&types.GetQuestionReq{})
			assert.NoError(t, err)
			assert.NotNil(t, resp)
			assert.Equal(t, values.ErrorCodeSuccess, resp.Code)
			assert.NotEmpty(t, resp.Data)
		}
	})
}
