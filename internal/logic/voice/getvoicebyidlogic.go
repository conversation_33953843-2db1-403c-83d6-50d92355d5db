package voice

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/zeromicro/go-zero/core/logc"
	xerrors "github.com/zeromicro/x/errors"
)

type GetVoiceByIDLogic struct {
	ctx       context.Context
	svcCtx    *svc.ServiceContext
	converter *converter
}

// 获取语音详情
func NewGetVoiceByIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetVoiceByIDLogic {
	return &GetVoiceByIDLogic{
		ctx:       ctx,
		svcCtx:    svcCtx,
		converter: newConverter(svcCtx),
	}
}

func (l *GetVoiceByIDLogic) GetVoiceByID(req *types.GetVoiceByIDReq) (*types.GetVoiceByIDResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	voices, err := l.svcCtx.DB.GetVoicesWithLikeStatus(l.ctx, &pg.GetVoicesWithLikeStatusParams{
		VoiceIds:              []int32{int32(req.VoiceID)},
		UserID:                int64(servermsg.GetCurrentUser(l.ctx).UserID),
		FilterByDisplayEnable: false,
		FilterByEnable:        false,
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voice by id: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voice by id")
	}
	if len(voices) == 0 {
		return nil, xerrors.New(values.ErrorCodeNotFound, "voice not found")
	}

	voicesInfo, err := l.converter.BatchToVoiceWithLikeStatus(l.ctx, voices)
	if err != nil {
		logc.Errorf(l.ctx, "failed to convert voice to voice info: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to convert voice to voice info")
	}

	if len(voicesInfo) == 0 {
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to convert voice info")
	}

	return &types.GetVoiceByIDResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: voicesInfo[0],
	}, nil
}
