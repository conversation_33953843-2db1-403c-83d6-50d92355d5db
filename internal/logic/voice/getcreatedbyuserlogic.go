package voice

import (
	"context"

	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	xerrors "github.com/zeromicro/x/errors"
)

type GetCreatedByUserLogic struct {
	ctx       context.Context
	svcCtx    *svc.ServiceContext
	converter *converter
}

// 获取用户创建的语音
func NewGetCreatedByUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCreatedByUserLogic {
	return &GetCreatedByUserLogic{
		ctx:       ctx,
		svcCtx:    svcCtx,
		converter: newConverter(svcCtx),
	}
}

func (l *GetCreatedByUserLogic) GetCreatedByUser(
	req *types.GetVoiceCreatedByUserReq) (*types.GetVoiceCreatedByUserResp, error) {
	// 如果用户ID为0，则使用当前用户ID
	if req.UserID == 0 {
		req.UserID = servermsg.GetCurrentUser(l.ctx).UserID
	}
	logc.Infof(l.ctx, "req: %+v", req)

	count, err := l.svcCtx.DB.GetVoicesByCreatorUserIDCount(l.ctx, &pg.GetVoicesByCreatorUserIDCountParams{
		UserID:                int64(servermsg.GetCurrentUser(l.ctx).UserID),
		QueryUserID:           int64(req.UserID),
		FilterByDisplayEnable: req.UserID != servermsg.GetCurrentUser(l.ctx).UserID,
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voices by creator user id count: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voices by creator user id count")
	}

	if count == 0 {
		return &types.GetVoiceCreatedByUserResp{
			ResponseBase: types.ResponseBase{
				Code: values.ErrorCodeSuccess,
				Msg:  "success",
			},
			Data: &types.VoicePagination{
				Pagination: utils.GetPageInfo(0, req.Page, req.Size),
			},
		}, nil
	}

	voices, err := l.svcCtx.DB.GetVoicesByCreatorUserID(l.ctx, &pg.GetVoicesByCreatorUserIDParams{
		UserID:                int64(servermsg.GetCurrentUser(l.ctx).UserID),
		QueryUserID:           int64(req.UserID),
		FilterByDisplayEnable: req.UserID != servermsg.GetCurrentUser(l.ctx).UserID,
		PageSize:              int32(req.Size),
		Page:                  int32(req.Page),
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voices by creator user id: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voices by creator user id")
	}

	voicesInfo, err := l.converter.BatchToVoiceWithLikeStatus(l.ctx,
		lo.Map(voices, func(item *pg.GetVoicesByCreatorUserIDRow, _ int) *pg.GetVoicesWithLikeStatusRow {
			return &pg.GetVoicesWithLikeStatusRow{
				AiuVoiceTable: item.AiuVoiceTable,
				IsLiked:       item.IsLiked,
			}
		}),
	)
	if err != nil {
		logc.Errorf(l.ctx, "failed to convert voices to voice info: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to convert voices to voice info")
	}
	logc.Infof(l.ctx, "voicesInfo: %+v", voicesInfo)

	return &types.GetVoiceCreatedByUserResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: &types.VoicePagination{
			Pagination: utils.GetPageInfo(int(count), req.Page, req.Size),
			Items:      voicesInfo,
		},
	}, nil
}
