package voice

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	xerrors "github.com/zeromicro/x/errors"
)

type GetAccentsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取口音
func NewGetAccentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAccentsLogic {
	return &GetAccentsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAccentsLogic) GetAccents(req *types.GetAccentsReq) (*types.GetAccentsResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	// Get accents from database
	ctx := l.ctx
	accents, err := l.svcCtx.DB.GetAccents(ctx, &pg.GetAccentsParams{
		PageSize: int32(req.Size),
		Page:     int32(req.Page),
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to get accents: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get accents")
	}

	// Get total count for pagination
	count, err := l.svcCtx.DB.GetAccentsCount(ctx)
	if err != nil {
		logc.Errorf(l.ctx, "failed to get accents count: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get accents count")
	}

	// Convert database records to response types
	var items []*types.Accent
	for _, accent := range accents {
		items = append(items, &types.Accent{
			ID:        int(accent.ID),
			Name:      accent.AccentName,
			VoiceName: accent.VoiceName,
		})
	}

	return &types.GetAccentsResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: &types.GetAccentsPagination{
			Pagination: utils.GetPageInfo(int(count), req.Page, req.Size),
			Items:      items,
		},
	}, nil
}
