package voice

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type LikeVoiceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 点赞/取消点赞语音，代替原来 likeVoice、unlikeVoice 两个接口
func NewLikeVoiceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LikeVoiceLogic {
	return &LikeVoiceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LikeVoiceLogic) LikeVoice(req *types.LikeVoiceReq) (*types.LikeVoiceResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	voice, err := l.svcCtx.DB.GetVoiceByID(l.ctx, int32(req.VoiceID))
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voice by id: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voice by id")
	}

	if voice == nil {
		return nil, xerrors.New(values.ErrorCodeNotFound, "voice not found")
	}

	var effectRows int64
	if req.Like {
		logc.Infof(l.ctx, "like voice: %v", req.VoiceID)
		effectRows, err = l.svcCtx.DB.UpsertUserVoiceLike(l.ctx, &pg.UpsertUserVoiceLikeParams{
			UserID:  int64(servermsg.GetCurrentUser(l.ctx).UserID),
			VoiceID: int32(req.VoiceID),
		})
	} else {
		logc.Infof(l.ctx, "unlike voice: %v", req.VoiceID)
		effectRows, err = l.svcCtx.DB.DeleteUserVoiceLike(l.ctx, &pg.DeleteUserVoiceLikeParams{
			UserID:  int64(servermsg.GetCurrentUser(l.ctx).UserID),
			VoiceID: int32(req.VoiceID),
		})
	}
	if err != nil {
		logc.Errorf(l.ctx, "failed to like/unlike voice: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to like/unlike voice")
	}
	if effectRows == 0 {
		return nil, xerrors.New(values.ErrorCodeNotFound, "liked voice not found")
	}

	return &types.LikeVoiceResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
	}, nil
}
