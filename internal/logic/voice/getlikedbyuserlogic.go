package voice

import (
	"context"

	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/zeromicro/go-zero/core/logc"
	xerrors "github.com/zeromicro/x/errors"
)

type GetLikedByUserLogic struct {
	ctx       context.Context
	svcCtx    *svc.ServiceContext
	converter *converter
}

// 获取用户点赞的语音
func NewGetLikedByUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLikedByUserLogic {
	return &GetLikedByUserLogic{
		ctx:       ctx,
		svcCtx:    svcCtx,
		converter: newConverter(svcCtx),
	}
}

func (l *GetLikedByUserLogic) GetLikedByUser(req *types.GetLikedByUserReq) (*types.GetLikedByUserResp, error) {
	if req.UserID == 0 {
		req.UserID = servermsg.GetCurrentUser(l.ctx).UserID
	}
	logc.Infof(l.ctx, "req: %+v", req)

	count, err := l.svcCtx.DB.GetVoicesLikedByUserIDCount(l.ctx, &pg.GetVoicesLikedByUserIDCountParams{
		UserID:      int64(servermsg.GetCurrentUser(l.ctx).UserID),
		QueryUserID: int64(req.UserID),
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voices liked by user id count: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voices liked by user id count")
	}

	if count == 0 {
		return &types.GetLikedByUserResp{
			ResponseBase: types.ResponseBase{
				Code: values.ErrorCodeSuccess,
				Msg:  "success",
			},
			Data: &types.VoicePagination{
				Pagination: utils.GetPageInfo(0, req.Page, req.Size),
			},
		}, nil
	}

	voices, err := l.svcCtx.DB.GetVoicesLikedByUserID(l.ctx, &pg.GetVoicesLikedByUserIDParams{
		UserID:      int64(servermsg.GetCurrentUser(l.ctx).UserID),
		QueryUserID: int64(req.UserID),
		PageSize:    int32(req.Size),
		Page:        int32(req.Page),
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voices liked by user id: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voices liked by user id")
	}

	voicesInfo, err := l.converter.BatchToVoiceWithLikeStatus(l.ctx,
		lo.Map(voices, func(item *pg.AiuVoiceTable, _ int) *pg.GetVoicesWithLikeStatusRow {
			return &pg.GetVoicesWithLikeStatusRow{
				AiuVoiceTable: *item,
				IsLiked:       true, // 查询出来的语音一定被点赞了的
			}
		}),
	)
	if err != nil {
		logc.Errorf(l.ctx, "failed to convert voices to voice info: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to convert voices to voice info")
	}

	return &types.GetLikedByUserResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: &types.VoicePagination{
			Pagination: utils.GetPageInfo(int(count), req.Page, req.Size),
			Items:      voicesInfo,
		},
	}, nil
}
