package voice

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupGetByCategoryTest(t *testing.T) (*gomock.Controller, *GetByCategoryLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewGetByCategoryLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3
}

func TestGetByCategory(t *testing.T) {
	tests := []struct {
		name              string
		setupMocks        func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3)
		input             *types.GetByCategoryReq
		expectedCode      int
		expectedErrMsg    string
		expectedItemCount int
		expectedTotal     int
	}{
		{
			name: "success with voices",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.GetVoicesByCategoryCount 的期望
				mockDB.EXPECT().
					GetVoicesByCategoryCount(gomock.Any(), &pg.GetVoicesByCategoryCountParams{
						UserID:     123,
						CategoryID: 1,
					}).
					Return(int64(2), nil)

				// 设置 DB.GetVoicesByCategory 的期望
				mockDB.EXPECT().
					GetVoicesByCategory(gomock.Any(), &pg.GetVoicesByCategoryParams{
						UserID:     123,
						CategoryID: 1,
						Page:       1,
						PageSize:   10,
					}).
					Return([]*pg.GetVoicesByCategoryRow{
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       1,
								DisplayName:   pgtype.Text{String: "Voice 1", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 180, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{1},
								Nsfw:          false,
							},
							IsLiked: false,
						},
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       2,
								DisplayName:   pgtype.Text{String: "Voice 2", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice2.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 200, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 456, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{1},
								Nsfw:          false,
							},
							IsLiked: true,
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/voice1.mp3": "https://presigned.example.com/voice1.mp3",
						"http://example.com/voice2.mp3": "https://presigned.example.com/voice2.mp3",
					}, nil)

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "creator1"},
						456: {ID: 456, UserName: "creator2"},
					}, nil)

				// 设置 GetVoiceCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
					}, nil)

				// 设置 GetAccentsByIDs 的期望
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceAccentTable{}, nil)
			},
			input: &types.GetByCategoryReq{
				CategoryID: 1,
				Page:       1,
				Size:       10,
			},
			expectedCode:      values.ErrorCodeSuccess,
			expectedItemCount: 2,
			expectedTotal:     2,
		},
		{
			name: "empty category",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetVoicesByCategoryCount 的期望
				mockDB.EXPECT().
					GetVoicesByCategoryCount(gomock.Any(), &pg.GetVoicesByCategoryCountParams{
						UserID:     123,
						CategoryID: 1,
					}).
					Return(int64(0), nil)
			},
			input: &types.GetByCategoryReq{
				CategoryID: 1,
				Page:       1,
				Size:       10,
			},
			expectedCode:      values.ErrorCodeSuccess,
			expectedItemCount: 0,
			expectedTotal:     0,
		},
		{
			name: "error on count",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetVoicesByCategoryCount 返回错误
				mockDB.EXPECT().
					GetVoicesByCategoryCount(gomock.Any(), gomock.Any()).
					Return(int64(0), xerrors.New(500, "database error"))
			},
			input: &types.GetByCategoryReq{
				CategoryID: 1,
				Page:       1,
				Size:       10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get voices by category count",
		},
		{
			name: "error on get voices",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetVoicesByCategoryCount 成功
				mockDB.EXPECT().
					GetVoicesByCategoryCount(gomock.Any(), gomock.Any()).
					Return(int64(2), nil)

				// 设置 DB.GetVoicesByCategory 返回错误
				mockDB.EXPECT().
					GetVoicesByCategory(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(500, "database error"))
			},
			input: &types.GetByCategoryReq{
				CategoryID: 1,
				Page:       1,
				Size:       10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get voices by category",
		},
		{
			name: "error on converter",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.GetVoicesByCategoryCount 成功
				mockDB.EXPECT().
					GetVoicesByCategoryCount(gomock.Any(), gomock.Any()).
					Return(int64(1), nil)

				// 设置 DB.GetVoicesByCategory 成功
				mockDB.EXPECT().
					GetVoicesByCategory(gomock.Any(), gomock.Any()).
					Return([]*pg.GetVoicesByCategoryRow{
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       1,
								DisplayName:   pgtype.Text{String: "Voice 1", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 180, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{1},
								Nsfw:          false,
							},
							IsLiked: false,
						},
					}, nil)

				// 设置 BatchGetPresignedURL 返回错误
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(nil, xerrors.New(500, "s3 error"))

				// 由于 mr.Finish 并行执行，即使 S3 失败，其他操作仍会执行，需要设置期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					AnyTimes()

				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					AnyTimes()

				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					AnyTimes()
			},
			input: &types.GetByCategoryReq{
				CategoryID: 1,
				Page:       1,
				Size:       10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "failed to convert voices to voice info",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3 := setupGetByCategoryTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3)
			}

			// 执行测试
			resp, err := logic.GetByCategory(tt.input)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证分页信息
				assert.Equal(t, tt.expectedTotal, resp.Data.Total)
				assert.Equal(t, tt.input.Page, resp.Data.Page)
				assert.Equal(t, tt.input.Size, resp.Data.Size)

				// 验证返回的语音列表长度
				if tt.expectedItemCount > 0 {
					assert.Equal(t, tt.expectedItemCount, len(resp.Data.Items))
				} else {
					assert.Nil(t, resp.Data.Items)
				}
			}
		})
	}
}
