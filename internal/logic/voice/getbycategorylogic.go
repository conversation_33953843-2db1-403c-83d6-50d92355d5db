package voice

import (
	"context"

	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	xerrors "github.com/zeromicro/x/errors"
)

type GetByCategoryLogic struct {
	ctx       context.Context
	svcCtx    *svc.ServiceContext
	converter *converter
}

// 获取分类语音
func NewGetByCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetByCategoryLogic {
	return &GetByCategoryLogic{
		ctx:       ctx,
		svcCtx:    svcCtx,
		converter: newConverter(svcCtx),
	}
}

func (l *GetByCategoryLogic) GetByCategory(req *types.GetByCategoryReq) (*types.GetByCategoryResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	count, err := l.svcCtx.DB.GetVoicesByCategoryCount(l.ctx, &pg.GetVoicesByCategoryCountParams{
		UserID:     int64(servermsg.GetCurrentUser(l.ctx).UserID),
		CategoryID: int32(req.CategoryID),
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voices by category count: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voices by category count")
	}

	if count == 0 {
		return &types.GetByCategoryResp{
			ResponseBase: types.ResponseBase{
				Code: values.ErrorCodeSuccess,
				Msg:  "success",
			},
			Data: &types.VoicePagination{
				Pagination: utils.GetPageInfo(0, req.Page, req.Size),
			},
		}, nil
	}

	voices, err := l.svcCtx.DB.GetVoicesByCategory(l.ctx, &pg.GetVoicesByCategoryParams{
		UserID:     int64(servermsg.GetCurrentUser(l.ctx).UserID),
		CategoryID: int32(req.CategoryID),
		PageSize:   int32(req.Size),
		Page:       int32(req.Page),
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voices by category: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voices by category")
	}

	voicesInfo, err := l.converter.BatchToVoiceWithLikeStatus(l.ctx,
		lo.Map(voices, func(item *pg.GetVoicesByCategoryRow, _ int) *pg.GetVoicesWithLikeStatusRow {
			return &pg.GetVoicesWithLikeStatusRow{
				AiuVoiceTable: item.AiuVoiceTable,
				IsLiked:       item.IsLiked,
			}
		}),
	)
	if err != nil {
		logc.Errorf(l.ctx, "failed to convert voices to voice info: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to convert voices to voice info")
	}

	return &types.GetByCategoryResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: &types.VoicePagination{
			Pagination: utils.GetPageInfo(int(count), req.Page, req.Size),
			Items:      voicesInfo,
		},
	}, nil
}
