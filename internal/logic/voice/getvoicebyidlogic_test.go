package voice

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupGetVoiceByIDTest(t *testing.T) (*gomock.Controller, *GetVoiceByIDLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewGetVoiceByIDLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3
}

func TestGetVoiceByID(t *testing.T) {
	tests := []struct {
		name           string
		setupMocks     func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3)
		request        *types.GetVoiceByIDReq
		expectedCode   int
		expectedErrMsg string
		expectData     bool
	}{
		{
			name: "successful get voice by id",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				mockDB.EXPECT().
					GetVoicesWithLikeStatus(gomock.Any(), &pg.GetVoicesWithLikeStatusParams{
						VoiceIds:              []int32{1},
						UserID:                123,
						FilterByDisplayEnable: false,
						FilterByEnable:        false,
					}).
					Return([]*pg.GetVoicesWithLikeStatusRow{
						{
							AiuVoiceTable: pg.AiuVoiceTable{
								VoiceId:       1,
								DisplayName:   pgtype.Text{String: "Test Voice", Valid: true},
								SampleUrl:     pgtype.Text{String: "http://example.com/voice.mp3", Valid: true},
								Duration:      pgtype.Int4{Int32: 180, Valid: true},
								CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
								Enable:        pgtype.Bool{Bool: true, Valid: true},
								Categories:    []int32{1},
								Nsfw:          false,
							},
							IsLiked: false,
						},
					}, nil)

				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/voice.mp3": "https://presigned.example.com/voice.mp3",
					}, nil)

				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "creator"},
					}, nil)

				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
					}, nil)

				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceAccentTable{}, nil)
			},
			request: &types.GetVoiceByIDReq{
				VoiceID: 1,
			},
			expectedCode: values.ErrorCodeSuccess,
			expectData:   true,
		},
		{
			name: "voice not found",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				mockDB.EXPECT().
					GetVoicesWithLikeStatus(gomock.Any(), &pg.GetVoicesWithLikeStatusParams{
						VoiceIds:              []int32{999},
						UserID:                123,
						FilterByDisplayEnable: false,
						FilterByEnable:        false,
					}).
					Return([]*pg.GetVoicesWithLikeStatusRow{}, nil)
			},
			request: &types.GetVoiceByIDReq{
				VoiceID: 999,
			},
			expectedCode:   values.ErrorCodeNotFound,
			expectedErrMsg: "voice not found",
		},
		{
			name: "database error",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				mockDB.EXPECT().
					GetVoicesWithLikeStatus(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(500, "database error"))
			},
			request: &types.GetVoiceByIDReq{
				VoiceID: 1,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get voice by id",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl, logic, mockDB, mockDynamo, mockS3 := setupGetVoiceByIDTest(t)
			defer ctrl.Finish()

			tt.setupMocks(mockDB, mockDynamo, mockS3)

			resp, err := logic.GetVoiceByID(tt.request)

			if tt.expectedErrMsg != "" {
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Contains(t, xError.Msg, tt.expectedErrMsg)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				if tt.expectData {
					assert.NotNil(t, resp.Data)
					assert.Equal(t, 1, resp.Data.VoiceID)
				}
			}
		})
	}
}
