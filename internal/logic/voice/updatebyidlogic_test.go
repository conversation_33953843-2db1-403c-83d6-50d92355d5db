package voice

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupUpdateByIDTest(t *testing.T) (*gomock.Controller, *UpdateByIDLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewUpdateByIDLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3
}

func TestUpdateByID(t *testing.T) {
	tests := []struct {
		name           string
		setupMocks     func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3)
		input          *types.UpdateVoiceByIDReq
		expectedCode   int
		expectedErrMsg string
	}{
		{
			name: "success update",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.GetVoiceByID 的期望
				mockDB.EXPECT().
					GetVoiceByID(gomock.Any(), int32(1)).
					Return(&pg.AiuVoiceTable{
						VoiceId:       1,
						DisplayName:   pgtype.Text{String: "Original Voice", Valid: true},
						SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
						Duration:      pgtype.Int4{Int32: 180, Valid: true},
						CreatorUserId: pgtype.Int8{Int64: 123, Valid: true}, // 当前用户是创建者
						Enable:        pgtype.Bool{Bool: true, Valid: true},
						Categories:    []int32{1},
						Nsfw:          false,
					}, nil)

				// 设置 DB.UpdateVoice 的期望
				mockDB.EXPECT().
					UpdateVoice(gomock.Any(), gomock.Any()).
					Return(&pg.AiuVoiceTable{
						VoiceId:       1,
						DisplayName:   pgtype.Text{String: "Updated Voice", Valid: true},
						SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
						Duration:      pgtype.Int4{Int32: 180, Valid: true},
						CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
						Enable:        pgtype.Bool{Bool: false, Valid: true}, // 更新后的状态
						Categories:    []int32{1},
						Nsfw:          false,
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/voice1.mp3": "https://presigned.example.com/voice1.mp3",
					}, nil)

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "testuser"},
					}, nil)

				// 设置 GetVoiceCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
					}, nil)

				// 设置 GetAccentsByIDs 的期望
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuVoiceAccentTable{}, nil)
			},
			input: &types.UpdateVoiceByIDReq{
				VoiceID: 1,
				UpdateInfo: types.UpdateInfo{
					DisplayName: &[]string{"Updated Voice"}[0],
					Enable:      &[]bool{false}[0],
					Public:      &[]bool{true}[0],
					AccentID:    &[]int{0}[0],
				},
			},
			expectedCode: values.ErrorCodeSuccess,
		},
		{
			name: "voice not found",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetVoiceByID 返回 nil
				mockDB.EXPECT().
					GetVoiceByID(gomock.Any(), int32(999)).
					Return(nil, nil)
			},
			input: &types.UpdateVoiceByIDReq{
				VoiceID: 999,
				UpdateInfo: types.UpdateInfo{
					DisplayName: &[]string{"Updated Voice"}[0],
					Enable:      &[]bool{false}[0],
					Public:      &[]bool{true}[0],
					AccentID:    &[]int{0}[0],
				},
			},
			expectedCode:   values.ErrorCodeNotFound,
			expectedErrMsg: "voice not found",
		},
		{
			name: "not the creator",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetVoiceByID 返回其他用户创建的语音
				mockDB.EXPECT().
					GetVoiceByID(gomock.Any(), int32(1)).
					Return(&pg.AiuVoiceTable{
						VoiceId:       1,
						DisplayName:   pgtype.Text{String: "Other User Voice", Valid: true},
						SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
						Duration:      pgtype.Int4{Int32: 180, Valid: true},
						CreatorUserId: pgtype.Int8{Int64: 456, Valid: true}, // 不是当前用户
						Enable:        pgtype.Bool{Bool: true, Valid: true},
						Categories:    []int32{1},
						Nsfw:          false,
					}, nil)
			},
			input: &types.UpdateVoiceByIDReq{
				VoiceID: 1,
				UpdateInfo: types.UpdateInfo{
					DisplayName: &[]string{"Updated Voice"}[0],
					Enable:      &[]bool{false}[0],
					Public:      &[]bool{true}[0],
					AccentID:    &[]int{0}[0],
				},
			},
			expectedCode:   values.ErrorCodeForbidden,
			expectedErrMsg: "you are not the creator of this voice",
		},
		{
			name: "error on get voice by id",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetVoiceByID 返回错误
				mockDB.EXPECT().
					GetVoiceByID(gomock.Any(), int32(1)).
					Return(nil, xerrors.New(500, "database error"))
			},
			input: &types.UpdateVoiceByIDReq{
				VoiceID: 1,
				UpdateInfo: types.UpdateInfo{
					DisplayName: &[]string{"Updated Voice"}[0],
					Enable:      &[]bool{false}[0],
					Public:      &[]bool{true}[0],
					AccentID:    &[]int{0}[0],
				},
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get voice by id",
		},
		{
			name: "error on update voice",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetVoiceByID 成功
				mockDB.EXPECT().
					GetVoiceByID(gomock.Any(), int32(1)).
					Return(&pg.AiuVoiceTable{
						VoiceId:       1,
						DisplayName:   pgtype.Text{String: "Original Voice", Valid: true},
						SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
						Duration:      pgtype.Int4{Int32: 180, Valid: true},
						CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
						Enable:        pgtype.Bool{Bool: true, Valid: true},
						Categories:    []int32{1},
						Nsfw:          false,
					}, nil)

				// 设置 DB.UpdateVoice 返回错误
				mockDB.EXPECT().
					UpdateVoice(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(500, "database error"))
			},
			input: &types.UpdateVoiceByIDReq{
				VoiceID: 1,
				UpdateInfo: types.UpdateInfo{
					DisplayName: &[]string{"Updated Voice"}[0],
					Enable:      &[]bool{false}[0],
					Public:      &[]bool{true}[0],
					AccentID:    &[]int{0}[0],
				},
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to update voice",
		},
		{
			name: "error on converter",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.GetVoiceByID 成功
				mockDB.EXPECT().
					GetVoiceByID(gomock.Any(), int32(1)).
					Return(&pg.AiuVoiceTable{
						VoiceId:       1,
						DisplayName:   pgtype.Text{String: "Original Voice", Valid: true},
						SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
						Duration:      pgtype.Int4{Int32: 180, Valid: true},
						CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
						Enable:        pgtype.Bool{Bool: true, Valid: true},
						Categories:    []int32{1},
						Nsfw:          false,
					}, nil)

				// 设置 DB.UpdateVoice 成功
				mockDB.EXPECT().
					UpdateVoice(gomock.Any(), gomock.Any()).
					Return(&pg.AiuVoiceTable{
						VoiceId:       1,
						DisplayName:   pgtype.Text{String: "Updated Voice", Valid: true},
						SampleUrl:     pgtype.Text{String: "http://example.com/voice1.mp3", Valid: true},
						Duration:      pgtype.Int4{Int32: 180, Valid: true},
						CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
						Enable:        pgtype.Bool{Bool: false, Valid: true},
						Categories:    []int32{1},
						Nsfw:          false,
					}, nil)

				// 设置 BatchGetPresignedURL 返回错误
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(nil, xerrors.New(500, "s3 error"))

				// 设置 BatchGetUsers 期望调用（converter 会调用）
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), []int{123}).
					Return(map[int]*dynamo.User{
						123: {UserName: "test_user"},
					}, nil)

				// 设置 GetVoiceCategoriesByIDs 期望调用（converter 会调用）
				mockDB.EXPECT().
					GetVoiceCategoriesByIDs(gomock.Any(), []int32{1}).
					Return([]*pg.AiuVoiceCategory{
						{ID: 1, Name: "Category 1"},
					}, nil)

				// 设置 GetAccentsByIDs 期望调用（converter 会调用）
				mockDB.EXPECT().
					GetAccentsByIDs(gomock.Any(), []int32{}).
					Return([]*pg.AiuVoiceAccentTable{}, nil)
			},
			input: &types.UpdateVoiceByIDReq{
				VoiceID: 1,
				UpdateInfo: types.UpdateInfo{
					DisplayName: &[]string{"Updated Voice"}[0],
					Enable:      &[]bool{false}[0],
					Public:      &[]bool{true}[0],
					AccentID:    &[]int{0}[0],
				},
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "failed to convert voice to voice info",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3 := setupUpdateByIDTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3)
			}

			// 执行测试
			resp, err := logic.UpdateByID(tt.input)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)
				assert.NotNil(t, resp.Data)
				assert.Equal(t, 1, resp.Data.VoiceID)
			}
		})
	}
}
