package voice

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/rest/httpc"
	xerrors "github.com/zeromicro/x/errors"
)

type CloneVoiceLogic struct {
	ctx       context.Context
	svcCtx    *svc.ServiceContext
	converter *converter
}

// 克隆语音
func NewCloneVoiceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CloneVoiceLogic {
	return &CloneVoiceLogic{
		ctx:       ctx,
		svcCtx:    svcCtx,
		converter: newConverter(svcCtx),
	}
}

func (l *CloneVoiceLogic) CloneVoice(req *types.CloneVoiceReq) (*types.CloneVoiceResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	count, err := l.svcCtx.DB.GetVoiceCountByCreatorUserID(l.ctx, int32(servermsg.GetCurrentUser(l.ctx).UserID))
	if err != nil {
		logc.Errorf(l.ctx, "failed to get voice count by creator user id: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get voice count by creator user id")
	}

	cloneResult, err := l.cloneVoice(l.ctx, &cloneVoiceReq{
		VoiceURL: req.RecordURL,
		S3Prefix: fmt.Sprintf("s3://%s/aiu-audio/voice-sample/", l.svcCtx.Config.S3.BucketName),
		TTSText:  "Hey there! This is how your cloned voice sounds. What do you think?",
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to clone voice: %v", err)
		return nil, err
	}
	logc.Infof(l.ctx, "cloneResult: %+v", cloneResult)

	voiceName := fmt.Sprintf("Cloned voice (%d)", count+1)
	voice, err := l.svcCtx.DB.CreateVoice(l.ctx, &pg.CreateVoiceParams{
		DisplayName:        voiceName,
		SampleUrl:          cloneResult.PreprocessedAudioPath,
		RecordUrl:          req.RecordURL,
		RecordText:         cloneResult.Transcription,
		ProcessedRecordUrl: cloneResult.PreprocessedAudioPath,
		DisplayEnable:      false,
		CharName:           "",
		LabsTag:            []string{voiceName},
		LabsId:             uuid.New().String(),
		RvcTag:             []string{""},
		RvcId:              "",
		RvcTranspose:       0,
		CreatorUserId:      int64(servermsg.GetCurrentUser(l.ctx).UserID),
		CreationType:       0,
		CreateTime:         int64(time.Now().UnixMilli()),
		Duration:           5,
		AccentId:           int64(req.AccentID),
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to create voice: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to create voice")
	}
	logc.Infof(l.ctx, "clone voice success, voice id: %d", voice.VoiceId)

	ret, err := l.converter.BatchToVoiceWithLikeStatus(l.ctx, []*pg.GetVoicesWithLikeStatusRow{
		{AiuVoiceTable: *voice},
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to convert voice: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to convert voice info")
	}

	return &types.CloneVoiceResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: ret[0],
	}, nil
}

type cloneVoiceReq struct {
	// 语音文件URL
	VoiceURL string `json:"voice_url"`
	// 语音文件S3前缀
	S3Prefix string `json:"s3_prefix"`
	// 语音文件TTS文本
	TTSText string `json:"tts_text"`
}

type cloneVoiceResult struct {
	// 预处理后的语音文件URL
	PreprocessedAudioPath string `json:"preprocessed_audio_path"`
	// 语音文件TTS文本
	Transcription string `json:"transcription"`
}

// 克隆语音
func (l *CloneVoiceLogic) cloneVoice(ctx context.Context, req *cloneVoiceReq) (*cloneVoiceResult, error) {
	logc.Debugf(ctx, "clone voice req: %+v", req)
	rsp, err := httpc.Do(ctx, http.MethodPost, "http://************/preprocess", req)
	if err != nil {
		logc.Errorf(ctx, "failed to clone voice: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to clone voice")
	}
	defer rsp.Body.Close()

	if rsp.StatusCode != http.StatusOK {
		logc.Errorf(ctx, "failed to clone voice: %v", rsp.Status)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to clone voice")
	}
	body, err := io.ReadAll(rsp.Body)
	if err != nil {
		logc.Errorf(ctx, "failed to read clone voice body: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to read clone voice body")
	}
	logc.Debugf(ctx, "clone voice body: %s", string(body))

	v := &cloneVoiceResult{}
	err = json.Unmarshal(body, v)
	if err != nil {
		logc.Errorf(ctx, "failed to decode clone voice result: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to decode clone voice result")
	}

	return v, nil
}
