package discover

import (
	"context"
	"encoding/json"
	std_errors "errors" // Renamed to avoid conflict if 'errors' is used as var name
	"testing"
	"time"

	xerrors "github.com/zeromicro/x/errors" // Ensure this import is correctly placed

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
)

func TestGetDiscoverPageConfigByTypeLogic_GetDiscoverPageConfigByType(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockDB := pgmock.NewMockQuerier(ctrl)

	validDataPayload := types.DiscoverPageConfigData{
		Title:                 "Test Title",
		Subtitle:              "Test Subtitle",
		CoverImageUrl:         "http://example.com/cover.jpg",
		SecondaryPageImageUrl: "http://example.com/secondary.jpg",
		LinkedSekaiIDs:        []int64{1, 2},
	}
	validDataJSON, _ := json.Marshal(validDataPayload)
	invalidDataJSON := []byte("invalid json")

	now := time.Now()
	pgTimestamp := pgtype.Timestamptz{Time: now, Valid: true}

	testCases := []struct {
		name    string
		req     *types.GetDiscoverPageConfigByTypeReq
		prepare func()
		want    *types.GetDiscoverPageConfigByTypeResp
		wantErr bool
		errCode int
	}{
		{
			name: "success_with_data",
			req: &types.GetDiscoverPageConfigByTypeReq{
				Type: "test_type",
				Page: 1,
				Size: 10,
			},
			prepare: func() {
				mockDB.EXPECT().CountDiscoverPageConfigByType(gomock.Any(), "test_type").Return(int64(1), nil)
				mockDB.EXPECT().GetDiscoverPageConfigByType(gomock.Any(), &pg.GetDiscoverPageConfigByTypeParams{
					Type:     "test_type",
					Page:     int32(1),
					PageSize: int32(10),
				}).Return([]*pg.GetDiscoverPageConfigByTypeRow{
					{
						ID:        1,
						Type:      "test_type",
						Data:      validDataJSON,
						Status:    "active",
						SortOrder: 1,
						CreatedAt: pgTimestamp,
						UpdatedAt: pgTimestamp,
					},
				}, nil)
			},
			want: &types.GetDiscoverPageConfigByTypeResp{
				ResponseBase: types.ResponseBase{Code: values.ErrorCodeSuccess, Msg: "success"},
				Data: &types.DiscoverPageConfigPagination{
					Pagination: types.Pagination{Page: 1, Size: 10, Total: 1, Pages: 1},
					Items: []*types.DiscoverPageConfigItem{
						{
							ID:        1,
							Type:      "test_type",
							Data:      validDataPayload,
							Status:    "active",
							SortOrder: 1,
							CreatedAt: now.Unix(),
							UpdatedAt: now.Unix(),
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success_no_data",
			req: &types.GetDiscoverPageConfigByTypeReq{
				Type: "empty_type",
				Page: 1,
				Size: 10,
			},
			prepare: func() {
				mockDB.EXPECT().CountDiscoverPageConfigByType(gomock.Any(), "empty_type").Return(int64(0), nil)
			},
			want: &types.GetDiscoverPageConfigByTypeResp{
				ResponseBase: types.ResponseBase{Code: values.ErrorCodeSuccess, Msg: "success"},
				Data: &types.DiscoverPageConfigPagination{
					Pagination: types.Pagination{Page: 1, Size: 10, Total: 0, Pages: 0},
					Items:      []*types.DiscoverPageConfigItem{},
				},
			},
			wantErr: false,
		},
		{
			name: "db_error_count_config",
			req: &types.GetDiscoverPageConfigByTypeReq{
				Type: "error_type",
				Page: 1,
				Size: 10,
			},
			prepare: func() {
				mockDB.EXPECT().CountDiscoverPageConfigByType(gomock.Any(), "error_type").Return(int64(0), std_errors.New("db count error"))
			},
			want:    nil,
			wantErr: true,
			errCode: values.ErrorCodeInternalDBError,
		},
		{
			name: "db_error_get_config",
			req: &types.GetDiscoverPageConfigByTypeReq{
				Type: "error_type_get",
				Page: 1,
				Size: 10,
			},
			prepare: func() {
				mockDB.EXPECT().CountDiscoverPageConfigByType(gomock.Any(), "error_type_get").Return(int64(1), nil)
				mockDB.EXPECT().GetDiscoverPageConfigByType(gomock.Any(), &pg.GetDiscoverPageConfigByTypeParams{
					Type:     "error_type_get",
					Page:     int32(1),
					PageSize: int32(10),
				}).Return(nil, std_errors.New("db get error"))
			},
			want:    nil,
			wantErr: true,
			errCode: values.ErrorCodeInternalDBError,
		},
		{
			name: "json_unmarshal_error",
			req: &types.GetDiscoverPageConfigByTypeReq{
				Type: "json_error_type",
				Page: 1,
				Size: 10,
			},
			prepare: func() {
				mockDB.EXPECT().CountDiscoverPageConfigByType(gomock.Any(), "json_error_type").Return(int64(1), nil)
				mockDB.EXPECT().GetDiscoverPageConfigByType(gomock.Any(), &pg.GetDiscoverPageConfigByTypeParams{
					Type:     "json_error_type",
					Page:     int32(1),
					PageSize: int32(10),
				}).Return([]*pg.GetDiscoverPageConfigByTypeRow{
					{
						ID:        2,
						Type:      "json_error_type",
						Data:      invalidDataJSON,
						Status:    "active",
						SortOrder: 1,
						CreatedAt: pgTimestamp,
						UpdatedAt: pgTimestamp,
					},
				}, nil)
			},
			want:    nil,
			wantErr: true,
			errCode: values.ErrorCodeInternalServerError,
		},
		{
			name: "success_with_multiple_data_and_pagination",
			req: &types.GetDiscoverPageConfigByTypeReq{
				Type: "multi_type",
				Page: 2,
				Size: 1,
			},
			prepare: func() {
				mockDB.EXPECT().CountDiscoverPageConfigByType(gomock.Any(), "multi_type").Return(int64(2), nil)
				mockDB.EXPECT().GetDiscoverPageConfigByType(gomock.Any(), &pg.GetDiscoverPageConfigByTypeParams{
					Type:     "multi_type",
					Page:     int32(2),
					PageSize: int32(1),
				}).Return([]*pg.GetDiscoverPageConfigByTypeRow{
					{
						ID:        2,
						Type:      "multi_type",
						Data:      validDataJSON,
						Status:    "active",
						SortOrder: 2,
						CreatedAt: pgTimestamp,
						UpdatedAt: pgTimestamp,
					},
				}, nil)
			},
			want: &types.GetDiscoverPageConfigByTypeResp{
				ResponseBase: types.ResponseBase{Code: values.ErrorCodeSuccess, Msg: "success"},
				Data: &types.DiscoverPageConfigPagination{
					Pagination: types.Pagination{Page: 2, Size: 1, Total: 2, Pages: 2},
					Items: []*types.DiscoverPageConfigItem{
						{
							ID:        2,
							Type:      "multi_type",
							Data:      validDataPayload,
							Status:    "active",
							SortOrder: 2,
							CreatedAt: now.Unix(),
							UpdatedAt: now.Unix(),
						},
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.prepare != nil {
				tc.prepare()
			}

			logic := NewGetDiscoverPageConfigByTypeLogic(context.Background(), &svc.ServiceContext{
				DB: mockDB,
			})

			got, err := logic.GetDiscoverPageConfigByType(tc.req)

			if tc.wantErr {
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg, actual type: %T, error: %v", err, err)
				if ok {
					assert.Equal(t, tc.errCode, xError.Code, "Error code mismatch")
					// Optionally, you can also check xError.Msg if needed, for example:
					// assert.Contains(t, xError.Msg, "expected part of message", "Error message content mismatch")
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.want, got)
			}
		})
	}
}
