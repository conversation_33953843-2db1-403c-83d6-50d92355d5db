package story

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/opensearch"
	"github.com/sekai-app/sekai-go/internal/db/opensearch/opensearchmock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupSearchStoryTest(t *testing.T) (*gomock.Controller, *SearchStoryLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3, *opensearchmock.MockOpenSearch) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)
	mockOpsearch := opensearchmock.NewMockOpenSearch(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:       mockDB,
		Dynamo:   mockDynamo,
		S3:       mockS3,
		Opsearch: mockOpsearch,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewSearchStoryLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3, mockOpsearch
}

func createSearchStoryResult(totalValue int, storyIDs ...int) *opensearch.SearchResult[opensearch.Story] {
	result := &opensearch.SearchResult[opensearch.Story]{}
	result.Hits.Total.Value = totalValue

	for _, id := range storyIDs {
		result.Hits.Hits = append(result.Hits.Hits, opensearch.SearchHit[opensearch.Story]{
			Source: opensearch.Story{ID: id},
		})
	}

	return result
}

func TestSearchStory(t *testing.T) {
	tests := []struct {
		name             string
		setupMocks       func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3, *opensearchmock.MockOpenSearch)
		request          *types.SearchStoryReq
		expectedCode     int
		expectedErrMsg   string
		expectedItemsLen int
		expectedTotal    int
	}{
		{
			name: "successful search with results",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchStoryByKeyword 的期望
				searchResult := createSearchStoryResult(3, 1, 2, 3)

				mockOpsearch.EXPECT().
					SearchStoryByKeyword(gomock.Any(), opensearch.SearchStoryReq{
						Keyword: "test",
						UserID:  123,
						Page:    1,
						Size:    10,
					}).
					Return(searchResult, nil)

				// 设置 DB.GetStoriesByIDs 的期望
				mockDB.EXPECT().
					GetStoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuUniverseTable{
						{
							UniverseId:    1,
							Highlight:     []byte(`{"text":"story highlight 1"}`),
							GifUrl:        pgtype.Text{String: "http://example.com/gif1.gif", Valid: true},
							CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
							Public:        pgtype.Bool{Bool: true, Valid: true},
							IsDelete:      pgtype.Bool{Bool: false, Valid: true},
							WhatIf:        pgtype.Text{String: "What if 1", Valid: true},
						},
						{
							UniverseId:    2,
							Highlight:     []byte(`{"text":"story highlight 2"}`),
							GifUrl:        pgtype.Text{String: "http://example.com/gif2.gif", Valid: true},
							CreatorUserId: pgtype.Int8{Int64: 456, Valid: true},
							Public:        pgtype.Bool{Bool: true, Valid: true},
							IsDelete:      pgtype.Bool{Bool: false, Valid: true},
							WhatIf:        pgtype.Text{String: "What if 2", Valid: true},
						},
					}, nil)

				// 设置 converter 相关依赖的 mock
				// Mock for BatchGetStoryViewCount
				mockDynamo.EXPECT().
					BatchGetStoryViewCount(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.StoryViewCount{
						1: {StoryID: 1, ViewCount: 100},
						2: {StoryID: 2, ViewCount: 200},
					}, nil)

				// Mock for BatchGetUsers
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "creator1"},
						456: {ID: 456, UserName: "creator2"},
					}, nil)

				// Mock for BatchGetPresignedURL
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Normal).
					Return(map[string]string{
						"http://example.com/gif1.gif": "https://presigned.example.com/gif1.gif",
						"http://example.com/gif2.gif": "https://presigned.example.com/gif2.gif",
					}, nil)
			},
			request: &types.SearchStoryReq{
				Keywords: "test",
				Page:     1,
				Size:     10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 2,
			expectedTotal:    3,
		},
		{
			name: "empty keyword",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, _ *opensearchmock.MockOpenSearch) {
				// 空关键词不会调用任何依赖
			},
			request: &types.SearchStoryReq{
				Keywords: "",
				Page:     1,
				Size:     10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 0,
			expectedTotal:    0,
		},
		{
			name: "page beyond max results",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, _ *opensearchmock.MockOpenSearch) {
				// 超出最大结果数，不会调用任何依赖
			},
			request: &types.SearchStoryReq{
				Keywords: "test",
				Page:     21,
				Size:     10, // 21 * 10 > 200 (maxSearchResultSize)
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 0,
			expectedTotal:    0,
		},
		{
			name: "opensearch error",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchStoryByKeyword 返回错误
				mockOpsearch.EXPECT().
					SearchStoryByKeyword(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(500, "opensearch error"))
			},
			request: &types.SearchStoryReq{
				Keywords: "error",
				Page:     1,
				Size:     10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "search failed",
		},
		{
			name: "opensearch returns empty results",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchStoryByKeyword 返回空结果
				searchResult := createSearchStoryResult(0)

				mockOpsearch.EXPECT().
					SearchStoryByKeyword(gomock.Any(), gomock.Any()).
					Return(searchResult, nil)
			},
			request: &types.SearchStoryReq{
				Keywords: "nonexistent",
				Page:     1,
				Size:     10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 0,
			expectedTotal:    0,
		},
		{
			name: "database error",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchStoryByKeyword 的期望
				searchResult := createSearchStoryResult(1, 1)

				mockOpsearch.EXPECT().
					SearchStoryByKeyword(gomock.Any(), gomock.Any()).
					Return(searchResult, nil)

				// 设置 DB.GetStoriesByIDs 返回错误
				mockDB.EXPECT().
					GetStoriesByIDs(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(500, "database error"))
			},
			request: &types.SearchStoryReq{
				Keywords: "dberror",
				Page:     1,
				Size:     10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get stories",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3, mockOpsearch := setupSearchStoryTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3, mockOpsearch)
			}

			// 执行测试
			resp, err := logic.SearchStory(tt.request)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证分页信息
				assert.Equal(t, tt.expectedTotal, resp.Data.Total)
				assert.Equal(t, tt.request.Page, resp.Data.Page)
				assert.Equal(t, tt.request.Size, resp.Data.Size)

				// 验证返回的 story 列表长度
				assert.Equal(t, tt.expectedItemsLen, len(resp.Data.Items))
			}
		})
	}
}

func TestBuildEmptyResponse(t *testing.T) {
	ctrl, logic, _, _, _, _ := setupSearchStoryTest(t)
	defer ctrl.Finish()

	resp := logic.buildEmptyResponse(1, 10, 0)

	assert.Equal(t, values.ErrorCodeSuccess, resp.Code)
	assert.Equal(t, "success", resp.Msg)
	assert.Equal(t, 0, resp.Data.Total)
	assert.Equal(t, 1, resp.Data.Page)
	assert.Equal(t, 10, resp.Data.Size)
	assert.Equal(t, 0, len(resp.Data.Items))
}

func TestProcessSearchResults(t *testing.T) {
	ctrl, logic, mockDB, mockDynamo, mockS3, _ := setupSearchStoryTest(t)
	defer ctrl.Finish()

	// 创建测试数据
	searchResult := createSearchStoryResult(2, 1, 2)

	// 设置 mock 期望
	mockDB.EXPECT().
		GetStoriesByIDs(gomock.Any(), gomock.Any()).
		Return([]*pg.AiuUniverseTable{
			{
				UniverseId:    1,
				Highlight:     []byte(`{"text":"highlight 1"}`),
				GifUrl:        pgtype.Text{String: "http://example.com/gif1.gif", Valid: true},
				CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
				Public:        pgtype.Bool{Bool: true, Valid: true},
				IsDelete:      pgtype.Bool{Bool: false, Valid: true},
				WhatIf:        pgtype.Text{String: "What if 1", Valid: true},
			},
			{
				UniverseId:    2,
				Highlight:     []byte(`{"text":"highlight 2"}`),
				GifUrl:        pgtype.Text{String: "http://example.com/gif2.gif", Valid: true},
				CreatorUserId: pgtype.Int8{Int64: 456, Valid: true},
				Public:        pgtype.Bool{Bool: true, Valid: true},
				IsDelete:      pgtype.Bool{Bool: false, Valid: true},
				WhatIf:        pgtype.Text{String: "What if 2", Valid: true},
			},
		}, nil)

	// Mock converter dependencies
	mockDynamo.EXPECT().
		BatchGetStoryViewCount(gomock.Any(), gomock.Any()).
		Return(map[int]*dynamo.StoryViewCount{
			1: {StoryID: 1, ViewCount: 100},
			2: {StoryID: 2, ViewCount: 200},
		}, nil)

	mockDynamo.EXPECT().
		BatchGetUsers(gomock.Any(), gomock.Any()).
		Return(map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
			456: {ID: 456, UserName: "creator2"},
		}, nil)

	// Mock S3
	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Normal).
		Return(map[string]string{
			"http://example.com/gif1.gif": "https://presigned.example.com/gif1.gif",
			"http://example.com/gif2.gif": "https://presigned.example.com/gif2.gif",
		}, nil)

	// 执行测试
	items, err := logic.processSearchResults(searchResult)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 2, len(items))
	assert.Equal(t, 1, items[0].StoryID)
	assert.Equal(t, "highlight 1", items[0].HighlightText)
	assert.Equal(t, "https://presigned.example.com/gif1.gif", items[0].GifURL)
	assert.Equal(t, 123, items[0].CreatorUserID)
	assert.Equal(t, "creator1", items[0].CreatorUserName)
	assert.Equal(t, true, items[0].Public)
	assert.Equal(t, false, items[0].IsDeleted)
	assert.Equal(t, "What if 1", items[0].WhatIf)
	assert.Equal(t, 100, items[0].ViewCount)
}

func TestFetchStoryDetails(t *testing.T) {
	ctrl, logic, mockDB, _, _, _ := setupSearchStoryTest(t)
	defer ctrl.Finish()

	storyIDs := []int64{1, 2}

	mockDB.EXPECT().
		GetStoriesByIDs(gomock.Any(), &pg.GetStoriesByIDsParams{
			StoryIds:       storyIDs,
			IsPublic:       pgtype.Bool{Bool: true, Valid: true},
			FilterByPublic: true,
		}).
		Return([]*pg.AiuUniverseTable{
			{
				UniverseId:    1,
				Highlight:     []byte(`{"text":"highlight 1"}`),
				GifUrl:        pgtype.Text{String: "http://example.com/gif1.gif", Valid: true},
				CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
			},
			{
				UniverseId:    2,
				Highlight:     []byte(`{"text":"highlight 2"}`),
				GifUrl:        pgtype.Text{String: "http://example.com/gif2.gif", Valid: true},
				CreatorUserId: pgtype.Int8{Int64: 456, Valid: true},
			},
		}, nil)

	result, err := logic.fetchStoryDetails(storyIDs)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))
	assert.Equal(t, int64(1), result[0].UniverseId)
	assert.Equal(t, int64(2), result[1].UniverseId)
}
