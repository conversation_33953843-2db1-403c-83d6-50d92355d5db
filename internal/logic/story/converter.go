package story

import (
	"context"
	"encoding/json"

	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/mr"
	xerrors "github.com/zeromicro/x/errors"
)

// Highlight 数据库中保存的高亮文本信息
type Highlight struct {
	Text string `json:"text"`
	// todo 其他字段
}

// Events universe 表中的 events 字段结构
type Events struct {
	EventID int64 `json:"0"`
}

type converter struct {
	svcCtx *svc.ServiceContext
}

func newConverter(svcCtx *svc.ServiceContext) *converter {
	return &converter{
		svcCtx: svcCtx,
	}
}

// BatchToStoryBaseInfo 批量转换story基础信息
func (c *converter) BatchToStoryBaseInfo(ctx context.Context,
	stories []*pg.AiuUniverseTable) ([]*types.StoryBaseInfo, error) {
	if len(stories) == 0 {
		return []*types.StoryBaseInfo{}, nil
	}

	data, err := c.getBatchData(ctx, stories)
	if err != nil {
		return nil, err
	}

	return c.buildBatchStoryBaseInfo(ctx, stories, data), nil
}

type batchData struct {
	gifURLMap    map[int]string
	userMap      map[int]*dynamo.User
	viewCountMap map[int]int
}

func (c *converter) getBatchData(ctx context.Context, stories []*pg.AiuUniverseTable) (*batchData, error) {
	var (
		gifURLMap    map[int]string
		userMap      map[int]*dynamo.User
		viewCountMap map[int]int
	)

	err := mr.Finish(
		func() error {
			var err error
			gifURLMap, err = c.getBatchGifURLs(ctx, stories)
			if err != nil {
				return err
			}
			return nil
		},
		func() error {
			var err error
			userMap, err = c.getBatchUserMap(ctx, stories)
			if err != nil {
				return err
			}
			return nil
		},
		func() error {
			var err error
			viewCountMap, err = c.getBatchViewCountMap(ctx, stories)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return nil, err
	}

	return &batchData{
		gifURLMap:    gifURLMap,
		userMap:      userMap,
		viewCountMap: viewCountMap,
	}, nil
}

func (c *converter) getBatchGifURLs(ctx context.Context, stories []*pg.AiuUniverseTable) (map[int]string, error) {
	// 从每个story中提取 coverUrl 或 fallback 到 gifUrl，并创建 storyID 到 URL 的映射
	storyIDToOriginalURL := lo.Associate(stories, func(story *pg.AiuUniverseTable) (int, string) {
		storyID := int(story.UniverseId)
		URL := utils.GetWithDefault(story.CoverUrl.String, story.GifUrl.String)
		return storyID, URL
	})

	// 提取所有URL并去重，并且记录需要从event table获取的storyID
	urlSet := make(map[string]struct{})
	var needGetFromEvent []*pg.AiuUniverseTable
	for storyID, url := range storyIDToOriginalURL {
		if url == "" {
			logc.Infof(ctx, "Story ID %d has no URL, get from event table", storyID)
			story, _ := lo.Find(stories, func(story *pg.AiuUniverseTable) bool {
				return story.UniverseId == int64(storyID)
			})
			needGetFromEvent = append(needGetFromEvent, story)
		}
		urlSet[url] = struct{}{}
	}
	uniqueURLs := lo.Keys(urlSet)

	if len(needGetFromEvent) > 0 {
		eventIDToStoryID := lo.Associate(needGetFromEvent, func(story *pg.AiuUniverseTable) (int64, int64) {
			var events Events
			_ = json.Unmarshal([]byte(story.Events), &events)
			return events.EventID, story.UniverseId
		})
		eventGifURLs, err := c.svcCtx.DB.GetEventGifURL(ctx, lo.Keys(eventIDToStoryID))
		if err != nil {
			logc.Infof(ctx, "Failed to get event gif URLs: %v", err)
		}
		for _, eventGifURL := range eventGifURLs {
			storyIDToOriginalURL[int(eventIDToStoryID[eventGifURL.EventId])] = eventGifURL.GifUrl.String
		}
		uniqueURLs = append(uniqueURLs, lo.Map(eventGifURLs, func(eventGifURL *pg.GetEventGifURLRow, _ int) string {
			return eventGifURL.GifUrl.String
		})...)
	}

	// 从S3获取预签名URL
	presignedURLMap, err := c.svcCtx.S3.BatchGetPresignedURL(ctx, uniqueURLs, s3.Normal)
	if err != nil {
		logc.Errorf(ctx, "Failed to get presigned URLs: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to get story image URLs")
	}

	// 创建storyID到预签名URL的映射
	storyIDToURLMap := lo.MapValues(storyIDToOriginalURL, func(originalURL string, _ int) string {
		if presignedURL, ok := presignedURLMap[originalURL]; ok {
			return presignedURL
		}
		return "" // 如果获取不到预签名URL，返回空字符串
	})

	return storyIDToURLMap, nil
}

func (c *converter) getBatchUserMap(ctx context.Context, stories []*pg.AiuUniverseTable) (map[int]*dynamo.User, error) {
	// 提取所有用户ID并去重
	userIDs := lo.Uniq(lo.Map(stories, func(story *pg.AiuUniverseTable, _ int) int {
		return int(story.CreatorUserId.Int64)
	}))

	// 批量获取用户信息
	userMap, err := c.svcCtx.Dynamo.BatchGetUsers(ctx, userIDs)
	if err != nil {
		logc.Errorf(ctx, "Failed to get users: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to get user information")
	}

	return userMap, nil
}

func (c *converter) getBatchViewCountMap(ctx context.Context, stories []*pg.AiuUniverseTable) (map[int]int, error) {
	storyIDs := lo.Map(stories, func(story *pg.AiuUniverseTable, _ int) int {
		return int(story.UniverseId)
	})

	viewCountMap, err := c.svcCtx.Dynamo.BatchGetStoryViewCount(ctx, storyIDs)
	if err != nil {
		logc.Errorf(ctx, "Failed to get story view counts: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to get story view counts")
	}

	return lo.MapValues(viewCountMap, func(viewCount *dynamo.StoryViewCount, _ int) int {
		return viewCount.ViewCount
	}), nil
}

func (c *converter) buildBatchStoryBaseInfo(ctx context.Context,
	stories []*pg.AiuUniverseTable, data *batchData) []*types.StoryBaseInfo {
	storyBaseInfos := make([]*types.StoryBaseInfo, len(stories))
	for i, story := range stories {
		highlight := Highlight{}
		err := json.Unmarshal([]byte(story.Highlight), &highlight)
		if err != nil {
			logc.Infof(ctx, "Failed to unmarshal highlight: %v", err)
		}

		storyBaseInfos[i] = &types.StoryBaseInfo{
			StoryID:       int(story.UniverseId),
			HighlightText: highlight.Text,
			GifURL:        data.gifURLMap[int(story.UniverseId)],
			CreatorUserID: int(story.CreatorUserId.Int64),
			CreatorUserName: utils.GetWithDefault(
				data.userMap[int(story.CreatorUserId.Int64)], &dynamo.User{}).UserName,
			Public:    story.Public.Bool,
			IsDeleted: story.IsDelete.Bool,
			WhatIf:    story.WhatIf.String,
			ViewCount: lo.If(
				story.Public.Bool,
				data.viewCountMap[int(story.UniverseId)],
			).Else(0),
		}
	}
	return storyBaseInfos
}
