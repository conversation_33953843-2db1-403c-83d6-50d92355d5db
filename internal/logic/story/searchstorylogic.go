package story

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/opensearch"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

const (
	// maxSearchResultSize 搜索结果最大数量
	maxSearchResultSize = 200
)

type SearchStoryLogic struct {
	logx.Logger
	ctx       context.Context
	svcCtx    *svc.ServiceContext
	converter *converter
}

// 搜索story
func NewSearchStoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchStoryLogic {
	return &SearchStoryLogic{
		Logger:    logx.WithContext(ctx),
		ctx:       ctx,
		svcCtx:    svcCtx,
		converter: newConverter(svcCtx),
	}
}

// SearchStory 搜索story接口
func (l *SearchStoryLogic) SearchStory(req *types.SearchStoryReq) (*types.SearchStoryResp, error) {
	logc.Debugf(l.ctx, "SearchStory req: %+v", req)

	// 处理空关键词情况
	if req.Keywords == "" {
		return l.buildEmptyResponse(req.Page, req.Size, 0), nil
	}

	// 限制最多搜索200条
	if req.Page*req.Size > maxSearchResultSize {
		return l.buildEmptyResponse(req.Page, req.Size, 0), nil
	}

	// 获取当前用户ID
	currentUser := servermsg.GetCurrentUser(l.ctx)

	// 构建搜索请求
	searchReq := opensearch.SearchStoryReq{
		Keyword: req.Keywords,
		UserID:  currentUser.UserID,
		Page:    req.Page,
		Size:    req.Size,
	}

	// 执行搜索
	searchResult, err := l.svcCtx.Opsearch.SearchStoryByKeyword(l.ctx, searchReq)
	if err != nil {
		logc.Errorf(l.ctx, "Failed to search story: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "search failed")
	}

	// 如果没有搜索结果
	if len(searchResult.Hits.Hits) == 0 {
		return l.buildEmptyResponse(req.Page, req.Size, searchResult.Hits.Total.Value), nil
	}

	// 获取并处理搜索结果
	items, err := l.processSearchResults(searchResult)
	if err != nil {
		logc.Errorf(l.ctx, "Failed to process search results: %v", err)
		return nil, err
	}

	// 计算实际总数，考虑最大限制
	total := min(searchResult.Hits.Total.Value, maxSearchResultSize)

	// 构建并返回结果
	return &types.SearchStoryResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.StoryBasePagination{
			Pagination: utils.GetPageInfo(total, req.Page, req.Size),
			Items:      items,
		},
	}, nil
}

// searchResult 是opensearch.SearchResult[opensearch.Story]的别名
type searchResult = opensearch.SearchResult[opensearch.Story]

// buildEmptyResponse 构建空结果响应
func (l *SearchStoryLogic) buildEmptyResponse(page, size int, total int) *types.SearchStoryResp {
	return &types.SearchStoryResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.StoryBasePagination{
			Pagination: utils.GetPageInfo(total, page, size),
			Items:      []*types.StoryBaseInfo{},
		},
	}
}

// processSearchResults 处理搜索结果
func (l *SearchStoryLogic) processSearchResults(searchResult *searchResult) ([]*types.StoryBaseInfo, error) {
	// 提取搜索结果中的StoryID列表
	storyIDs := lo.Map(searchResult.Hits.Hits, func(hit opensearch.SearchHit[opensearch.Story], _ int) int64 {
		return int64(hit.Source.ID)
	})

	// 从数据库中获取详细的Story信息
	stories, err := l.fetchStoryDetails(storyIDs)
	if err != nil {
		return nil, err
	}

	// 将数据库中的Story信息转换为前端需要的格式
	items, err := l.converter.BatchToStoryBaseInfo(l.ctx, stories)
	if err != nil {
		return nil, err
	}

	return items, nil
}

// fetchStoryDetails 从数据库获取Story详细信息
func (l *SearchStoryLogic) fetchStoryDetails(storyIDs []int64) ([]*pg.AiuUniverseTable, error) {
	logc.Infof(l.ctx, "Fetching stories for IDs: %v", storyIDs)

	stories, err := l.svcCtx.DB.GetStoriesByIDs(l.ctx, &pg.GetStoriesByIDsParams{
		StoryIds:       storyIDs,
		IsPublic:       pgtype.Bool{Bool: true, Valid: true},
		FilterByPublic: true,
	})
	if err != nil {
		logc.Errorf(l.ctx, "Failed to get stories from database: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get stories")
	}

	return stories, nil
}
