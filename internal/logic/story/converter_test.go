package story

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func setupConverterTest(t *testing.T) (*gomock.Controller, *converter, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	converter := newConverter(svcCtx)

	return ctrl, converter, mockDB, mockDynamo, mockS3
}

func TestGetBatchGifURLs(t *testing.T) {
	ctrl, converter, _, _, mockS3 := setupConverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	stories := []*pg.AiuUniverseTable{
		{
			UniverseId: 1,
			GifUrl:     pgtype.Text{String: "http://example.com/gif1.gif", Valid: true},
			CoverUrl:   pgtype.Text{String: "", Valid: false},
		},
		{
			UniverseId: 2,
			GifUrl:     pgtype.Text{String: "http://example.com/gif2.gif", Valid: true},
			CoverUrl:   pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
		},
		{
			UniverseId: 3,
			GifUrl:     pgtype.Text{String: "", Valid: false},
			CoverUrl:   pgtype.Text{String: "http://example.com/cover3.jpg", Valid: true},
		},
	}

	urls := []string{
		"http://example.com/gif1.gif",
		"http://example.com/cover2.jpg",
		"http://example.com/cover3.jpg",
	}

	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Normal).
		DoAndReturn(func(_ context.Context, reqUrls []string, _ s3.ResolutionType) (map[string]string, error) {
			assert.ElementsMatch(t, urls, reqUrls)

			return map[string]string{
				"http://example.com/gif1.gif":   "https://presigned.example.com/gif1.gif",
				"http://example.com/cover2.jpg": "https://presigned.example.com/cover2.jpg",
				"http://example.com/cover3.jpg": "https://presigned.example.com/cover3.jpg",
			}, nil
		})

	result, err := converter.getBatchGifURLs(ctx, stories)

	assert.NoError(t, err)
	assert.Equal(t, 3, len(result))
	assert.Equal(t, "https://presigned.example.com/gif1.gif", result[1])
	assert.Equal(t, "https://presigned.example.com/cover2.jpg", result[2])
	assert.Equal(t, "https://presigned.example.com/cover3.jpg", result[3])
}

func TestGetBatchUserMap(t *testing.T) {
	ctrl, converter, _, mockDynamo, _ := setupConverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	stories := []*pg.AiuUniverseTable{
		{UniverseId: 1, CreatorUserId: pgtype.Int8{Int64: 123, Valid: true}},
		{UniverseId: 2, CreatorUserId: pgtype.Int8{Int64: 456, Valid: true}},
		{UniverseId: 3, CreatorUserId: pgtype.Int8{Int64: 123, Valid: true}}, // 重复的用户ID
	}

	mockDynamo.EXPECT().
		BatchGetUsers(gomock.Any(), []int{123, 456}).
		Return(map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
			456: {ID: 456, UserName: "creator2"},
		}, nil)

	result, err := converter.getBatchUserMap(ctx, stories)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))
	assert.Equal(t, 123, result[123].ID)
	assert.Equal(t, "creator1", result[123].UserName)
	assert.Equal(t, 456, result[456].ID)
	assert.Equal(t, "creator2", result[456].UserName)
}

func TestGetBatchViewCountMap(t *testing.T) {
	ctrl, converter, _, mockDynamo, _ := setupConverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	stories := []*pg.AiuUniverseTable{
		{UniverseId: 1},
		{UniverseId: 2},
	}

	mockDynamo.EXPECT().
		BatchGetStoryViewCount(gomock.Any(), []int{1, 2}).
		Return(map[int]*dynamo.StoryViewCount{
			1: {StoryID: 1, ViewCount: 100},
			2: {StoryID: 2, ViewCount: 200},
		}, nil)

	result, err := converter.getBatchViewCountMap(ctx, stories)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))
	assert.Equal(t, 100, result[1])
	assert.Equal(t, 200, result[2])
}

func TestBuildBatchStoryBaseInfo(t *testing.T) {
	ctrl, converter, _, _, _ := setupConverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	stories := []*pg.AiuUniverseTable{
		{
			UniverseId:    1,
			Highlight:     []byte(`{"text":"story highlight 1"}`),
			CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
			Public:        pgtype.Bool{Bool: true, Valid: true},
			IsDelete:      pgtype.Bool{Bool: false, Valid: true},
			WhatIf:        pgtype.Text{String: "What if 1", Valid: true},
		},
		{
			UniverseId:    2,
			Highlight:     []byte(`{"text":"story highlight 2"}`),
			CreatorUserId: pgtype.Int8{Int64: 456, Valid: true},
			Public:        pgtype.Bool{Bool: false, Valid: true},
			IsDelete:      pgtype.Bool{Bool: true, Valid: true},
			WhatIf:        pgtype.Text{String: "What if 2", Valid: true},
		},
	}

	data := &batchData{
		gifURLMap: map[int]string{
			1: "https://presigned.example.com/gif1.gif",
			2: "https://presigned.example.com/gif2.gif",
		},
		userMap: map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
			456: {ID: 456, UserName: "creator2"},
		},
		viewCountMap: map[int]int{
			1: 100,
			2: 200,
		},
	}

	result := converter.buildBatchStoryBaseInfo(ctx, stories, data)

	assert.Equal(t, 2, len(result))

	// 验证第一个故事
	assert.Equal(t, 1, result[0].StoryID)
	assert.Equal(t, "story highlight 1", result[0].HighlightText)
	assert.Equal(t, "https://presigned.example.com/gif1.gif", result[0].GifURL)
	assert.Equal(t, 123, result[0].CreatorUserID)
	assert.Equal(t, "creator1", result[0].CreatorUserName)
	assert.True(t, result[0].Public)
	assert.False(t, result[0].IsDeleted)
	assert.Equal(t, "What if 1", result[0].WhatIf)
	assert.Equal(t, 100, result[0].ViewCount)

	// 验证第二个故事
	assert.Equal(t, 2, result[1].StoryID)
	assert.Equal(t, "story highlight 2", result[1].HighlightText)
	assert.Equal(t, "https://presigned.example.com/gif2.gif", result[1].GifURL)
	assert.Equal(t, 456, result[1].CreatorUserID)
	assert.Equal(t, "creator2", result[1].CreatorUserName)
	assert.False(t, result[1].Public)
	assert.True(t, result[1].IsDeleted)
	assert.Equal(t, "What if 2", result[1].WhatIf)
	// Private stories should have view count of 0
	assert.Equal(t, 0, result[1].ViewCount)
}

func TestBatchToStoryBaseInfo(t *testing.T) {
	ctrl, converter, _, mockDynamo, mockS3 := setupConverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	stories := []*pg.AiuUniverseTable{
		{
			UniverseId:    1,
			Highlight:     []byte(`{"text":"story highlight 1"}`),
			GifUrl:        pgtype.Text{String: "http://example.com/gif1.gif", Valid: true},
			CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
			Public:        pgtype.Bool{Bool: true, Valid: true},
			IsDelete:      pgtype.Bool{Bool: false, Valid: true},
			WhatIf:        pgtype.Text{String: "What if 1", Valid: true},
		},
		{
			UniverseId:    2,
			Highlight:     []byte(`{"text":"story highlight 2"}`),
			GifUrl:        pgtype.Text{String: "http://example.com/gif2.gif", Valid: true},
			CoverUrl:      pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
			CreatorUserId: pgtype.Int8{Int64: 456, Valid: true},
			Public:        pgtype.Bool{Bool: false, Valid: true},
			IsDelete:      pgtype.Bool{Bool: true, Valid: true},
			WhatIf:        pgtype.Text{String: "What if 2", Valid: true},
		},
	}

	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Normal).
		Return(map[string]string{
			"http://example.com/gif1.gif":   "https://presigned.example.com/gif1.gif",
			"http://example.com/cover2.jpg": "https://presigned.example.com/cover2.jpg",
		}, nil)

	// Mock for getBatchUserMap
	mockDynamo.EXPECT().
		BatchGetUsers(gomock.Any(), []int{123, 456}).
		Return(map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
			456: {ID: 456, UserName: "creator2"},
		}, nil)

	// Mock for getBatchViewCountMap
	mockDynamo.EXPECT().
		BatchGetStoryViewCount(gomock.Any(), []int{1, 2}).
		Return(map[int]*dynamo.StoryViewCount{
			1: {StoryID: 1, ViewCount: 100},
			2: {StoryID: 2, ViewCount: 200},
		}, nil)

	result, err := converter.BatchToStoryBaseInfo(ctx, stories)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))

	// 验证第一个故事
	assert.Equal(t, 1, result[0].StoryID)
	assert.Equal(t, "story highlight 1", result[0].HighlightText)
	assert.Equal(t, "https://presigned.example.com/gif1.gif", result[0].GifURL)
	assert.Equal(t, 123, result[0].CreatorUserID)
	assert.Equal(t, "creator1", result[0].CreatorUserName)
	assert.True(t, result[0].Public)
	assert.False(t, result[0].IsDeleted)
	assert.Equal(t, "What if 1", result[0].WhatIf)
	assert.Equal(t, 100, result[0].ViewCount)

	// 验证第二个故事
	assert.Equal(t, 2, result[1].StoryID)
	assert.Equal(t, "story highlight 2", result[1].HighlightText)
	// CoverUrl should be preferred over GifUrl
	assert.Equal(t, "https://presigned.example.com/cover2.jpg", result[1].GifURL)
	assert.Equal(t, 456, result[1].CreatorUserID)
	assert.Equal(t, "creator2", result[1].CreatorUserName)
	assert.False(t, result[1].Public)
	assert.True(t, result[1].IsDeleted)
	assert.Equal(t, "What if 2", result[1].WhatIf)
	assert.Equal(t, 0, result[1].ViewCount)
}

func TestEmptyBatchToStoryBaseInfo(t *testing.T) {
	ctrl, converter, _, _, _ := setupConverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	// Test with empty slice
	stories := []*pg.AiuUniverseTable{}

	result, err := converter.BatchToStoryBaseInfo(ctx, stories)

	assert.NoError(t, err)
	assert.Equal(t, 0, len(result))
}

func TestInvalidHighlightJSON(t *testing.T) {
	ctrl, converter, _, _, _ := setupConverterTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	// Test with invalid JSON in highlight field
	stories := []*pg.AiuUniverseTable{
		{
			UniverseId:    1,
			Highlight:     []byte(`invalid json`),
			CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
		},
	}

	data := &batchData{
		gifURLMap: map[int]string{
			1: "https://presigned.example.com/gif1.gif",
		},
		userMap: map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
		},
		viewCountMap: map[int]int{
			1: 100,
		},
	}

	result := converter.buildBatchStoryBaseInfo(ctx, stories, data)

	assert.Equal(t, 1, len(result))
	assert.Equal(t, 1, result[0].StoryID)
	// Highlight text should be empty when JSON is invalid
	assert.Equal(t, "", result[0].HighlightText)
}
