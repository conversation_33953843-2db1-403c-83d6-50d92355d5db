package bgm

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupGetBGMsByCategoryTest(t *testing.T) (*gomock.Controller, *GetBGMsByCategoryLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewGetBGMsByCategoryLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3
}

func TestGetBGMsByCategory(t *testing.T) {
	tests := []struct {
		name           string
		setupMocks     func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3)
		request        *types.GetBGMsByCategoryReq
		expectedCode   int
		expectedErrMsg string
		expectedTotal  int
		expectedItems  int
	}{
		{
			name: "successful get bgms by category",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.GetBGMsByCategoryCount 的期望
				mockDB.EXPECT().
					GetBGMsByCategoryCount(gomock.Any(), int32(1)).
					Return(int64(3), nil)

				// 设置 DB.GetBGMsByCategory 的期望
				mockDB.EXPECT().
					GetBGMsByCategory(gomock.Any(), &pg.GetBGMsByCategoryParams{
						CategoryID: int32(1),
						Page:       int32(1),
						PageSize:   int32(10),
					}).
					Return([]*pg.AiuBgmTable{
						{
							ID:             1,
							Name:           pgtype.Text{String: "BGM 1 in Category 1", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm1.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 180, Valid: true},
							Tags:           []string{"tag1", "tag2"},
							ReferenceCount: pgtype.Int4{Int32: 5, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
							Categories:     []int32{1},
							Public:         true,
							Nsfw:           false,
							Enable:         pgtype.Bool{Bool: true, Valid: true},
						},
						{
							ID:             2,
							Name:           pgtype.Text{String: "BGM 2 in Category 1", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm2.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 200, Valid: true},
							Tags:           []string{"tag3", "tag4"},
							ReferenceCount: pgtype.Int4{Int32: 10, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 456, Valid: true},
							Categories:     []int32{1},
							Public:         true,
							Nsfw:           false,
							Enable:         pgtype.Bool{Bool: true, Valid: true},
						},
						{
							ID:             3,
							Name:           pgtype.Text{String: "BGM 3 in Category 1", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm3.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover3.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 150, Valid: true},
							Tags:           []string{"tag5", "tag6"},
							ReferenceCount: pgtype.Int4{Int32: 3, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 789, Valid: true},
							Categories:     []int32{1},
							Public:         true,
							Nsfw:           false,
							Enable:         pgtype.Bool{Bool: true, Valid: true},
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
						"http://example.com/cover2.jpg": "https://presigned.example.com/cover2.jpg",
						"http://example.com/cover3.jpg": "https://presigned.example.com/cover3.jpg",
					}, nil)

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "creator1"},
						456: {ID: 456, UserName: "creator2"},
						789: {ID: 789, UserName: "creator3"},
					}, nil)

				// 设置 GetBGMCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
					}, nil)

				// 设置 CheckBGMsLikedByUser 的期望
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{}, nil)
			},
			request: &types.GetBGMsByCategoryReq{
				CategoryID: 1,
				Page:       1,
				Size:       10,
			},
			expectedCode:  values.ErrorCodeSuccess,
			expectedTotal: 3,
			expectedItems: 3,
		},
		{
			name: "get count error",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetBGMsByCategoryCount 的期望返回错误
				mockDB.EXPECT().
					GetBGMsByCategoryCount(gomock.Any(), int32(1)).
					Return(int64(0), xerrors.New(values.ErrorCodeInternalDBError, "database error"))
			},
			request: &types.GetBGMsByCategoryReq{
				CategoryID: 1,
				Page:       1,
				Size:       10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get BGMs count by category",
		},
		{
			name: "get bgms error",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetBGMsByCategoryCount 的期望
				mockDB.EXPECT().
					GetBGMsByCategoryCount(gomock.Any(), int32(1)).
					Return(int64(3), nil)

				// 设置 DB.GetBGMsByCategory 的期望返回错误
				mockDB.EXPECT().
					GetBGMsByCategory(gomock.Any(), &pg.GetBGMsByCategoryParams{
						CategoryID: int32(1),
						Page:       int32(1),
						PageSize:   int32(10),
					}).
					Return(nil, xerrors.New(values.ErrorCodeInternalDBError, "database error"))
			},
			request: &types.GetBGMsByCategoryReq{
				CategoryID: 1,
				Page:       1,
				Size:       10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get BGMs by category",
		},
		{
			name: "empty result",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetBGMsByCategoryCount 的期望
				mockDB.EXPECT().
					GetBGMsByCategoryCount(gomock.Any(), int32(2)).
					Return(int64(0), nil)

				// 设置 DB.GetBGMsByCategory 的期望返回空结果
				mockDB.EXPECT().
					GetBGMsByCategory(gomock.Any(), &pg.GetBGMsByCategoryParams{
						CategoryID: int32(2),
						Page:       int32(1),
						PageSize:   int32(10),
					}).
					Return([]*pg.AiuBgmTable{}, nil)
			},
			request: &types.GetBGMsByCategoryReq{
				CategoryID: 2,
				Page:       1,
				Size:       10,
			},
			expectedCode:  values.ErrorCodeSuccess,
			expectedTotal: 0,
			expectedItems: 0,
		},
		{
			name: "batch convert error",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.GetBGMsByCategoryCount 的期望
				mockDB.EXPECT().
					GetBGMsByCategoryCount(gomock.Any(), int32(1)).
					Return(int64(1), nil)

				// 设置 DB.GetBGMsByCategory 的期望
				mockDB.EXPECT().
					GetBGMsByCategory(gomock.Any(), &pg.GetBGMsByCategoryParams{
						CategoryID: int32(1),
						Page:       int32(1),
						PageSize:   int32(10),
					}).
					Return([]*pg.AiuBgmTable{
						{
							ID:            1,
							Name:          pgtype.Text{String: "BGM 1 in Category 1", Valid: true},
							CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
							Categories:    []int32{1},
							Enable:        pgtype.Bool{Bool: true, Valid: true},
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{}, nil).
					AnyTimes()

				// 添加 GetBGMCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmCategory{
						{ID: 1, Name: "Category 1"},
					}, nil).
					AnyTimes()

				// 设置 BatchGetUsers 的期望返回错误
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(values.ErrorCodeInternalDynamoError, "dynamo error"))

				// 设置 CheckBGMsLikedByUser 的期望
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{}, nil).
					AnyTimes()
			},
			request: &types.GetBGMsByCategoryReq{
				CategoryID: 1,
				Page:       1,
				Size:       10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "failed to convert BGMs",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3 := setupGetBGMsByCategoryTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3)
			}

			// 执行测试
			resp, err := logic.GetBGMsByCategory(tt.request)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证分页信息
				assert.Equal(t, tt.expectedTotal, resp.Data.Total)
				assert.Equal(t, tt.request.Page, resp.Data.Page)
				assert.Equal(t, tt.request.Size, resp.Data.Size)

				// 验证返回的BGM列表长度
				assert.Equal(t, tt.expectedItems, len(resp.Data.Items))
			}
		})
	}
}
