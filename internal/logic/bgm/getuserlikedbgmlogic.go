package bgm

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type GetUserLikedBGMLogic struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *coverter
}

func NewGetUserLikedBGMLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserLikedBGMLogic {
	return &GetUserLikedBGMLogic{
		Logger:   logx.WithContext(ctx),
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: new<PERSON>overter(svcCtx),
	}
}

func (l *GetUserLikedBGMLogic) GetUserLikedBGM(req *types.GetUserLikedBGMReq) (*types.GetUserLikedBGMResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	// 如果用户ID为0，则使用当前用户ID果用户ID为0，则使用当前用户ID
	userID := req.UserID
	if userID == 0 {
		userID = servermsg.GetCurrentUser(l.ctx).UserID
	}

	total, err := l.svcCtx.DB.GetUserLikedBGMsCount(l.ctx, &pg.GetUserLikedBGMsCountParams{
		UserID:         int32(userID),
		FilterByPublic: userID != servermsg.GetCurrentUser(l.ctx).UserID,
	})
	if err != nil {
		logc.Errorf(l.ctx, "GetUserLikedBGMsCount error: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "internal error")
	}

	bgms, err := l.svcCtx.DB.GetUserLikedBGMs(l.ctx, &pg.GetUserLikedBGMsParams{
		UserID:         int32(userID),
		FilterByPublic: userID != servermsg.GetCurrentUser(l.ctx).UserID,
		Page:           int32(req.Page),
		PageSize:       int32(req.Size),
	})
	if err != nil {
		logc.Errorf(l.ctx, "GetUserLikedBGMs error: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "internal error")
	}
	logc.Debugf(l.ctx, "len(bgms): %d", len(bgms))

	bgmBaseInfos, err := l.coverter.BatchToBGMBaseInfo(l.ctx, bgms)
	if err != nil {
		logc.Errorf(l.ctx, "BatchToBGMBaseInfo error: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "internal error")
	}

	return &types.GetUserLikedBGMResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.BGMPagination{
			Pagination: utils.GetPageInfo(int(total), req.Page, req.Size),
			Items:      bgmBaseInfos,
		},
	}, nil
}
