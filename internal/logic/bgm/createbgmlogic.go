package bgm

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type CreateBGMLogic struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *coverter
}

func NewCreateBGMLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBGMLogic {
	return &CreateBGMLogic{
		Logger:   logx.WithContext(ctx),
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: newCoverter(svcCtx),
	}
}

func (l *CreateBGMLogic) CreateBGM(req *types.CreateBGMReq) (*types.CreateBGMResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)
	result, err := l.processBGM(l.ctx, req.URL, 1250)
	if err != nil {
		logc.Errorf(l.ctx, "failed to processBGM: %v", err)
		return nil, err
	}

	user := servermsg.GetCurrentUser(l.ctx)
	bgm, err := l.svcCtx.DB.CreateBGM(l.ctx, &pg.CreateBGMParams{
		Url:            pgtype.Text{String: result.S3URL, Valid: true},
		Name:           pgtype.Text{String: req.Name, Valid: true},
		CoverUrl:       pgtype.Text{String: utils.GetWithDefault(req.CoverURL, getRandomCover()), Valid: true},
		Duration:       pgtype.Int4{Int32: utils.GetWithDefault(int32(req.Duration), int32(result.Duration)), Valid: true},
		Tags:           req.Tags,
		ReferenceCount: pgtype.Int4{Int32: 0, Valid: true},
		CreatorUserId:  pgtype.Int8{Int64: int64(user.UserID), Valid: true},
		CreationType:   0,
		Enable:         pgtype.Bool{Bool: true, Valid: true},
		Public:         req.Public,
		CreateTimeUtc:  pgtype.Int8{Int64: time.Now().UnixMilli(), Valid: true},
		PremiseIds:     req.PremiseIDs,
		Categories:     req.Categories,
		Nsfw:           false,
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to createBGM: %v", err)
		return nil, err
	}

	bgmInfo, err := l.coverter.ToBGMBaseInfo(l.ctx, bgm)
	if err != nil {
		logc.Errorf(l.ctx, "failed to convert bgm to bgm info: %v", err)
		return nil, err
	}

	return &types.CreateBGMResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: *bgmInfo,
	}, nil
}

var defaultCoverList = []string{
	"s3://sekai-stage-data/aiu-bgm/cover/acoustic.jpg",
	"s3://sekai-stage-data/aiu-bgm/cover/country.jpg",
	"s3://sekai-stage-data/aiu-bgm/cover/guitar.jpg",
	"s3://sekai-stage-data/aiu-bgm/cover/piano.jpg",
	"s3://sekai-stage-data/aiu-bgm/cover/retro.jpg",
	"s3://sekai-stage-data/aiu-bgm/cover/space.jpg",
}

func getRandomCover() string {
	return defaultCoverList[rand.Intn(len(defaultCoverList))]
}

// processBGMResult 处理BGM结果
type processBGMResult struct {
	S3URL    string
	Duration float64
}

func (l *CreateBGMLogic) processBGM(ctx context.Context, URL string, targetRMS float64) (*processBGMResult, error) {
	tmpFileName := os.TempDir() + "/" + uuid.New().String() + ".mp3"
	defer utils.CleanupFile(tmpFileName)

	// 获取S3信息
	s3Key, err := l.svcCtx.S3.ParseURLToS3Key(URL)
	if err != nil {
		return nil, xerrors.New(values.ErrorCodeBadRequest, fmt.Sprintf("failed to get S3 info: %v", err))
	}

	// 下载文件
	if err := l.svcCtx.S3.Download(ctx, s3Key, tmpFileName); err != nil {
		return nil, xerrors.New(values.ErrorCodeNotFoundS3, fmt.Sprintf("failed to download file: %v", err))
	}

	// 使用ffmpeg获取当前音频的RMS值
	currentRMS, err := l.svcCtx.FFmpeg.GetAudioRMS(tmpFileName)
	if err != nil {
		return nil, xerrors.New(values.ErrorCodeInternalServerError, fmt.Sprintf("failed to get audio RMS: %v", err))
	}

	// 计算音量调整值
	changeInDB := 20 * math.Log10(targetRMS/currentRMS)

	// 获取音频时长
	duration, err := l.svcCtx.FFmpeg.GetAudioDuration(tmpFileName)
	if err != nil {
		return nil, xerrors.New(values.ErrorCodeInternalServerError, fmt.Sprintf("failed to get audio duration: %v", err))
	}

	// 使用ffmpeg调整音量
	normalizedFileName := strings.TrimSuffix(tmpFileName, filepath.Ext(tmpFileName)) + "_normalized.mp3"
	defer utils.CleanupFile(normalizedFileName)
	if err := l.svcCtx.FFmpeg.NormalizeAudio(tmpFileName, normalizedFileName, changeInDB); err != nil {
		return nil, xerrors.New(values.ErrorCodeInternalServerError, fmt.Sprintf("failed to normalize audio: %v", err))
	}

	// 生成新的S3 key
	newS3Key := strings.TrimSuffix(s3Key, filepath.Ext(s3Key)) + "_normalized.mp3"

	// 上传文件到S3
	s3URL, err := l.svcCtx.S3.Upload(ctx, normalizedFileName, newS3Key)
	if err != nil {
		return nil, xerrors.New(values.ErrorCodeInternalServerError, fmt.Sprintf("failed to upload file: %v", err))
	}

	return &processBGMResult{
		S3URL:    s3URL,
		Duration: duration,
	}, nil
}
