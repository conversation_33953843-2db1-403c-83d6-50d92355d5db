package bgm

import (
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type DeleteBGMLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteBGMLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBGMLogic {
	return &DeleteBGMLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteBGMLogic) DeleteBGM(req *types.DeleteBGMReq) (*types.DeleteBGMResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	user := servermsg.GetCurrentUser(l.ctx)
	_, err := l.svcCtx.DB.SoftDeleteBGM(l.ctx, &pg.SoftDeleteBGMParams{
		ID:            int64(req.ID),
		CreatorUserId: pgtype.Int8{Int64: int64(user.UserID), Valid: true},
	})
	if err == pgx.ErrNoRows {
		return nil, xerrors.New(values.ErrorCodeForbidden, "you are not the creator of this bgm")
	}
	if err != nil {
		logc.Errorf(l.ctx, "failed to delete bgm: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to delete bgm")
	}

	return &types.DeleteBGMResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
	}, nil
}
