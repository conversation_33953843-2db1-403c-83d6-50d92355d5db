package bgm

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupUpdateBGMTest(t *testing.T) (*gomock.Controller, *UpdateBGMLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewUpdateBGMLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3
}

func TestUpdateBGM(t *testing.T) {
	// 辅助函数：创建字符串指针
	strPtr := func(s string) *string {
		return &s
	}
	// 辅助函数：创建整数指针
	intPtr := func(i int) *int {
		return &i
	}
	// 辅助函数：创建布尔值指针
	boolPtr := func(b bool) *bool {
		return &b
	}

	tests := []struct {
		name           string
		setupMocks     func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3)
		request        *types.UpdateBGMReq
		expectedCode   int
		expectedErrMsg string
	}{
		{
			name: "successful update",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.UpdateBGM 的期望
				mockDB.EXPECT().
					UpdateBGM(gomock.Any(), gomock.Any()).
					Return(&pg.AiuBgmTable{
						ID:             101,
						Name:           pgtype.Text{String: "Updated BGM", Valid: true},
						Url:            pgtype.Text{String: "http://example.com/updated.mp3", Valid: true},
						CoverUrl:       pgtype.Text{String: "http://example.com/updated-cover.jpg", Valid: true},
						Duration:       pgtype.Int4{Int32: 200, Valid: true},
						Tags:           []string{"updated", "tag"},
						ReferenceCount: pgtype.Int4{Int32: 0, Valid: true},
						CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
						Categories:     []int32{1, 2},
						Public:         true,
						Nsfw:           false,
						Enable:         pgtype.Bool{Bool: true, Valid: true},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(map[string]string{
						"http://example.com/updated-cover.jpg": "https://presigned.example.com/updated-cover.jpg",
					}, nil).
					AnyTimes()

				// 添加 GetPresignedURL 的期望
				mockS3.EXPECT().
					GetPresignedURL(gomock.Any(), gomock.Any(), gomock.Any()).
					Return("https://presigned.example.com/updated-cover.jpg", nil).
					AnyTimes()

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "testuser"},
					}, nil).
					AnyTimes()

				// 添加 GetUser 的期望
				mockDynamo.EXPECT().
					GetUser(gomock.Any(), gomock.Any()).
					Return(&dynamo.User{ID: 123, UserName: "testuser"}, nil).
					AnyTimes()

				// 设置 GetBGMCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
					}, nil)

				// 设置 CheckBGMsLikedByUser 的期望
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{}, nil)
			},
			request: &types.UpdateBGMReq{
				ID:         101,
				Name:       strPtr("Updated BGM"),
				URL:        strPtr("http://example.com/updated.mp3"),
				CoverURL:   strPtr("http://example.com/updated-cover.jpg"),
				Duration:   intPtr(200),
				Tags:       []string{"updated", "tag"},
				Public:     boolPtr(true),
				Categories: []int32{1, 2},
				PremiseIDs: []string{"10", "20"},
			},
			expectedCode: values.ErrorCodeSuccess,
		},
		{
			name: "bgm not found",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.UpdateBGM 的期望返回 pgx.ErrNoRows
				mockDB.EXPECT().
					UpdateBGM(gomock.Any(), gomock.Any()).
					Return(nil, pgx.ErrNoRows)
			},
			request: &types.UpdateBGMReq{
				ID:         999, // 不存在的BGM ID
				Name:       strPtr("Nonexistent BGM"),
				URL:        strPtr("http://example.com/nonexistent.mp3"),
				CoverURL:   strPtr("http://example.com/nonexistent-cover.jpg"),
				Duration:   intPtr(150),
				Tags:       []string{"nonexistent", "tag"},
				Public:     boolPtr(true),
				Categories: []int32{1},
				PremiseIDs: []string{},
			},
			expectedCode:   values.ErrorCodeNotFound,
			expectedErrMsg: "bgm not found",
		},
		{
			name: "database error",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.UpdateBGM 的期望返回数据库错误
				mockDB.EXPECT().
					UpdateBGM(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(values.ErrorCodeInternalDBError, "database error"))
			},
			request: &types.UpdateBGMReq{
				ID:         102,
				Name:       strPtr("Error BGM"),
				URL:        strPtr("http://example.com/error.mp3"),
				CoverURL:   strPtr("http://example.com/error-cover.jpg"),
				Duration:   intPtr(180),
				Tags:       []string{"error", "tag"},
				Public:     boolPtr(true),
				Categories: []int32{1},
				PremiseIDs: []string{},
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to update bgm",
		},
		{
			name: "convert error",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.UpdateBGM 的期望
				mockDB.EXPECT().
					UpdateBGM(gomock.Any(), gomock.Any()).
					Return(&pg.AiuBgmTable{
						ID:             103,
						Name:           pgtype.Text{String: "Convert Error BGM", Valid: true},
						Url:            pgtype.Text{String: "http://example.com/convert-error.mp3", Valid: true},
						CoverUrl:       pgtype.Text{String: "http://example.com/convert-error-cover.jpg", Valid: true},
						Duration:       pgtype.Int4{Int32: 220, Valid: true},
						Tags:           []string{"convert", "error", "tag"},
						ReferenceCount: pgtype.Int4{Int32: 0, Valid: true},
						CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
						Categories:     []int32{1},
						Public:         true,
						Nsfw:           false,
						Enable:         pgtype.Bool{Bool: true, Valid: true},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(map[string]string{}, nil).
					AnyTimes()

				// 添加 GetPresignedURL 的期望
				mockS3.EXPECT().
					GetPresignedURL(gomock.Any(), gomock.Any(), gomock.Any()).
					Return("", nil).
					AnyTimes()

				// 设置 GetBGMCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmCategory{
						{ID: 1, Name: "Category 1"},
					}, nil).
					AnyTimes()

				// 设置 CheckBGMsLikedByUser 的期望
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{}, nil).
					AnyTimes()

				// 设置 BatchGetUsers 的期望返回错误
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(values.ErrorCodeInternalDynamoError, "dynamo error")).
					AnyTimes()

				// 添加 GetUser 的期望
				mockDynamo.EXPECT().
					GetUser(gomock.Any(), gomock.Eq(123)).
					Return(nil, xerrors.New(values.ErrorCodeInternalDynamoError, "dynamo error")).
					AnyTimes()
			},
			request: &types.UpdateBGMReq{
				ID:         103,
				Name:       strPtr("Convert Error BGM"),
				URL:        strPtr("http://example.com/convert-error.mp3"),
				CoverURL:   strPtr("http://example.com/convert-error-cover.jpg"),
				Duration:   intPtr(220),
				Tags:       []string{"convert", "error", "tag"},
				Public:     boolPtr(true),
				Categories: []int32{1},
				PremiseIDs: []string{},
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "failed to convert bgm to bgm info",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3 := setupUpdateBGMTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3)
			}

			// 执行测试
			resp, err := logic.UpdateBGM(tt.request)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证更新后的BGM数据
				assert.Equal(t, *tt.request.Name, resp.Data.Name)
				assert.Equal(t, *tt.request.URL, resp.Data.URL)
				assert.Equal(t, *tt.request.Duration, resp.Data.Duration)
			}
		})
	}
}
