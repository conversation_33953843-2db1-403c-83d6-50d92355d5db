package bgm

import (
	"context"
	"errors"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/ffmpeg/ffmpegmock"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupCreateBGMTest(t *testing.T) (*gomock.Controller, *CreateBGMLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3, *ffmpegmock.MockFFmpeg) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)
	mockFFmpeg := ffmpegmock.NewMockFFmpeg(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
		FFmpeg: mockFFmpeg,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewCreateBGMLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3, mockFFmpeg
}

func TestCreateBGM(t *testing.T) {
	tests := []struct {
		name           string
		setupMocks     func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3, *ffmpegmock.MockFFmpeg)
		request        *types.CreateBGMReq
		expectedCode   int
		expectedErrMsg string
	}{
		{
			name: "successful create bgm",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3, mockFFmpeg *ffmpegmock.MockFFmpeg) {
				// 模拟 ParseURLToS3Key
				mockS3.EXPECT().
					ParseURLToS3Key(gomock.Any()).
					Return("test/audio.mp3", nil).
					AnyTimes()

				// 模拟 Download
				mockS3.EXPECT().
					Download(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				// 模拟 GetAudioRMS
				mockFFmpeg.EXPECT().
					GetAudioRMS(gomock.Any()).
					Return(1000.0, nil).
					AnyTimes()

				// 模拟 GetAudioDuration
				mockFFmpeg.EXPECT().
					GetAudioDuration(gomock.Any()).
					Return(180.0, nil).
					AnyTimes()

				// 模拟 NormalizeAudio
				mockFFmpeg.EXPECT().
					NormalizeAudio(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				// 模拟 Upload
				mockS3.EXPECT().
					Upload(gomock.Any(), gomock.Any(), gomock.Any()).
					Return("s3://sekai-stage-data/aiu-bgm/test/audio_normalized.mp3", nil).
					AnyTimes()

				// 模拟 CreateBGM
				mockDB.EXPECT().
					CreateBGM(gomock.Any(), gomock.Any()).
					Return(&pg.AiuBgmTable{
						ID:             101,
						Name:           pgtype.Text{String: "Test BGM", Valid: true},
						Url:            pgtype.Text{String: "s3://sekai-stage-data/aiu-bgm/test/audio_normalized.mp3", Valid: true},
						CoverUrl:       pgtype.Text{String: "s3://sekai-stage-data/aiu-bgm/cover/piano.jpg", Valid: true},
						Duration:       pgtype.Int4{Int32: 180, Valid: true},
						Tags:           []string{"test", "bgm"},
						ReferenceCount: pgtype.Int4{Int32: 0, Valid: true},
						CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
						Categories:     []int32{1, 2},
						Public:         true,
						Nsfw:           false,
						Enable:         pgtype.Bool{Bool: true, Valid: true},
					}, nil).
					AnyTimes()

				// 模拟 BatchGetPresignedURL
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(map[string]string{
						"s3://sekai-stage-data/aiu-bgm/cover/piano.jpg": "https://presigned.example.com/cover.jpg",
					}, nil).
					AnyTimes()

				// 模拟 GetPresignedURL
				mockS3.EXPECT().
					GetPresignedURL(gomock.Any(), gomock.Any(), gomock.Any()).
					Return("https://presigned.example.com/cover.jpg", nil).
					AnyTimes()

				// 模拟 BatchGetUsers
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "testuser"},
					}, nil).
					AnyTimes()

				// 模拟 GetUser
				mockDynamo.EXPECT().
					GetUser(gomock.Any(), gomock.Any()).
					Return(&dynamo.User{ID: 123, UserName: "testuser"}, nil).
					AnyTimes()

				// 模拟 GetBGMCategoriesByIDs
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
					}, nil).
					AnyTimes()

				// 模拟 CheckBGMsLikedByUser
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{}, nil).
					AnyTimes()
			},
			request: &types.CreateBGMReq{
				Name:       "Test BGM",
				URL:        "s3://sekai-stage-data/aiu-bgm/test/audio.mp3",
				Duration:   180,
				CoverURL:   "s3://sekai-stage-data/aiu-bgm/cover/piano.jpg",
				Tags:       []string{"test", "bgm"},
				Public:     true,
				Categories: []int32{1, 2},
				PremiseIDs: []string{},
			},
			expectedCode: values.ErrorCodeSuccess,
		},
		{
			name: "s3 parse error",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, mockS3 *s3mock.MockS3, _ *ffmpegmock.MockFFmpeg) {
				// 模拟 ParseURLToS3Key 返回错误
				mockS3.EXPECT().
					ParseURLToS3Key(gomock.Any()).
					Return("", errors.New("invalid S3 URL")).
					AnyTimes()
			},
			request: &types.CreateBGMReq{
				Name:       "Error BGM",
				URL:        "invalid-url",
				Duration:   180,
				Tags:       []string{"error", "bgm"},
				Public:     true,
				Categories: []int32{1},
			},
			expectedCode:   values.ErrorCodeBadRequest,
			expectedErrMsg: "failed to get S3 info: invalid S3 URL",
		},
		{
			name: "s3 download error",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, mockS3 *s3mock.MockS3, _ *ffmpegmock.MockFFmpeg) {
				// 模拟 ParseURLToS3Key
				mockS3.EXPECT().
					ParseURLToS3Key(gomock.Any()).
					Return("test/nonexistent.mp3", nil).
					AnyTimes()

				// 模拟 Download 返回错误
				mockS3.EXPECT().
					Download(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(errors.New("file not found")).
					AnyTimes()
			},
			request: &types.CreateBGMReq{
				Name:       "Download Error BGM",
				URL:        "s3://sekai-stage-data/aiu-bgm/test/nonexistent.mp3",
				Duration:   180,
				Tags:       []string{"error", "bgm"},
				Public:     true,
				Categories: []int32{1},
			},
			expectedCode:   values.ErrorCodeNotFoundS3,
			expectedErrMsg: "failed to download file: file not found",
		},
		{
			name: "ffmpeg rms error",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, mockS3 *s3mock.MockS3, mockFFmpeg *ffmpegmock.MockFFmpeg) {
				// 模拟 ParseURLToS3Key
				mockS3.EXPECT().
					ParseURLToS3Key(gomock.Any()).
					Return("test/invalid.mp3", nil).
					AnyTimes()

				// 模拟 Download
				mockS3.EXPECT().
					Download(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				// 模拟 GetAudioRMS 返回错误
				mockFFmpeg.EXPECT().
					GetAudioRMS(gomock.Any()).
					Return(0.0, errors.New("invalid audio file")).
					AnyTimes()
			},
			request: &types.CreateBGMReq{
				Name:       "RMS Error BGM",
				URL:        "s3://sekai-stage-data/aiu-bgm/test/invalid.mp3",
				Duration:   180,
				Tags:       []string{"error", "bgm"},
				Public:     true,
				Categories: []int32{1},
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "failed to get audio RMS: invalid audio file",
		},
		{
			name: "ffmpeg duration error",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, mockS3 *s3mock.MockS3, mockFFmpeg *ffmpegmock.MockFFmpeg) {
				// 模拟 ParseURLToS3Key
				mockS3.EXPECT().
					ParseURLToS3Key(gomock.Any()).
					Return("test/invalid_duration.mp3", nil).
					AnyTimes()

				// 模拟 Download
				mockS3.EXPECT().
					Download(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				// 模拟 GetAudioRMS
				mockFFmpeg.EXPECT().
					GetAudioRMS(gomock.Any()).
					Return(1000.0, nil).
					AnyTimes()

				// 模拟 GetAudioDuration 返回错误
				mockFFmpeg.EXPECT().
					GetAudioDuration(gomock.Any()).
					Return(0.0, errors.New("cannot determine duration")).
					AnyTimes()
			},
			request: &types.CreateBGMReq{
				Name:       "Duration Error BGM",
				URL:        "s3://sekai-stage-data/aiu-bgm/test/invalid_duration.mp3",
				Duration:   180,
				Tags:       []string{"error", "bgm"},
				Public:     true,
				Categories: []int32{1},
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "failed to get audio duration: cannot determine duration",
		},
		{
			name: "ffmpeg normalize error",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, mockS3 *s3mock.MockS3, mockFFmpeg *ffmpegmock.MockFFmpeg) {
				// 模拟 ParseURLToS3Key
				mockS3.EXPECT().
					ParseURLToS3Key(gomock.Any()).
					Return("test/normalize_error.mp3", nil).
					AnyTimes()

				// 模拟 Download
				mockS3.EXPECT().
					Download(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				// 模拟 GetAudioRMS
				mockFFmpeg.EXPECT().
					GetAudioRMS(gomock.Any()).
					Return(1000.0, nil).
					AnyTimes()

				// 模拟 GetAudioDuration
				mockFFmpeg.EXPECT().
					GetAudioDuration(gomock.Any()).
					Return(180.0, nil).
					AnyTimes()

				// 模拟 NormalizeAudio 返回错误
				mockFFmpeg.EXPECT().
					NormalizeAudio(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(errors.New("normalization failed")).
					AnyTimes()
			},
			request: &types.CreateBGMReq{
				Name:       "Normalize Error BGM",
				URL:        "s3://sekai-stage-data/aiu-bgm/test/normalize_error.mp3",
				Duration:   180,
				Tags:       []string{"error", "bgm"},
				Public:     true,
				Categories: []int32{1},
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "failed to normalize audio: normalization failed",
		},
		{
			name: "s3 upload error",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, mockS3 *s3mock.MockS3, mockFFmpeg *ffmpegmock.MockFFmpeg) {
				// 模拟 ParseURLToS3Key
				mockS3.EXPECT().
					ParseURLToS3Key(gomock.Any()).
					Return("test/upload_error.mp3", nil).
					AnyTimes()

				// 模拟 Download
				mockS3.EXPECT().
					Download(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				// 模拟 GetAudioRMS
				mockFFmpeg.EXPECT().
					GetAudioRMS(gomock.Any()).
					Return(1000.0, nil).
					AnyTimes()

				// 模拟 GetAudioDuration
				mockFFmpeg.EXPECT().
					GetAudioDuration(gomock.Any()).
					Return(180.0, nil).
					AnyTimes()

				// 模拟 NormalizeAudio
				mockFFmpeg.EXPECT().
					NormalizeAudio(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				// 模拟 Upload 返回错误
				mockS3.EXPECT().
					Upload(gomock.Any(), gomock.Any(), gomock.Any()).
					Return("", errors.New("upload failed")).
					AnyTimes()
			},
			request: &types.CreateBGMReq{
				Name:       "Upload Error BGM",
				URL:        "s3://sekai-stage-data/aiu-bgm/test/upload_error.mp3",
				Duration:   180,
				Tags:       []string{"error", "bgm"},
				Public:     true,
				Categories: []int32{1},
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "failed to upload file: upload failed",
		},
		{
			name: "database error",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, mockS3 *s3mock.MockS3, mockFFmpeg *ffmpegmock.MockFFmpeg) {
				// 模拟 ParseURLToS3Key
				mockS3.EXPECT().
					ParseURLToS3Key(gomock.Any()).
					Return("test/db_error.mp3", nil).
					AnyTimes()

				// 模拟 Download
				mockS3.EXPECT().
					Download(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				// 模拟 GetAudioRMS
				mockFFmpeg.EXPECT().
					GetAudioRMS(gomock.Any()).
					Return(1000.0, nil).
					AnyTimes()

				// 模拟 GetAudioDuration
				mockFFmpeg.EXPECT().
					GetAudioDuration(gomock.Any()).
					Return(180.0, nil).
					AnyTimes()

				// 模拟 NormalizeAudio
				mockFFmpeg.EXPECT().
					NormalizeAudio(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).
					AnyTimes()

				// 模拟 Upload
				mockS3.EXPECT().
					Upload(gomock.Any(), gomock.Any(), gomock.Any()).
					Return("s3://sekai-stage-data/aiu-bgm/test/db_error_normalized.mp3", nil).
					AnyTimes()

				// 模拟 CreateBGM 返回错误
				mockDB.EXPECT().
					CreateBGM(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(values.ErrorCodeInternalDBError, "database error")).
					AnyTimes()
			},
			request: &types.CreateBGMReq{
				Name:       "DB Error BGM",
				URL:        "s3://sekai-stage-data/aiu-bgm/test/db_error.mp3",
				Duration:   180,
				Tags:       []string{"error", "bgm"},
				Public:     true,
				Categories: []int32{1},
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "database error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3, mockFFmpeg := setupCreateBGMTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3, mockFFmpeg)
			}

			// 执行测试
			resp, err := logic.CreateBGM(tt.request)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				if xError, ok := err.(*xerrors.CodeMsg); ok {
					assert.Equal(t, tt.expectedCode, xError.Code)
					assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				} else {
					assert.Contains(t, err.Error(), tt.expectedErrMsg)
				}
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证创建的BGM数据
				assert.Equal(t, tt.request.Name, resp.Data.Name)
				assert.Equal(t, tt.request.Duration, resp.Data.Duration)
			}
		})
	}
}
