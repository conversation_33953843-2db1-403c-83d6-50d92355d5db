package bgm

import (
	"context"
	"encoding/json"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type GetTrendingBGMLogic struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *coverter
}

func NewGetTrendingBGMLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTrendingBGMLogic {
	return &GetTrendingBGMLogic{
		Logger:   logx.WithContext(ctx),
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: newCoverter(svcCtx),
	}
}

// GetTrendingBGM 获取热门BGM
func (l *GetTrendingBGMLogic) GetTrendingBGM(req *types.GetTrendingBGMReq) (*types.GetTrendingBGMResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	value, err := l.svcCtx.Dynamo.GetTopInfo(l.ctx, "sekai_backend_bgm_top30")
	if err != nil {
		logc.Errorf(l.ctx, "GetTopInfo error: %+v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDynamoError, "get top info error")
	}
	logc.Infof(l.ctx, "value: %+v", value)

	var bgmIDs []int32
	err = json.Unmarshal([]byte(value), &bgmIDs)
	if err != nil {
		logc.Errorf(l.ctx, "Unmarshal error: %+v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "internal server error")
	}
	if len(bgmIDs) == 0 {
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to get trending bgms")
	}

	bgms, err := l.svcCtx.DB.GetBGMByIDs(l.ctx, &pg.GetBGMByIDsParams{
		Ids:            bgmIDs,
		Nsfw:           false,
		FilterByNsfw:   true,
		Enable:         pgtype.Bool{Bool: true, Valid: true},
		FilterByEnable: true,
		Public:         true,
		FilterByPublic: true,
	})
	if err != nil {
		logc.Errorf(l.ctx, "GetBGMByIDs error: %+v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "internal server error")
	}

	// 批量转换为BGMBaseInfo
	start := (req.Page - 1) * req.Size
	end := min(req.Page*req.Size, len(bgms))
	var data []*types.BGMBaseInfo
	if start < len(bgms) {
		data, err = l.coverter.BatchToBGMBaseInfo(l.ctx, bgms[start:end])
		if err != nil {
			logc.Errorf(l.ctx, "BatchToBGMBaseInfo error: %+v", err)
			return nil, xerrors.New(values.ErrorCodeInternalServerError, "internal server error")
		}
	}

	// 计算总数
	total := len(bgms)

	return &types.GetTrendingBGMResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.BGMPagination{
			Pagination: utils.GetPageInfo(total, req.Page, req.Size),
			Items:      data,
		},
	}, nil
}
