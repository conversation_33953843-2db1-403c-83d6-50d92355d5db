package bgm

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/opensearch"
	"github.com/sekai-app/sekai-go/internal/db/opensearch/opensearchmock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupSearchBGMTest(t *testing.T) (*gomock.Controller, *SearchBGMLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3, *opensearchmock.MockOpenSearch) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)
	mockOpsearch := opensearchmock.NewMockOpenSearch(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:       mockDB,
		Dynamo:   mockDynamo,
		S3:       mockS3,
		Opsearch: mockOpsearch,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewSearchBGMLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3, mockOpsearch
}

func TestSearchBGM(t *testing.T) {
	tests := []struct {
		name             string
		setupMocks       func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3, *opensearchmock.MockOpenSearch)
		request          *types.SearchBGMReq
		expectedCode     int
		expectedErrMsg   string
		expectedItemsLen int
		expectedTotal    int
	}{
		{
			name: "successful search with results",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchBGMByName 的期望
				searchResults := []opensearch.SearchHit[opensearch.BGM]{
					{
						Score:  1.5,
						Source: opensearch.BGM{ID: 1, Name: "Test BGM 1"},
					},
					{
						Score:  1.2,
						Source: opensearch.BGM{ID: 2, Name: "Test BGM 2"},
					},
					{
						Score:  0.8,
						Source: opensearch.BGM{ID: 3, Name: "Test BGM 3"},
					},
				}
				mockOpsearch.EXPECT().
					SearchBGMByName(gomock.Any(), 123, "test", 1, 100).
					Return(searchResults, 3, nil)

				// 设置 Dynamo.BatchGetBGMMetaInfo 的期望
				mockDynamo.EXPECT().
					BatchGetBGMMetaInfo(gomock.Any(), []int{1, 2, 3}).
					Return(map[int]*dynamo.BGMMetaInfo{
						1: {UseCount: 10},
						2: {UseCount: 5},
						3: {UseCount: 3},
					}, nil)

				// 设置 DB.GetBGMByIDs 的期望
				mockDB.EXPECT().
					GetBGMByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmTable{
						{
							ID:             1,
							Name:           pgtype.Text{String: "Test BGM 1", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm1.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 180, Valid: true},
							Tags:           []string{"tag1", "tag2"},
							ReferenceCount: pgtype.Int4{Int32: 5, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
							Public:         true,
							Nsfw:           false,
							Categories:     []int32{1},
						},
						{
							ID:             2,
							Name:           pgtype.Text{String: "Test BGM 2", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm2.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 200, Valid: true},
							Tags:           []string{"tag3", "tag4"},
							ReferenceCount: pgtype.Int4{Int32: 10, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 456, Valid: true},
							Public:         true,
							Nsfw:           false,
							Categories:     []int32{2},
						},
						{
							ID:             3,
							Name:           pgtype.Text{String: "Test BGM 3", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm3.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover3.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 150, Valid: true},
							Tags:           []string{"tag5", "tag6"},
							ReferenceCount: pgtype.Int4{Int32: 3, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 789, Valid: true},
							Public:         true,
							Nsfw:           false,
							Categories:     []int32{3},
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
						"http://example.com/cover2.jpg": "https://presigned.example.com/cover2.jpg",
						"http://example.com/cover3.jpg": "https://presigned.example.com/cover3.jpg",
					}, nil)

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "creator1"},
						456: {ID: 456, UserName: "creator2"},
						789: {ID: 789, UserName: "creator3"},
					}, nil)

				// 设置 GetBGMCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
						{ID: 3, Name: "Category 3", CoverUrl: pgtype.Text{String: "http://example.com/cat3.jpg", Valid: true}},
					}, nil)

				// 设置 CheckBGMsLikedByUser 的期望
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{}, nil)
			},
			request: &types.SearchBGMReq{
				Keyword: "test",
				Page:    1,
				Size:    10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 3,
			expectedTotal:    3,
		},
		{
			name: "search with no results",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchBGMByName 的期望返回空结果
				mockOpsearch.EXPECT().
					SearchBGMByName(gomock.Any(), 123, "nonexistent", 1, 100).
					Return([]opensearch.SearchHit[opensearch.BGM]{}, 0, nil)
			},
			request: &types.SearchBGMReq{
				Keyword: "nonexistent",
				Page:    1,
				Size:    10,
			},
			expectedCode:     values.ErrorCodeSuccess,
			expectedItemsLen: 0,
			expectedTotal:    0,
		},
		{
			name: "search error in OpenSearch",
			setupMocks: func(_ *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchBGMByName 的期望返回错误
				mockOpsearch.EXPECT().
					SearchBGMByName(gomock.Any(), 123, "error", 1, 100).
					Return(nil, 0, xerrors.New(500, "search error"))
			},
			request: &types.SearchBGMReq{
				Keyword: "error",
				Page:    1,
				Size:    10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "Search BGM failed",
		},
		{
			name: "dynamo error",
			setupMocks: func(_ *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, _ *s3mock.MockS3, mockOpsearch *opensearchmock.MockOpenSearch) {
				// 设置 OpenSearch.SearchBGMByName 的期望
				searchResults := []opensearch.SearchHit[opensearch.BGM]{
					{
						Score:  1.5,
						Source: opensearch.BGM{ID: 1, Name: "Test BGM 1"},
					},
				}
				mockOpsearch.EXPECT().
					SearchBGMByName(gomock.Any(), 123, "dynamo-error", 1, 100).
					Return(searchResults, 1, nil)

				// 设置 Dynamo.BatchGetBGMMetaInfo 的期望返回错误
				mockDynamo.EXPECT().
					BatchGetBGMMetaInfo(gomock.Any(), []int{1}).
					Return(nil, xerrors.New(500, "dynamo error"))
			},
			request: &types.SearchBGMReq{
				Keyword: "dynamo-error",
				Page:    1,
				Size:    10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "Search BGM failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3, mockOpsearch := setupSearchBGMTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3, mockOpsearch)
			}

			// 执行测试
			resp, err := logic.SearchBGM(tt.request)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证分页信息
				assert.Equal(t, tt.expectedTotal, resp.Data.Total)
				assert.Equal(t, tt.request.Page, resp.Data.Page)
				assert.Equal(t, tt.request.Size, resp.Data.Size)

				// 验证返回的BGM列表长度
				assert.Equal(t, tt.expectedItemsLen, len(resp.Data.Items))
			}
		})
	}
}

func TestSortSearchResult(t *testing.T) {
	ctrl, logic, _, _, _, _ := setupSearchBGMTest(t)
	defer ctrl.Finish()

	// 创建测试数据
	bgms := []SearchBGM{
		{
			Score:  1.5,
			Source: opensearch.BGM{ID: 1, Name: "BGM with highest score but medium popularity"},
		},
		{
			Score:  1.0,
			Source: opensearch.BGM{ID: 2, Name: "BGM with medium score and highest popularity"},
		},
		{
			Score:  0.8,
			Source: opensearch.BGM{ID: 3, Name: "BGM with lowest score and low popularity"},
		},
	}

	bgmMetaInfoMap := map[int]*dynamo.BGMMetaInfo{
		1: {UseCount: 5},  // 中等流行度
		2: {UseCount: 10}, // 高流行度
		3: {UseCount: 1},  // 低流行度
	}

	// 执行排序
	sortedBGMIDs, err := logic.sortSearchResult(context.Background(), bgms, bgmMetaInfoMap)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 3, len(sortedBGMIDs))

	// 计算预期的分数
	// BGM 1: (1.5/1.5) * math.Log(5+2) = 1 * math.Log(7) ≈ 1.95
	// BGM 2: (1.0/1.5) * math.Log(10+2) ≈ 0.67 * math.Log(12) ≈ 1.65
	// BGM 3: (0.8/1.5) * math.Log(1+2) ≈ 0.53 * math.Log(3) ≈ 0.58
	// 因此预期排序是: 1, 2, 3

	if len(sortedBGMIDs) >= 3 {
		assert.Equal(t, 1, sortedBGMIDs[0], "BGM 1 should be first due to highest combined score")
		assert.Equal(t, 2, sortedBGMIDs[1], "BGM 2 should be second")
		assert.Equal(t, 3, sortedBGMIDs[2], "BGM 3 should be last due to lowest score and popularity")
	}
}

func TestGetBGMByIDs(t *testing.T) {
	ctrl, logic, mockDB, mockDynamo, mockS3, _ := setupSearchBGMTest(t)
	defer ctrl.Finish()

	// 测试 BGM IDs
	bgmIDs := []int{1, 2, 3}

	// 模拟 DB.GetBGMByIDs
	mockDB.EXPECT().
		GetBGMByIDs(gomock.Any(), gomock.Any()).
		Return([]*pg.AiuBgmTable{
			{
				ID:            1,
				Name:          pgtype.Text{String: "Test BGM 1", Valid: true},
				CoverUrl:      pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
				CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
				Categories:    []int32{1},
			},
			{
				ID:            2,
				Name:          pgtype.Text{String: "Test BGM 2", Valid: true},
				CoverUrl:      pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
				CreatorUserId: pgtype.Int8{Int64: 456, Valid: true},
				Categories:    []int32{2},
			},
		}, nil)

	// 设置其他必要的模拟
	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
		Return(map[string]string{
			"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
			"http://example.com/cover2.jpg": "https://presigned.example.com/cover2.jpg",
		}, nil)

	mockDynamo.EXPECT().
		BatchGetUsers(gomock.Any(), gomock.Any()).
		Return(map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
			456: {ID: 456, UserName: "creator2"},
		}, nil)

	mockDB.EXPECT().
		GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
		Return([]*pg.AiuBgmCategory{
			{ID: 1, Name: "Category 1"},
			{ID: 2, Name: "Category 2"},
		}, nil)

	mockDB.EXPECT().
		CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
		Return([]*pg.CheckBGMsLikedByUserRow{}, nil)

	// 执行测试
	result, err := logic.getBGMByIDs(context.Background(), bgmIDs, 1, 10)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 2, len(result))

	// 验证返回的 BGM 信息
	assert.Equal(t, 1, result[0].ID)
	assert.Equal(t, "Test BGM 1", result[0].Name)
	assert.Equal(t, "https://presigned.example.com/cover1.jpg", result[0].CoverURL)

	assert.Equal(t, 2, result[1].ID)
	assert.Equal(t, "Test BGM 2", result[1].Name)
	assert.Equal(t, "https://presigned.example.com/cover2.jpg", result[1].CoverURL)
}
