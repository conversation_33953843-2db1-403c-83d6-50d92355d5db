package bgm

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type GetBGMsByCategoryLogic struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *coverter
}

func NewGetBGMsByCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBGMsByCategoryLogic {
	return &GetBGMsByCategoryLogic{
		Logger:   logx.WithContext(ctx),
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: newCoverter(svcCtx),
	}
}

func (l *GetBGMsByCategoryLogic) GetBGMsByCategory(
	req *types.GetBGMsByCategoryReq) (*types.GetBGMsByCategoryResp, error) {

	logc.Infof(l.ctx, "req: %+v", req)

	total, err := l.svcCtx.DB.GetBGMsByCategoryCount(l.ctx, int32(req.CategoryID))
	if err != nil {
		logc.Errorf(l.ctx, "failed to get BGMs count by category: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get BGMs count by category")
	}

	bgms, err := l.svcCtx.DB.GetBGMsByCategory(l.ctx, &pg.GetBGMsByCategoryParams{
		CategoryID: int32(req.CategoryID),
		Page:       int32(req.Page),
		PageSize:   int32(req.Size),
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to get BGMs by category: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get BGMs by category")
	}

	// Convert BGMs to response format using batch converter
	bgmInfos, err := l.coverter.BatchToBGMBaseInfo(l.ctx, bgms)
	if err != nil {
		logc.Errorf(l.ctx, "failed to convert BGMs: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to convert BGMs")
	}

	return &types.GetBGMsByCategoryResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.BGMPagination{
			Pagination: utils.GetPageInfo(int(total), req.Page, req.Size),
			Items:      bgmInfos,
		},
	}, nil
}
