package bgm

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupGetCreatedByUserTest(t *testing.T, userID int) (*gomock.Controller, *GetCreatedByUserLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   userID,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewGetCreatedByUserLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3
}

// nolint:revive // there are some unused parameters in test functions
func TestGetCreatedByUser(t *testing.T) {
	tests := []struct {
		name           string
		currentUserID  int
		setupMocks     func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3, int)
		request        *types.GetCreatedByUserReq
		expectedCode   int
		expectedErrMsg string
		expectedTotal  int
		expectedItems  int
	}{
		{
			name:          "successful get own created bgms",
			currentUserID: 123,
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3, currentUserID int) {
				// 设置 DB.GetBGMCreatedByUserCount 的期望
				mockDB.EXPECT().
					GetBGMCreatedByUserCount(gomock.Any(), &pg.GetBGMCreatedByUserCountParams{
						QueryUserId:    int32(currentUserID),
						FilterByPublic: false, // 查询自己创建的，不需要过滤非公开的
					}).
					Return(int64(2), nil)

				// 设置 DB.GetBGMCreatedByUser 的期望
				mockDB.EXPECT().
					GetBGMCreatedByUser(gomock.Any(), &pg.GetBGMCreatedByUserParams{
						QueryUserId:    int32(currentUserID),
						FilterByPublic: false,
						PageSize:       int32(10),
						Page:           int32(1),
					}).
					Return([]*pg.AiuBgmTable{
						{
							ID:             1,
							Name:           pgtype.Text{String: "My BGM 1", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm1.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 180, Valid: true},
							Tags:           []string{"tag1", "tag2"},
							ReferenceCount: pgtype.Int4{Int32: 5, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: int64(currentUserID), Valid: true},
							Categories:     []int32{1},
							Public:         true,
							Nsfw:           false,
							Enable:         pgtype.Bool{Bool: true, Valid: true},
						},
						{
							ID:             2,
							Name:           pgtype.Text{String: "My Private BGM", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm2.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 200, Valid: true},
							Tags:           []string{"tag3", "tag4"},
							ReferenceCount: pgtype.Int4{Int32: 10, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: int64(currentUserID), Valid: true},
							Categories:     []int32{2},
							Public:         false, // 私有的BGM
							Nsfw:           false,
							Enable:         pgtype.Bool{Bool: true, Valid: true},
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
						"http://example.com/cover2.jpg": "https://presigned.example.com/cover2.jpg",
					}, nil)

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						currentUserID: {ID: currentUserID, UserName: "testuser"},
					}, nil)

				// 设置 GetBGMCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
					}, nil)

				// 设置 CheckBGMsLikedByUser 的期望
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{}, nil)
			},
			request: &types.GetCreatedByUserReq{
				UserID: 123,
				Page:   1,
				Size:   10,
			},
			expectedCode:  values.ErrorCodeSuccess,
			expectedTotal: 2,
			expectedItems: 2,
		},
		{
			name:          "get other user's created public bgms",
			currentUserID: 123,
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3, _ int) {
				otherUserID := 456

				// 设置 DB.GetBGMCreatedByUserCount 的期望
				mockDB.EXPECT().
					GetBGMCreatedByUserCount(gomock.Any(), &pg.GetBGMCreatedByUserCountParams{
						QueryUserId:    int32(otherUserID),
						FilterByPublic: true, // 查询其他用户创建的，只能看公开的
					}).
					Return(int64(1), nil)

				// 设置 DB.GetBGMCreatedByUser 的期望
				mockDB.EXPECT().
					GetBGMCreatedByUser(gomock.Any(), &pg.GetBGMCreatedByUserParams{
						QueryUserId:    int32(otherUserID),
						FilterByPublic: true,
						PageSize:       int32(10),
						Page:           int32(1),
					}).
					Return([]*pg.AiuBgmTable{
						{
							ID:             3,
							Name:           pgtype.Text{String: "Other User's Public BGM", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm3.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover3.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 150, Valid: true},
							Tags:           []string{"tag5", "tag6"},
							ReferenceCount: pgtype.Int4{Int32: 3, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: int64(otherUserID), Valid: true},
							Categories:     []int32{3},
							Public:         true,
							Nsfw:           false,
							Enable:         pgtype.Bool{Bool: true, Valid: true},
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/cover3.jpg": "https://presigned.example.com/cover3.jpg",
					}, nil)

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						otherUserID: {ID: otherUserID, UserName: "otheruser"},
					}, nil)

				// 设置 GetBGMCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmCategory{
						{ID: 3, Name: "Category 3", CoverUrl: pgtype.Text{String: "http://example.com/cat3.jpg", Valid: true}},
					}, nil)

				// 设置 CheckBGMsLikedByUser 的期望
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{}, nil)
			},
			request: &types.GetCreatedByUserReq{
				UserID: 456, // 查询其他用户
				Page:   1,
				Size:   10,
			},
			expectedCode:  values.ErrorCodeSuccess,
			expectedTotal: 1,
			expectedItems: 1,
		},
		{
			name:          "get count error",
			currentUserID: 123,
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3, currentUserID int) {
				// 设置 DB.GetBGMCreatedByUserCount 的期望返回错误
				mockDB.EXPECT().
					GetBGMCreatedByUserCount(gomock.Any(), &pg.GetBGMCreatedByUserCountParams{
						QueryUserId:    int32(currentUserID),
						FilterByPublic: false,
					}).
					Return(int64(0), xerrors.New(values.ErrorCodeInternalDBError, "database error"))
			},
			request: &types.GetCreatedByUserReq{
				UserID: 123,
				Page:   1,
				Size:   10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "database error",
		},
		{
			name:          "get bgms error",
			currentUserID: 123,
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3, currentUserID int) {
				// 设置 DB.GetBGMCreatedByUserCount 的期望
				mockDB.EXPECT().
					GetBGMCreatedByUserCount(gomock.Any(), &pg.GetBGMCreatedByUserCountParams{
						QueryUserId:    int32(currentUserID),
						FilterByPublic: false,
					}).
					Return(int64(2), nil)

				// 设置 DB.GetBGMCreatedByUser 的期望返回错误
				mockDB.EXPECT().
					GetBGMCreatedByUser(gomock.Any(), &pg.GetBGMCreatedByUserParams{
						QueryUserId:    int32(currentUserID),
						FilterByPublic: false,
						PageSize:       int32(10),
						Page:           int32(1),
					}).
					Return(nil, xerrors.New(values.ErrorCodeInternalDBError, "database error"))
			},
			request: &types.GetCreatedByUserReq{
				UserID: 123,
				Page:   1,
				Size:   10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "database error",
		},
		{
			name:          "batch convert error",
			currentUserID: 123,
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3, currentUserID int) {
				// 设置 DB.GetBGMCreatedByUserCount 的期望
				mockDB.EXPECT().
					GetBGMCreatedByUserCount(gomock.Any(), &pg.GetBGMCreatedByUserCountParams{
						QueryUserId:    int32(currentUserID),
						FilterByPublic: false,
					}).
					Return(int64(1), nil)

				// 设置 DB.GetBGMCreatedByUser 的期望
				mockDB.EXPECT().
					GetBGMCreatedByUser(gomock.Any(), &pg.GetBGMCreatedByUserParams{
						QueryUserId:    int32(currentUserID),
						FilterByPublic: false,
						PageSize:       int32(10),
						Page:           int32(1),
					}).
					Return([]*pg.AiuBgmTable{
						{
							ID:            1,
							Name:          pgtype.Text{String: "My BGM 1", Valid: true},
							CreatorUserId: pgtype.Int8{Int64: int64(currentUserID), Valid: true},
							Categories:    []int32{1},
							Enable:        pgtype.Bool{Bool: true, Valid: true},
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{}, nil).
					AnyTimes()

				// 设置 GetBGMCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmCategory{
						{ID: 1, Name: "Category 1"},
					}, nil).
					AnyTimes()

				// 添加 CheckBGMsLikedByUser 的期望
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{}, nil).
					AnyTimes()

				// 设置 BatchGetUsers 的期望返回错误
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(values.ErrorCodeInternalDynamoError, "dynamo error"))
			},
			request: &types.GetCreatedByUserReq{
				UserID: 123,
				Page:   1,
				Size:   10,
			},
			expectedCode:   values.ErrorCodeInternalDynamoError,
			expectedErrMsg: "dynamo error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3 := setupGetCreatedByUserTest(t, tt.currentUserID)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3, tt.currentUserID)
			}

			// 执行测试
			resp, err := logic.GetCreatedByUser(tt.request)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				if ok {
					assert.Equal(t, tt.expectedCode, xError.Code)
					assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				}
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证分页信息
				assert.Equal(t, tt.expectedTotal, resp.Data.Total)
				assert.Equal(t, tt.request.Page, resp.Data.Page)
				assert.Equal(t, tt.request.Size, resp.Data.Size)

				// 验证返回的BGM列表长度
				assert.Equal(t, tt.expectedItems, len(resp.Data.Items))
			}
		})
	}
}
