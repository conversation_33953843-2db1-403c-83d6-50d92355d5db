package bgm

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupGetBGMByIDTest(t *testing.T) (*gomock.Controller, *GetBGMByIDLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewGetBGMByIDLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3
}

func TestGetBGMByID(t *testing.T) {
	tests := []struct {
		name           string
		setupMocks     func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3)
		input          *types.GetBGMByIDReq
		expectedCode   int
		expectedErrMsg string
		expectedBGM    *types.BGMBaseInfo
	}{
		{
			name: "success",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.GetBGMByID 的期望
				mockDB.EXPECT().
					GetBGMByID(gomock.Any(), int64(1)).
					Return(&pg.AiuBgmTable{
						ID:             1,
						Name:           pgtype.Text{String: "Test BGM", Valid: true},
						Url:            pgtype.Text{String: "http://example.com/bgm.mp3", Valid: true},
						CoverUrl:       pgtype.Text{String: "http://example.com/cover.jpg", Valid: true},
						Duration:       pgtype.Int4{Int32: 180, Valid: true},
						Tags:           []string{"tag1", "tag2"},
						ReferenceCount: pgtype.Int4{Int32: 5, Valid: true},
						CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
						Public:         true,
						Nsfw:           false,
						Categories:     []int32{1, 2},
					}, nil)

				// 设置获取封面URL的期望
				mockS3.EXPECT().
					GetPresignedURL(gomock.Any(), "http://example.com/cover.jpg", s3.Original).
					Return("https://presigned.example.com/cover.jpg", nil)

				// 设置获取创建者信息的期望
				mockDynamo.EXPECT().
					GetUser(gomock.Any(), 123).
					Return(&dynamo.User{ID: 123, UserName: "creator"}, nil)

				// 设置获取分类的期望
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), []int32{1, 2}).
					Return([]*pg.AiuBgmCategory{
						{
							ID:       1,
							Name:     "Category 1",
							CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true},
						},
						{
							ID:       2,
							Name:     "Category 2",
							CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true},
						},
					}, nil)

				// 设置获取点赞信息的期望
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{}, nil)
			},
			input:        &types.GetBGMByIDReq{ID: 1},
			expectedCode: values.ErrorCodeSuccess,
			expectedBGM: &types.BGMBaseInfo{
				ID:              1,
				Name:            "Test BGM",
				URL:             "http://example.com/bgm.mp3",
				CoverURL:        "https://presigned.example.com/cover.jpg",
				Duration:        180,
				Tags:            []string{"tag1", "tag2"},
				ReferenceCount:  5,
				CreatorUserID:   123,
				CreatorUserName: "creator",
				Public:          true,
				NSFW:            false,
				Liked:           false,
				Categories: []*types.BGMCategory{
					{ID: 1, Name: "Category 1", CoverURL: "http://example.com/cat1.jpg"},
					{ID: 2, Name: "Category 2", CoverURL: "http://example.com/cat2.jpg"},
				},
			},
		},
		{
			name: "bgm not found",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetBGMByID 的期望，返回 ErrNoRows
				mockDB.EXPECT().
					GetBGMByID(gomock.Any(), int64(999)).
					Return(nil, pgx.ErrNoRows)
			},
			input:          &types.GetBGMByIDReq{ID: 999},
			expectedCode:   values.ErrorCodeNotFound,
			expectedErrMsg: "bgm not found",
		},
		{
			name: "database error",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetBGMByID 的期望，返回一般性数据库错误
				mockDB.EXPECT().
					GetBGMByID(gomock.Any(), int64(2)).
					Return(nil, xerrors.New(500, "database error"))
			},
			input:          &types.GetBGMByIDReq{ID: 2},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to get bgm by id",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3 := setupGetBGMByIDTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3)
			}

			// 执行测试
			resp, err := logic.GetBGMByID(tt.input)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证返回的 BGM 信息是否正确
				if tt.expectedBGM != nil {
					assert.Equal(t, tt.expectedBGM.ID, resp.Data.ID)
					assert.Equal(t, tt.expectedBGM.Name, resp.Data.Name)
					assert.Equal(t, tt.expectedBGM.URL, resp.Data.URL)
					assert.Equal(t, tt.expectedBGM.CoverURL, resp.Data.CoverURL)
					assert.Equal(t, tt.expectedBGM.Duration, resp.Data.Duration)
					assert.Equal(t, tt.expectedBGM.Tags, resp.Data.Tags)
					assert.Equal(t, tt.expectedBGM.ReferenceCount, resp.Data.ReferenceCount)
					assert.Equal(t, tt.expectedBGM.CreatorUserID, resp.Data.CreatorUserID)
					assert.Equal(t, tt.expectedBGM.CreatorUserName, resp.Data.CreatorUserName)
					assert.Equal(t, tt.expectedBGM.Public, resp.Data.Public)
					assert.Equal(t, tt.expectedBGM.NSFW, resp.Data.NSFW)
					assert.Equal(t, tt.expectedBGM.Liked, resp.Data.Liked)
					assert.Equal(t, len(tt.expectedBGM.Categories), len(resp.Data.Categories))
				}
			}
		})
	}
}
