package bgm

import (
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type GetBGMByIDLogic struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *coverter
}

func NewGetBGMByIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBGMByIDLogic {
	return &GetBGMByIDLogic{
		Logger:   logx.WithContext(ctx),
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: newCoverter(svcCtx),
	}
}

func (l *GetBGMByIDLogic) GetBGMByID(req *types.GetBGMByIDReq) (*types.GetBGMByIDResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	bgm, err := l.svcCtx.DB.GetBGMByID(l.ctx, int64(req.ID))
	if err == pgx.ErrNoRows {
		return nil, xerrors.New(values.ErrorCodeNotFound, "bgm not found")
	}
	if err != nil {
		logc.Errorf(l.ctx, "failed to get bgm by id: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get bgm by id")
	}

	bgmInfo, err := l.coverter.ToBGMBaseInfo(l.ctx, bgm)
	if err != nil {
		logc.Errorf(l.ctx, "failed to convert bgm to bgm info: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to convert bgm to bgm info")
	}

	return &types.GetBGMByIDResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: *bgmInfo,
	}, nil
}
