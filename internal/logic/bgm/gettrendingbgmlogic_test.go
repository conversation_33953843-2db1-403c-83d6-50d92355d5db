package bgm

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupGetTrendingBGMTest(t *testing.T) (*gomock.Controller, *GetTrendingBGMLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewGetTrendingBGMLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3
}

func TestGetTrendingBGM(t *testing.T) {
	tests := []struct {
		name           string
		setupMocks     func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3)
		request        *types.GetTrendingBGMReq
		expectedCode   int
		expectedErrMsg string
		expectedTotal  int
		expectedItems  int
	}{
		{
			name: "successful get trending BGM",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 Dynamo.GetTopInfo 的期望
				topBGMs := []int32{1, 2, 3}
				topBGMsJSON, _ := json.Marshal(topBGMs)
				mockDynamo.EXPECT().
					GetTopInfo(gomock.Any(), "sekai_backend_bgm_top30").
					Return(string(topBGMsJSON), nil)

				// 设置 DB.GetBGMByIDs 的期望
				mockDB.EXPECT().
					GetBGMByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmTable{
						{
							ID:             1,
							Name:           pgtype.Text{String: "Trending BGM 1", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm1.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 180, Valid: true},
							Tags:           []string{"tag1", "tag2"},
							ReferenceCount: pgtype.Int4{Int32: 5, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
							Categories:     []int32{1},
							Public:         true,
							Nsfw:           false,
							Enable:         pgtype.Bool{Bool: true, Valid: true},
						},
						{
							ID:             2,
							Name:           pgtype.Text{String: "Trending BGM 2", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm2.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 200, Valid: true},
							Tags:           []string{"tag3", "tag4"},
							ReferenceCount: pgtype.Int4{Int32: 10, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 456, Valid: true},
							Categories:     []int32{2},
							Public:         true,
							Nsfw:           false,
							Enable:         pgtype.Bool{Bool: true, Valid: true},
						},
						{
							ID:             3,
							Name:           pgtype.Text{String: "Trending BGM 3", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm3.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover3.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 150, Valid: true},
							Tags:           []string{"tag5", "tag6"},
							ReferenceCount: pgtype.Int4{Int32: 3, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 789, Valid: true},
							Categories:     []int32{3},
							Public:         true,
							Nsfw:           false,
							Enable:         pgtype.Bool{Bool: true, Valid: true},
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
						"http://example.com/cover2.jpg": "https://presigned.example.com/cover2.jpg",
						"http://example.com/cover3.jpg": "https://presigned.example.com/cover3.jpg",
					}, nil)

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "creator1"},
						456: {ID: 456, UserName: "creator2"},
						789: {ID: 789, UserName: "creator3"},
					}, nil)

				// 设置 GetBGMCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
						{ID: 3, Name: "Category 3", CoverUrl: pgtype.Text{String: "http://example.com/cat3.jpg", Valid: true}},
					}, nil)

				// 设置 CheckBGMsLikedByUser 的期望
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{}, nil)
			},
			request: &types.GetTrendingBGMReq{
				Page: 1,
				Size: 10,
			},
			expectedCode:  values.ErrorCodeSuccess,
			expectedTotal: 3,
			expectedItems: 3,
		},
		{
			name: "dynamo error",
			setupMocks: func(_ *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 Dynamo.GetTopInfo 的期望返回错误
				mockDynamo.EXPECT().
					GetTopInfo(gomock.Any(), "sekai_backend_bgm_top30").
					Return("", xerrors.New(values.ErrorCodeInternalDynamoError, "dynamo error"))
			},
			request: &types.GetTrendingBGMReq{
				Page: 1,
				Size: 10,
			},
			expectedCode:   values.ErrorCodeInternalDynamoError,
			expectedErrMsg: "get top info error",
		},
		{
			name: "unmarshal error",
			setupMocks: func(_ *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 Dynamo.GetTopInfo 的期望返回无效的JSON
				mockDynamo.EXPECT().
					GetTopInfo(gomock.Any(), "sekai_backend_bgm_top30").
					Return("invalid json", nil)
			},
			request: &types.GetTrendingBGMReq{
				Page: 1,
				Size: 10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "internal server error",
		},
		{
			name: "empty bgm ids",
			setupMocks: func(_ *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 Dynamo.GetTopInfo 的期望返回空数组
				mockDynamo.EXPECT().
					GetTopInfo(gomock.Any(), "sekai_backend_bgm_top30").
					Return("[]", nil)
			},
			request: &types.GetTrendingBGMReq{
				Page: 1,
				Size: 10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "failed to get trending bgms",
		},
		{
			name: "database error",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 Dynamo.GetTopInfo 的期望
				topBGMs := []int32{1, 2, 3}
				topBGMsJSON, _ := json.Marshal(topBGMs)
				mockDynamo.EXPECT().
					GetTopInfo(gomock.Any(), "sekai_backend_bgm_top30").
					Return(string(topBGMsJSON), nil)

				// 设置 DB.GetBGMByIDs 的期望返回错误
				mockDB.EXPECT().
					GetBGMByIDs(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(values.ErrorCodeInternalServerError, "database error"))
			},
			request: &types.GetTrendingBGMReq{
				Page: 1,
				Size: 10,
			},
			expectedCode:   values.ErrorCodeInternalServerError,
			expectedErrMsg: "internal server error",
		},
		{
			name: "successful get but pagination out of range",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 Dynamo.GetTopInfo 的期望
				topBGMs := []int32{1, 2, 3}
				topBGMsJSON, _ := json.Marshal(topBGMs)
				mockDynamo.EXPECT().
					GetTopInfo(gomock.Any(), "sekai_backend_bgm_top30").
					Return(string(topBGMsJSON), nil)

				// 设置 DB.GetBGMByIDs 的期望
				mockDB.EXPECT().
					GetBGMByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmTable{
						{
							ID:     1,
							Name:   pgtype.Text{String: "Trending BGM 1", Valid: true},
							Public: true,
							Nsfw:   false,
							Enable: pgtype.Bool{Bool: true, Valid: true},
						},
					}, nil)
			},
			request: &types.GetTrendingBGMReq{
				Page: 2, // 请求页码超出实际数据范围
				Size: 10,
			},
			expectedCode:  values.ErrorCodeSuccess,
			expectedTotal: 1,
			expectedItems: 0, // 期望返回0条数据，因为已经超出范围
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3 := setupGetTrendingBGMTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3)
			}

			// 执行测试
			resp, err := logic.GetTrendingBGM(tt.request)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证分页信息
				assert.Equal(t, tt.expectedTotal, resp.Data.Total)
				assert.Equal(t, tt.request.Page, resp.Data.Page)
				assert.Equal(t, tt.request.Size, resp.Data.Size)

				// 验证返回的BGM列表长度
				assert.Equal(t, tt.expectedItems, len(resp.Data.Items))
			}
		})
	}
}
