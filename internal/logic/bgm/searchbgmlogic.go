package bgm

import (
	"context"
	"fmt"
	"math"
	"sort"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/opensearch"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type SearchBGMLogic struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *coverter
}

func NewSearchBGMLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchBGMLogic {
	return &SearchBGMLogic{
		Logger:   logx.WithContext(ctx),
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: newCoverter(svcCtx),
	}
}

func (l *SearchBGMLogic) SearchBGM(req *types.SearchBGMReq) (*types.SearchBGMResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	userID := servermsg.GetCurrentUser(l.ctx).UserID
	// 搜索100条，然后排序，只返回前50条
	bgms, total, err := l.svcCtx.Opsearch.SearchBGMByName(l.ctx, userID, req.Keyword, 1, 100)
	if err != nil {
		logc.Errorf(l.ctx, "SearchBGMByName failed: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "Search BGM failed")
	}

	logc.Debugf(l.ctx, "total: %d", total)
	if len(bgms) == 0 {
		logc.Infof(l.ctx, "no bgm found")
		return &types.SearchBGMResp{
			ResponseBase: types.ResponseBase{
				Code: values.ErrorCodeSuccess,
				Msg:  "success",
			},
			Data: types.BGMPagination{
				Pagination: utils.GetPageInfo(total, req.Page, req.Size),
				Items:      nil,
			},
		}, nil
	}

	// 获取bgmID列表
	bgmIDs := lo.Map(bgms, func(bgm opensearch.SearchHit[opensearch.BGM], _ int) int {
		return bgm.Source.ID
	})
	bgmMetaInfoMap, err := l.svcCtx.Dynamo.BatchGetBGMMetaInfo(l.ctx, bgmIDs)
	if err != nil {
		logc.Errorf(l.ctx, "BatchGetBGMMetaInfo failed: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "Search BGM failed")
	}

	// 排序
	bgmIDs, err = l.sortSearchResult(l.ctx, bgms, bgmMetaInfoMap)
	if err != nil {
		logc.Errorf(l.ctx, "sortSearchResult failed: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "Search BGM failed")
	}

	// 获取排序后的bgm
	data, err := l.getBGMByIDs(l.ctx, bgmIDs[:min(len(bgmIDs), 50)], req.Page, req.Size)
	if err != nil {
		logc.Errorf(l.ctx, "getBGMByIDs failed: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "Search BGM failed")
	}

	return &types.SearchBGMResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.BGMPagination{
			Pagination: utils.GetPageInfo(total, req.Page, req.Size),
			Items:      data,
		},
	}, nil
}

// BGMSearchModel represents a BGM search result with scoring
type bgmSearchModel struct {
	bgmItem    opensearch.SearchHit[opensearch.BGM]
	maxScore   float64
	popularity int
}

// score calculates normalized search score including popularity
func (m *bgmSearchModel) score() float64 {
	maxScore := float64(1)
	if m.maxScore != 0 {
		maxScore = m.maxScore
	}
	return (m.bgmItem.Score / maxScore) * math.Log(float64(m.popularity+2))
}

// String returns debug string representation
func (m *bgmSearchModel) String() string {
	return fmt.Sprintf("bgm_name: %s, id: %d, score: %f, es_score: %f, popularity: %d",
		m.bgmItem.Source.Name,
		m.bgmItem.Source.ID,
		m.score(),
		m.bgmItem.Score,
		m.popularity)
}

// SearchBGM is a type alias for opensearch.SearchHit[opensearch.BGM]
type SearchBGM = opensearch.SearchHit[opensearch.BGM]

// sortSearchResult 返回排序后的bgmID列表
func (l *SearchBGMLogic) sortSearchResult(ctx context.Context,
	bgms []SearchBGM, bgmMetaInfoMap map[int]*dynamo.BGMMetaInfo) ([]int, error) {

	// Create search models
	searchModels := make([]*bgmSearchModel, 0, len(bgms))
	for _, bgm := range bgms {
		popularity := 0
		if meta, ok := bgmMetaInfoMap[bgm.Source.ID]; ok {
			popularity = meta.UseCount
		}
		searchModels = append(searchModels, &bgmSearchModel{
			bgmItem:    bgm,
			maxScore:   bgms[0].Score, // First hit has max score
			popularity: popularity,
		})
	}

	// Sort by score descending
	sort.Slice(searchModels, func(i, j int) bool {
		return searchModels[i].score() > searchModels[j].score()
	})

	// Log top 10 results
	for i, model := range searchModels {
		if i >= 10 {
			break
		}
		logc.Infof(ctx, "search top 10 bgm, %d: %s", i+1, model.String())
	}

	bgmIDs := lo.Map(searchModels, func(model *bgmSearchModel, _ int) int {
		return model.bgmItem.Source.ID
	})
	return bgmIDs, nil
}

func (l *SearchBGMLogic) getBGMByIDs(ctx context.Context,
	bgmIDs []int, page int, size int) ([]*types.BGMBaseInfo, error) {

	bmgs, err := l.svcCtx.DB.GetBGMByIDs(l.ctx, &pg.GetBGMByIDsParams{
		Ids:            utils.CovertNumberSlice[int, int32](bgmIDs),
		Nsfw:           false,
		FilterByNsfw:   true,
		Enable:         pgtype.Bool{Bool: true, Valid: true},
		FilterByEnable: true,
		Public:         true,
		FilterByPublic: true,
	})
	if err != nil {
		logc.Errorf(ctx, "GetBGMByIDs error: %+v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "internal server error")
	}

	// 批量转换为BGMBaseInfo
	start := (page - 1) * size
	end := min(page*size, len(bmgs))
	var data []*types.BGMBaseInfo
	if start < len(bmgs) {
		data, err = l.coverter.BatchToBGMBaseInfo(l.ctx, bmgs[start:end])
		if err != nil {
			logc.Errorf(ctx, "BatchToBGMBaseInfo error: %+v", err)
			return nil, xerrors.New(values.ErrorCodeInternalServerError, "internal server error")
		}
	}

	return data, nil
}
