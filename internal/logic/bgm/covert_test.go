package bgm

import (
	"context"
	"testing"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func setupCovertTest(t *testing.T) (*gomock.Controller, *coverter, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	coverter := newCoverter(svcCtx)

	return ctrl, coverter, mockDB, mockDynamo, mockS3
}

func createTestServerMsgContext(userID int) context.Context {
	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   userID,
			UserName: "testuser",
		},
	}
	return context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)
}

func TestToBGMCategories(t *testing.T) {
	ctrl, converter, _, _, _ := setupCovertTest(t)
	defer ctrl.Finish()

	testCategories := []*pg.AiuBgmCategory{
		{
			ID:       1,
			Name:     "Category 1",
			CoverUrl: pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
		},
		{
			ID:       2,
			Name:     "Category 2",
			CoverUrl: pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
		},
	}

	result := converter.ToBGMCategories(context.Background(), testCategories)

	assert.Equal(t, 2, len(result))
	assert.Equal(t, 1, result[0].ID)
	assert.Equal(t, "Category 1", result[0].Name)
	assert.Equal(t, "http://example.com/cover1.jpg", result[0].CoverURL)
	assert.Equal(t, 2, result[1].ID)
	assert.Equal(t, "Category 2", result[1].Name)
	assert.Equal(t, "http://example.com/cover2.jpg", result[1].CoverURL)
}

func TestGetCoverURL(t *testing.T) {
	ctrl, converter, _, _, mockS3 := setupCovertTest(t)
	defer ctrl.Finish()

	ctx := context.Background()
	testBgm := &pg.AiuBgmTable{
		CoverUrl: pgtype.Text{String: "http://example.com/cover.jpg", Valid: true},
	}

	// 设置 mock 期望
	mockS3.EXPECT().
		GetPresignedURL(gomock.Any(), "http://example.com/cover.jpg", s3.Original).
		Return("https://presigned.example.com/cover.jpg", nil)

	coverURL, err := converter.getCoverURL(ctx, testBgm)

	assert.NoError(t, err)
	assert.Equal(t, "https://presigned.example.com/cover.jpg", coverURL)
}

func TestGetCreator(t *testing.T) {
	ctrl, converter, _, mockDynamo, _ := setupCovertTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	t.Run("with valid creator ID", func(t *testing.T) {
		testBgm := &pg.AiuBgmTable{
			CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
		}

		mockDynamo.EXPECT().
			GetUser(gomock.Any(), 123).
			Return(&dynamo.User{ID: 123, UserName: "creator"}, nil)

		creator, err := converter.getCreator(ctx, testBgm)

		assert.NoError(t, err)
		assert.Equal(t, 123, creator.ID)
		assert.Equal(t, "creator", creator.UserName)
	})

	t.Run("with invalid creator ID", func(t *testing.T) {
		testBgm := &pg.AiuBgmTable{
			CreatorUserId: pgtype.Int8{Int64: 0, Valid: false},
		}

		creator, err := converter.getCreator(ctx, testBgm)

		assert.NoError(t, err)
		assert.Equal(t, 0, creator.ID)
		assert.Equal(t, "unknown", creator.UserName)
	})
}

func TestGetCategories(t *testing.T) {
	ctrl, converter, mockDB, _, _ := setupCovertTest(t)
	defer ctrl.Finish()

	ctx := context.Background()
	testBgm := &pg.AiuBgmTable{
		Categories: []int32{1, 2},
	}

	testCategories := []*pg.AiuBgmCategory{
		{
			ID:       1,
			Name:     "Category 1",
			CoverUrl: pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
		},
		{
			ID:       2,
			Name:     "Category 2",
			CoverUrl: pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
		},
	}

	mockDB.EXPECT().
		GetBGMCategoriesByIDs(gomock.Any(), []int32{1, 2}).
		Return(testCategories, nil)

	categories, err := converter.getCategories(ctx, testBgm)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(categories))
	assert.Equal(t, 1, categories[0].ID)
	assert.Equal(t, "Category 1", categories[0].Name)
}

func TestGetBatchLikedInfo(t *testing.T) {
	ctrl, converter, mockDB, _, _ := setupCovertTest(t)
	defer ctrl.Finish()

	userID := 123
	ctx := createTestServerMsgContext(userID)

	testBgms := []*pg.AiuBgmTable{
		{ID: 1},
		{ID: 2},
	}

	now := time.Now()
	likedInfo := []*pg.CheckBGMsLikedByUserRow{
		{BgmID: 1, CreatedAt: pgtype.Timestamptz{Time: now, Valid: true}},
	}

	mockDB.EXPECT().
		CheckBGMsLikedByUser(gomock.Any(), &pg.CheckBGMsLikedByUserParams{
			UserID: int32(userID),
			BgmIDs: []int32{1, 2},
		}).
		Return(likedInfo, nil)

	result, err := converter.getBatchLikedInfo(ctx, testBgms)

	assert.NoError(t, err)
	assert.Equal(t, 1, len(result))
	assert.False(t, result[1].IsZero()) // BGM 1 被点赞
	assert.True(t, result[2].IsZero())  // BGM 2 未被点赞
}

func TestToBGMBaseInfo(t *testing.T) {
	ctrl, converter, mockDB, mockDynamo, mockS3 := setupCovertTest(t)
	defer ctrl.Finish()

	userID := 123
	ctx := createTestServerMsgContext(userID)

	testBgm := &pg.AiuBgmTable{
		ID:             1,
		Name:           pgtype.Text{String: "Test BGM", Valid: true},
		Url:            pgtype.Text{String: "http://example.com/bgm.mp3", Valid: true},
		CoverUrl:       pgtype.Text{String: "http://example.com/cover.jpg", Valid: true},
		Duration:       pgtype.Int4{Int32: 180, Valid: true},
		Tags:           []string{"tag1", "tag2"},
		ReferenceCount: pgtype.Int4{Int32: 5, Valid: true},
		CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
		Public:         true,
		Nsfw:           false,
		Categories:     []int32{1, 2},
	}

	// 设置 mock 期望
	mockS3.EXPECT().
		GetPresignedURL(gomock.Any(), "http://example.com/cover.jpg", s3.Original).
		Return("https://presigned.example.com/cover.jpg", nil)

	mockDynamo.EXPECT().
		GetUser(gomock.Any(), 123).
		Return(&dynamo.User{ID: 123, UserName: "creator"}, nil)

	testCategories := []*pg.AiuBgmCategory{
		{
			ID:       1,
			Name:     "Category 1",
			CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true},
		},
		{
			ID:       2,
			Name:     "Category 2",
			CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true},
		},
	}

	mockDB.EXPECT().
		GetBGMCategoriesByIDs(gomock.Any(), []int32{1, 2}).
		Return(testCategories, nil)

	now := time.Now()
	likedInfo := []*pg.CheckBGMsLikedByUserRow{
		{BgmID: 1, CreatedAt: pgtype.Timestamptz{Time: now, Valid: true}},
	}

	mockDB.EXPECT().
		CheckBGMsLikedByUser(gomock.Any(), &pg.CheckBGMsLikedByUserParams{
			UserID: int32(userID),
			BgmIDs: []int32{1},
		}).
		Return(likedInfo, nil)

	result, err := converter.ToBGMBaseInfo(ctx, testBgm)

	assert.NoError(t, err)
	assert.Equal(t, 1, result.ID)
	assert.Equal(t, "Test BGM", result.Name)
	assert.Equal(t, "http://example.com/bgm.mp3", result.URL)
	assert.Equal(t, "https://presigned.example.com/cover.jpg", result.CoverURL)
	assert.Equal(t, 180, result.Duration)
	assert.Equal(t, []string{"tag1", "tag2"}, result.Tags)
	assert.Equal(t, 5, result.ReferenceCount)
	assert.Equal(t, 123, result.CreatorUserID)
	assert.Equal(t, "creator", result.CreatorUserName)
	assert.True(t, result.Public)
	assert.False(t, result.NSFW)
	assert.True(t, result.Liked)
	assert.Equal(t, 2, len(result.Categories))
}

func TestBatchToBGMBaseInfo(t *testing.T) {
	ctrl, converter, mockDB, mockDynamo, mockS3 := setupCovertTest(t)
	defer ctrl.Finish()

	userID := 123
	ctx := createTestServerMsgContext(userID)

	testBgms := []*pg.AiuBgmTable{
		{
			ID:             1,
			Name:           pgtype.Text{String: "BGM 1", Valid: true},
			Url:            pgtype.Text{String: "http://example.com/bgm1.mp3", Valid: true},
			CoverUrl:       pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
			Duration:       pgtype.Int4{Int32: 180, Valid: true},
			Tags:           []string{"tag1", "tag2"},
			ReferenceCount: pgtype.Int4{Int32: 5, Valid: true},
			CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
			Public:         true,
			Nsfw:           false,
			Categories:     []int32{1},
		},
		{
			ID:             2,
			Name:           pgtype.Text{String: "BGM 2", Valid: true},
			Url:            pgtype.Text{String: "http://example.com/bgm2.mp3", Valid: true},
			CoverUrl:       pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
			Duration:       pgtype.Int4{Int32: 200, Valid: true},
			Tags:           []string{"tag3", "tag4"},
			ReferenceCount: pgtype.Int4{Int32: 10, Valid: true},
			CreatorUserId:  pgtype.Int8{Int64: 456, Valid: true},
			Public:         false,
			Nsfw:           true,
			Categories:     []int32{2},
		},
	}

	// 设置 mock 期望
	coverURLs := []string{
		"http://example.com/cover1.jpg",
		"http://example.com/cover2.jpg",
	}
	coverURLMap := map[string]string{
		"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
		"http://example.com/cover2.jpg": "https://presigned.example.com/cover2.jpg",
	}
	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), coverURLs, s3.Original).
		Return(coverURLMap, nil)

	mockDynamo.EXPECT().
		BatchGetUsers(gomock.Any(), []int{123, 456}).
		Return(map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
			456: {ID: 456, UserName: "creator2"},
		}, nil)

	testCategories := []*pg.AiuBgmCategory{
		{
			ID:       1,
			Name:     "Category 1",
			CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true},
		},
		{
			ID:       2,
			Name:     "Category 2",
			CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true},
		},
	}

	mockDB.EXPECT().
		GetBGMCategoriesByIDs(gomock.Any(), []int32{1, 2}).
		Return(testCategories, nil)

	now := time.Now()
	likedInfo := []*pg.CheckBGMsLikedByUserRow{
		{BgmID: 1, CreatedAt: pgtype.Timestamptz{Time: now, Valid: true}},
	}

	mockDB.EXPECT().
		CheckBGMsLikedByUser(gomock.Any(), &pg.CheckBGMsLikedByUserParams{
			UserID: int32(userID),
			BgmIDs: []int32{1, 2},
		}).
		Return(likedInfo, nil)

	result, err := converter.BatchToBGMBaseInfo(ctx, testBgms)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))

	// 检查第一个 BGM
	assert.Equal(t, 1, result[0].ID)
	assert.Equal(t, "BGM 1", result[0].Name)
	assert.Equal(t, "https://presigned.example.com/cover1.jpg", result[0].CoverURL)
	assert.True(t, result[0].Public)
	assert.True(t, result[0].Liked)
	assert.Equal(t, 123, result[0].CreatorUserID)
	assert.Equal(t, "creator1", result[0].CreatorUserName)
	assert.Equal(t, 1, len(result[0].Categories))

	// 检查第二个 BGM
	assert.Equal(t, 2, result[1].ID)
	assert.Equal(t, "BGM 2", result[1].Name)
	assert.Equal(t, "https://presigned.example.com/cover2.jpg", result[1].CoverURL)
	assert.False(t, result[1].Public)
	assert.False(t, result[1].Liked)
	assert.Equal(t, 456, result[1].CreatorUserID)
	assert.Equal(t, "creator2", result[1].CreatorUserName)
	assert.Equal(t, 1, len(result[1].Categories))
}

func TestGetBatchUsers(t *testing.T) {
	ctrl, converter, _, mockDynamo, _ := setupCovertTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	testBgms := []*pg.AiuBgmTable{
		{CreatorUserId: pgtype.Int8{Int64: 123, Valid: true}},
		{CreatorUserId: pgtype.Int8{Int64: 456, Valid: true}},
		{CreatorUserId: pgtype.Int8{Int64: 123, Valid: true}}, // 重复的用户ID
	}

	mockDynamo.EXPECT().
		BatchGetUsers(gomock.Any(), []int{123, 456}).
		Return(map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
			456: {ID: 456, UserName: "creator2"},
		}, nil)

	result, err := converter.getBatchUsers(ctx, testBgms)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(result))
	assert.Equal(t, 123, result[123].ID)
	assert.Equal(t, "creator1", result[123].UserName)
	assert.Equal(t, 456, result[456].ID)
	assert.Equal(t, "creator2", result[456].UserName)
}

func TestGetBatchCategories(t *testing.T) {
	ctrl, converter, mockDB, _, _ := setupCovertTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	testBgms := []*pg.AiuBgmTable{
		{Categories: []int32{1, 2}},
		{Categories: []int32{2, 3}},
	}

	testCategories := []*pg.AiuBgmCategory{
		{ID: 1, Name: "Category 1"},
		{ID: 2, Name: "Category 2"},
		{ID: 3, Name: "Category 3"},
	}

	mockDB.EXPECT().
		GetBGMCategoriesByIDs(gomock.Any(), []int32{1, 2, 3}).
		Return(testCategories, nil)

	result, err := converter.getBatchCategories(ctx, testBgms)

	assert.NoError(t, err)
	assert.Equal(t, 3, len(result))
	assert.Equal(t, "Category 1", result[1].Name)
	assert.Equal(t, "Category 2", result[2].Name)
	assert.Equal(t, "Category 3", result[3].Name)
}

func TestBuildBatchBGMBaseInfo(t *testing.T) {
	ctrl, converter, _, _, _ := setupCovertTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	testBgms := []*pg.AiuBgmTable{
		{
			ID:             1,
			Name:           pgtype.Text{String: "BGM 1", Valid: true},
			Url:            pgtype.Text{String: "http://example.com/bgm1.mp3", Valid: true},
			CoverUrl:       pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
			Duration:       pgtype.Int4{Int32: 180, Valid: true},
			Tags:           []string{"tag1", "tag2"},
			ReferenceCount: pgtype.Int4{Int32: 5, Valid: true},
			CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
			Public:         true,
			Nsfw:           false,
			Categories:     []int32{1, 2},
		},
	}

	data := &batchData{
		coverURLMap: map[string]string{
			"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
		},
		userMap: map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
		},
		categoryMap: map[int32]*pg.AiuBgmCategory{
			1: {ID: 1, Name: "Category 1"},
			2: {ID: 2, Name: "Category 2"},
		},
		likedInfoMap: map[int32]time.Time{
			1: time.Now(),
		},
	}

	result := converter.buildBatchBGMBaseInfo(ctx, testBgms, data)

	assert.Equal(t, 1, len(result))
	assert.Equal(t, 1, result[0].ID)
	assert.Equal(t, "BGM 1", result[0].Name)
	assert.Equal(t, "https://presigned.example.com/cover1.jpg", result[0].CoverURL)
	assert.Equal(t, 180, result[0].Duration)
	assert.Equal(t, []string{"tag1", "tag2"}, result[0].Tags)
	assert.Equal(t, 5, result[0].ReferenceCount)
	assert.Equal(t, 123, result[0].CreatorUserID)
	assert.Equal(t, "creator1", result[0].CreatorUserName)
	assert.True(t, result[0].Public)
	assert.False(t, result[0].NSFW)
	assert.True(t, result[0].Liked)
	assert.Equal(t, 2, len(result[0].Categories))
}

func TestGetBatchData(t *testing.T) {
	ctrl, converter, mockDB, mockDynamo, mockS3 := setupCovertTest(t)
	defer ctrl.Finish()

	userID := 123
	ctx := createTestServerMsgContext(userID)

	testBgms := []*pg.AiuBgmTable{
		{
			ID:            1,
			CoverUrl:      pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
			CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
			Categories:    []int32{1},
		},
		{
			ID:            2,
			CoverUrl:      pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
			CreatorUserId: pgtype.Int8{Int64: 456, Valid: true},
			Categories:    []int32{2},
		},
	}

	// 设置 mock 期望
	coverURLs := []string{
		"http://example.com/cover1.jpg",
		"http://example.com/cover2.jpg",
	}
	coverURLMap := map[string]string{
		"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
		"http://example.com/cover2.jpg": "https://presigned.example.com/cover2.jpg",
	}
	mockS3.EXPECT().
		BatchGetPresignedURL(gomock.Any(), coverURLs, s3.Original).
		Return(coverURLMap, nil)

	mockDynamo.EXPECT().
		BatchGetUsers(gomock.Any(), []int{123, 456}).
		Return(map[int]*dynamo.User{
			123: {ID: 123, UserName: "creator1"},
			456: {ID: 456, UserName: "creator2"},
		}, nil)

	testCategories := []*pg.AiuBgmCategory{
		{ID: 1, Name: "Category 1"},
		{ID: 2, Name: "Category 2"},
	}

	mockDB.EXPECT().
		GetBGMCategoriesByIDs(gomock.Any(), []int32{1, 2}).
		Return(testCategories, nil)

	now := time.Now()
	likedInfo := []*pg.CheckBGMsLikedByUserRow{
		{BgmID: 1, CreatedAt: pgtype.Timestamptz{Time: now, Valid: true}},
	}

	mockDB.EXPECT().
		CheckBGMsLikedByUser(gomock.Any(), &pg.CheckBGMsLikedByUserParams{
			UserID: int32(userID),
			BgmIDs: []int32{1, 2},
		}).
		Return(likedInfo, nil)

	result, err := converter.getBatchData(ctx, testBgms)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 2, len(result.coverURLMap))
	assert.Equal(t, 2, len(result.userMap))
	assert.Equal(t, 2, len(result.categoryMap))
	assert.Equal(t, 1, len(result.likedInfoMap))
}
