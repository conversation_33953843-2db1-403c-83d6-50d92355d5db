package bgm

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupDeleteBGMTest(t *testing.T) (*gomock.Controller, *DeleteBGMLogic, *pgmock.MockQuerier) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)

	svcCtx := &svc.ServiceContext{
		DB: mockDB,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   123,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewDeleteBGMLogic(ctx, svcCtx)

	return ctrl, logic, mockDB
}

func TestDeleteBGM(t *testing.T) {
	tests := []struct {
		name           string
		setupMocks     func(*pgmock.MockQuerier)
		request        *types.DeleteBGMReq
		expectedCode   int
		expectedErrMsg string
	}{
		{
			name: "successful delete",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				// 设置成功删除的期望
				mockDB.EXPECT().
					SoftDeleteBGM(gomock.Any(), &pg.SoftDeleteBGMParams{
						ID:            int64(101),
						CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
					}).
					Return(&pg.AiuBgmTable{
						ID:            101,
						CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
						Enable:        pgtype.Bool{Bool: false, Valid: true}, // 软删除会将 Enable 设置为 false
					}, nil)
			},
			request: &types.DeleteBGMReq{
				ID: 101,
			},
			expectedCode: values.ErrorCodeSuccess,
		},
		{
			name: "not bgm creator",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				// 模拟用户不是 BGM 创建者的情况
				mockDB.EXPECT().
					SoftDeleteBGM(gomock.Any(), &pg.SoftDeleteBGMParams{
						ID:            int64(102),
						CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
					}).
					Return(nil, pgx.ErrNoRows)
			},
			request: &types.DeleteBGMReq{
				ID: 102,
			},
			expectedCode:   values.ErrorCodeForbidden,
			expectedErrMsg: "you are not the creator of this bgm",
		},
		{
			name: "database error",
			setupMocks: func(mockDB *pgmock.MockQuerier) {
				// 模拟数据库错误
				mockDB.EXPECT().
					SoftDeleteBGM(gomock.Any(), &pg.SoftDeleteBGMParams{
						ID:            int64(103),
						CreatorUserId: pgtype.Int8{Int64: 123, Valid: true},
					}).
					Return(nil, xerrors.New(values.ErrorCodeInternalDBError, "database error"))
			},
			request: &types.DeleteBGMReq{
				ID: 103,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "failed to delete bgm",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB := setupDeleteBGMTest(t)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB)
			}

			// 执行测试
			resp, err := logic.DeleteBGM(tt.request)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)
			}
		})
	}
}
