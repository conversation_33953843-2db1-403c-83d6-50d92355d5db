package bgm

import (
	"context"
	"time"

	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/mr"
)

type coverter struct {
	svcCtx *svc.ServiceContext
}

func newCoverter(svcCtx *svc.ServiceContext) *coverter {
	return &coverter{
		svcCtx: svcCtx,
	}
}

func (c *coverter) ToBGMCategories(_ context.Context, categories []*pg.AiuBgmCategory) []*types.BGMCategory {
	result := make([]*types.BGMCategory, 0, len(categories))
	for _, category := range categories {
		result = append(result, &types.BGMCategory{
			ID:       int(category.ID),
			Name:     category.Name,
			CoverURL: category.CoverUrl.String,
		})
	}

	return result
}

func (c *coverter) ToBGMBaseInfo(ctx context.Context, bgm *pg.AiuBgmTable) (*types.BGMBaseInfo, error) {
	// 并发获取，其中任一失败，则返回错误
	var coverURL string
	var creator *dynamo.User
	var categories []*types.BGMCategory
	var likedInfoMap map[int32]time.Time
	err := mr.Finish(
		func() error {
			var err error
			coverURL, err = c.getCoverURL(ctx, bgm)
			return err
		},
		func() error {
			var err error
			creator, err = c.getCreator(ctx, bgm)
			return err
		},
		func() error {
			var err error
			categories, err = c.getCategories(ctx, bgm)
			return err
		},
		func() error {
			var err error
			likedInfoMap, err = c.getBatchLikedInfo(ctx, []*pg.AiuBgmTable{bgm})
			return err
		},
	)
	if err != nil {
		return nil, err
	}

	return &types.BGMBaseInfo{
		ID:              int(bgm.ID),
		Name:            bgm.Name.String,
		URL:             bgm.Url.String,
		CoverURL:        coverURL,
		Duration:        int(bgm.Duration.Int32),
		Tags:            bgm.Tags,
		ReferenceCount:  int(bgm.ReferenceCount.Int32),
		CreatorUserID:   creator.ID,
		CreatorUserName: creator.UserName,
		Public:          bgm.Public,
		NSFW:            bgm.Nsfw,
		Liked:           !likedInfoMap[int32(bgm.ID)].IsZero(),
		Categories:      categories,
	}, nil
}

func (c *coverter) getCoverURL(ctx context.Context, bgm *pg.AiuBgmTable) (string, error) {
	coverURL, err := c.svcCtx.S3.GetPresignedURL(ctx, bgm.CoverUrl.String, s3.Original)
	if err != nil {
		logc.Errorf(ctx, "failed to get presigned URL: %v", err)
		return "", err
	}
	return coverURL, nil
}

func (c *coverter) getCreator(ctx context.Context, bgm *pg.AiuBgmTable) (*dynamo.User, error) {
	if !bgm.CreatorUserId.Valid || bgm.CreatorUserId.Int64 == 0 {
		return &dynamo.User{
			ID:       int(bgm.CreatorUserId.Int64),
			UserName: "unknown",
		}, nil
	}
	creator, err := c.svcCtx.Dynamo.GetUser(ctx, int(bgm.CreatorUserId.Int64))
	if err != nil {
		logc.Errorf(ctx, "failed to get user: %v, userID: %d", err, bgm.CreatorUserId.Int64)
		return nil, err
	}
	return creator, nil
}

func (c *coverter) getCategories(ctx context.Context, bgm *pg.AiuBgmTable) ([]*types.BGMCategory, error) {
	bgmCategories, err := c.svcCtx.DB.GetBGMCategoriesByIDs(ctx, bgm.Categories)
	if err != nil {
		logc.Errorf(ctx, "failed to get BGM categories: %v", err)
		return nil, err
	}
	return c.ToBGMCategories(ctx, bgmCategories), nil
}

func (c *coverter) BatchToBGMBaseInfo(ctx context.Context, bgms []*pg.AiuBgmTable) ([]*types.BGMBaseInfo, error) {
	if len(bgms) == 0 {
		return nil, nil
	}

	data, err := c.getBatchData(ctx, bgms)
	if err != nil {
		return nil, err
	}

	return c.buildBatchBGMBaseInfo(ctx, bgms, data), nil
}

type batchData struct {
	coverURLMap  map[string]string
	userMap      map[int]*dynamo.User
	categoryMap  map[int32]*pg.AiuBgmCategory
	likedInfoMap map[int32]time.Time
}

func (c *coverter) getBatchData(ctx context.Context, bgms []*pg.AiuBgmTable) (*batchData, error) {
	data := &batchData{}
	err := mr.Finish(
		func() error {
			var err error
			data.coverURLMap, err = c.getBatchCoverURLs(ctx, bgms)
			return err
		},
		func() error {
			var err error
			data.userMap, err = c.getBatchUsers(ctx, bgms)
			return err
		},
		func() error {
			var err error
			data.categoryMap, err = c.getBatchCategories(ctx, bgms)
			return err
		},
		func() error {
			var err error
			data.likedInfoMap, err = c.getBatchLikedInfo(ctx, bgms)
			return err
		},
	)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (c *coverter) getBatchCoverURLs(ctx context.Context, bgms []*pg.AiuBgmTable) (map[string]string, error) {
	coverURLs := lo.Map(bgms, func(bgm *pg.AiuBgmTable, _ int) string {
		return bgm.CoverUrl.String
	})
	coverURLMap, err := c.svcCtx.S3.BatchGetPresignedURL(ctx, coverURLs, s3.Original)
	if err != nil {
		logc.Errorf(ctx, "failed to get presigned URLs: %v", err)
		return nil, err
	}
	return coverURLMap, nil
}

func (c *coverter) getBatchUsers(ctx context.Context, bgms []*pg.AiuBgmTable) (map[int]*dynamo.User, error) {
	bgmUserIDs := lo.Map(bgms, func(bgm *pg.AiuBgmTable, _ int) int {
		return int(bgm.CreatorUserId.Int64)
	})
	bgmUserIDs = lo.Uniq(bgmUserIDs)
	logc.Debugf(ctx, "bgmUserIDs: %v", bgmUserIDs)
	userMap, err := c.svcCtx.Dynamo.BatchGetUsers(ctx, bgmUserIDs)
	if err != nil {
		logc.Errorf(ctx, "failed to get users: %v", err)
		return nil, err
	}
	return userMap, nil
}

func (c *coverter) getBatchCategories(ctx context.Context,
	bgms []*pg.AiuBgmTable) (map[int32]*pg.AiuBgmCategory, error) {
	var categoryIDs []int32
	for _, bgm := range bgms {
		categoryIDs = append(categoryIDs, bgm.Categories...)
	}
	categoryIDs = lo.Uniq(categoryIDs)
	categories, err := c.svcCtx.DB.GetBGMCategoriesByIDs(ctx, categoryIDs)
	if err != nil {
		logc.Errorf(ctx, "failed to get BGM categories: %v", err)
		return nil, err
	}
	return lo.SliceToMap(categories, func(category *pg.AiuBgmCategory) (int32, *pg.AiuBgmCategory) {
		return int32(category.ID), category
	}), nil
}

func (c *coverter) getBatchLikedInfo(ctx context.Context, bgms []*pg.AiuBgmTable) (map[int32]time.Time, error) {
	bgmIDs := lo.Map(bgms, func(bgm *pg.AiuBgmTable, _ int) int32 {
		return int32(bgm.ID)
	})
	userID := servermsg.GetCurrentUser(ctx).UserID
	likedInfos, err := c.svcCtx.DB.CheckBGMsLikedByUser(ctx, &pg.CheckBGMsLikedByUserParams{
		UserID: int32(userID),
		BgmIDs: bgmIDs,
	})
	if err != nil {
		logc.Infof(ctx, "failed to check BGMs liked by user: %v", err)
		return nil, nil
	}
	return lo.SliceToMap(likedInfos, func(likedInfo *pg.CheckBGMsLikedByUserRow) (int32, time.Time) {
		return int32(likedInfo.BgmID), likedInfo.CreatedAt.Time
	}), nil
}

func (c *coverter) buildBatchBGMBaseInfo(ctx context.Context,
	bgms []*pg.AiuBgmTable, data *batchData) []*types.BGMBaseInfo {
	result := make([]*types.BGMBaseInfo, 0, len(bgms))
	for _, bgm := range bgms {
		categories := lo.FilterMap(bgm.Categories, func(categoryID int32, _ int) (*pg.AiuBgmCategory, bool) {
			category, ok := data.categoryMap[categoryID]
			if !ok {
				return nil, false
			}
			return category, true
		})
		result = append(result, &types.BGMBaseInfo{
			ID:              int(bgm.ID),
			Name:            bgm.Name.String,
			URL:             bgm.Url.String,
			CoverURL:        data.coverURLMap[bgm.CoverUrl.String],
			Duration:        int(bgm.Duration.Int32),
			Tags:            bgm.Tags,
			ReferenceCount:  int(bgm.ReferenceCount.Int32),
			CreatorUserID:   utils.GetWithDefault(data.userMap[int(bgm.CreatorUserId.Int64)], &dynamo.User{}).ID,
			CreatorUserName: utils.GetWithDefault(data.userMap[int(bgm.CreatorUserId.Int64)], &dynamo.User{}).UserName,
			Public:          bgm.Public,
			NSFW:            bgm.Nsfw,
			Liked:           !data.likedInfoMap[int32(bgm.ID)].IsZero(),
			Categories:      c.ToBGMCategories(ctx, categories),
		})
	}
	return result
}
