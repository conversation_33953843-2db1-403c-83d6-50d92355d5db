package bgm

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type GetBGMCategoryLogic struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *coverter
}

func NewGetBGMCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBGMCategoryLogic {
	return &GetBGMCategoryLogic{
		Logger:   logx.WithContext(ctx),
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: new<PERSON>overter(svcCtx),
	}
}

func (l *GetBGMCategoryLogic) GetBGMCategory(req *types.GetBGMCategoryReq) (*types.GetBGMCategoryResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	total, err := l.svcCtx.DB.GetBGMCategoriesCount(l.ctx)
	if err != nil {
		logc.Errorf(l.ctx, "failed to get BGM categories count: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get BGM categories count")
	}

	bgmCategories, err := l.svcCtx.DB.GetBGMCategories(l.ctx, &pg.GetBGMCategoriesParams{
		Page:     int32(req.Page),
		PageSize: int32(req.Size),
	})
	if err != nil {
		logc.Errorf(l.ctx, "failed to get BGM categories: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to get BGM categories")
	}

	categories := l.coverter.ToBGMCategories(l.ctx, bgmCategories)

	return &types.GetBGMCategoryResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.BGMCategoryPagination{
			Pagination: utils.GetPageInfo(int(total), req.Page, req.Size),
			Items:      categories,
		},
	}, nil
}
