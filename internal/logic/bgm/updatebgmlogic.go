package bgm

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type UpdateBGMLogic struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *coverter
}

func NewUpdateBGMLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBGMLogic {
	return &UpdateBGMLogic{
		Logger:   logx.WithContext(ctx),
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: newCoverter(svcCtx),
	}
}

func (l *UpdateBGMLogic) UpdateBGM(req *types.UpdateBGMReq) (*types.UpdateBGMResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)

	user := servermsg.GetCurrentUser(l.ctx)

	bgm, err := l.svcCtx.DB.UpdateBGM(l.ctx, &pg.UpdateBGMParams{
		ID:            int64(req.ID),
		Name:          utils.ToPGText(req.Name),
		URL:           utils.ToPGText(req.URL),
		CoverURL:      utils.ToPGText(req.CoverURL),
		Duration:      utils.ToPGInt4(req.Duration),
		Tags:          req.Tags,
		Public:        utils.ToPGBool(req.Public),
		Categories:    req.Categories,
		PremiseIDs:    req.PremiseIDs,
		CreateTimeUTC: pgtype.Int8{Int64: int64(time.Now().Unix()), Valid: true},
		CreatorUserID: pgtype.Int8{Int64: int64(user.UserID), Valid: true},
	})
	if err == pgx.ErrNoRows {
		return nil, xerrors.New(values.ErrorCodeNotFound, "bgm not found")
	}
	if err != nil {
		logc.Errorf(l.ctx, "failed to update bgm: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "failed to update bgm")
	}

	bgmInfo, err := l.coverter.ToBGMBaseInfo(l.ctx, bgm)
	if err != nil {
		logc.Errorf(l.ctx, "failed to convert bgm to bgm info: %v", err)
		return nil, xerrors.New(values.ErrorCodeInternalServerError, "failed to convert bgm to bgm info")
	}

	return &types.UpdateBGMResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: *bgmInfo,
	}, nil
}
