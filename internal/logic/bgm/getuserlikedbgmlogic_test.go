package bgm

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/sekai-app/sekai-go/internal/crates/s3"
	"github.com/sekai-app/sekai-go/internal/crates/s3/s3mock"
	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/db/dynamo"
	"github.com/sekai-app/sekai-go/internal/db/dynamo/dynamomock"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/db/pg/pgmock"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"
	"github.com/stretchr/testify/assert"
	xerrors "github.com/zeromicro/x/errors"
	"go.uber.org/mock/gomock"
)

func setupGetUserLikedBGMTest(t *testing.T, userID int) (*gomock.Controller, *GetUserLikedBGMLogic, *pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3) {
	ctrl := gomock.NewController(t)

	mockDB := pgmock.NewMockQuerier(ctrl)
	mockDynamo := dynamomock.NewMockDB(ctrl)
	mockS3 := s3mock.NewMockS3(ctrl)

	svcCtx := &svc.ServiceContext{
		DB:     mockDB,
		Dynamo: mockDynamo,
		S3:     mockS3,
	}

	// 创建一个带有 ServerMsg 的上下文
	serverMsg := &servermsg.ServerMsg{
		CurrentUser: servermsg.CurrentUser{
			UserID:   userID,
			UserName: "testuser",
		},
	}
	ctx := context.WithValue(context.Background(), servermsg.ServerMsgContextKey, serverMsg)

	logic := NewGetUserLikedBGMLogic(ctx, svcCtx)

	return ctrl, logic, mockDB, mockDynamo, mockS3
}

func TestGetUserLikedBGM(t *testing.T) {
	currentUserID := 123

	tests := []struct {
		name              string
		setupMocks        func(*pgmock.MockQuerier, *dynamomock.MockDB, *s3mock.MockS3)
		input             *types.GetUserLikedBGMReq
		expectedCode      int
		expectedErrMsg    string
		expectedItemCount int
		expectedTotal     int
	}{
		{
			name: "get current user liked bgms",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				// 设置 DB.GetUserLikedBGMsCount 的期望
				mockDB.EXPECT().
					GetUserLikedBGMsCount(gomock.Any(), &pg.GetUserLikedBGMsCountParams{
						UserID:         int32(currentUserID),
						FilterByPublic: false, // 查询自己的不需要过滤
					}).
					Return(int64(2), nil)

				// 设置 DB.GetUserLikedBGMs 的期望
				mockDB.EXPECT().
					GetUserLikedBGMs(gomock.Any(), &pg.GetUserLikedBGMsParams{
						UserID:         int32(currentUserID),
						FilterByPublic: false,
						Page:           1,
						PageSize:       10,
					}).
					Return([]*pg.AiuBgmTable{
						{
							ID:             1,
							Name:           pgtype.Text{String: "BGM 1", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm1.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 180, Valid: true},
							Tags:           []string{"tag1", "tag2"},
							ReferenceCount: pgtype.Int4{Int32: 5, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
							Public:         true,
							Nsfw:           false,
							Categories:     []int32{1},
						},
						{
							ID:             2,
							Name:           pgtype.Text{String: "BGM 2", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm2.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover2.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 200, Valid: true},
							Tags:           []string{"tag3", "tag4"},
							ReferenceCount: pgtype.Int4{Int32: 10, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 456, Valid: true},
							Public:         true,
							Nsfw:           false,
							Categories:     []int32{2},
						},
					}, nil)

				// 设置 BatchGetPresignedURL 的期望
				coverURLs := []string{
					"http://example.com/cover1.jpg",
					"http://example.com/cover2.jpg",
				}
				coverURLMap := map[string]string{
					"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
					"http://example.com/cover2.jpg": "https://presigned.example.com/cover2.jpg",
				}
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), coverURLs, s3.Original).
					Return(coverURLMap, nil)

				// 设置 BatchGetUsers 的期望
				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), []int{123, 456}).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "creator1"},
						456: {ID: 456, UserName: "creator2"},
					}, nil)

				// 设置 GetBGMCategoriesByIDs 的期望
				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), []int32{1, 2}).
					Return([]*pg.AiuBgmCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
						{ID: 2, Name: "Category 2", CoverUrl: pgtype.Text{String: "http://example.com/cat2.jpg", Valid: true}},
					}, nil)

				// 设置 CheckBGMsLikedByUser 的期望
				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{
						{BgmID: 1, CreatedAt: pgtype.Timestamptz{Valid: true}},
						{BgmID: 2, CreatedAt: pgtype.Timestamptz{Valid: true}},
					}, nil)
			},
			input: &types.GetUserLikedBGMReq{
				Page: 1,
				Size: 10,
				// UserID 不设置，使用当前用户
			},
			expectedCode:      values.ErrorCodeSuccess,
			expectedItemCount: 2,
			expectedTotal:     2,
		},
		{
			name: "get other user liked bgms with filter",
			setupMocks: func(mockDB *pgmock.MockQuerier, mockDynamo *dynamomock.MockDB, mockS3 *s3mock.MockS3) {
				otherUserID := 456

				// 设置 DB.GetUserLikedBGMsCount 的期望
				mockDB.EXPECT().
					GetUserLikedBGMsCount(gomock.Any(), &pg.GetUserLikedBGMsCountParams{
						UserID:         int32(otherUserID),
						FilterByPublic: true, // 查询他人的需要过滤
					}).
					Return(int64(1), nil)

				// 设置 DB.GetUserLikedBGMs 的期望
				mockDB.EXPECT().
					GetUserLikedBGMs(gomock.Any(), &pg.GetUserLikedBGMsParams{
						UserID:         int32(otherUserID),
						FilterByPublic: true,
						Page:           1,
						PageSize:       10,
					}).
					Return([]*pg.AiuBgmTable{
						{
							ID:             1,
							Name:           pgtype.Text{String: "Public BGM", Valid: true},
							Url:            pgtype.Text{String: "http://example.com/bgm1.mp3", Valid: true},
							CoverUrl:       pgtype.Text{String: "http://example.com/cover1.jpg", Valid: true},
							Duration:       pgtype.Int4{Int32: 180, Valid: true},
							Tags:           []string{"tag1", "tag2"},
							ReferenceCount: pgtype.Int4{Int32: 5, Valid: true},
							CreatorUserId:  pgtype.Int8{Int64: 123, Valid: true},
							Public:         true,
							Nsfw:           false,
							Categories:     []int32{1},
						},
					}, nil)

				// 设置剩下的期望...
				mockS3.EXPECT().
					BatchGetPresignedURL(gomock.Any(), gomock.Any(), s3.Original).
					Return(map[string]string{
						"http://example.com/cover1.jpg": "https://presigned.example.com/cover1.jpg",
					}, nil)

				mockDynamo.EXPECT().
					BatchGetUsers(gomock.Any(), gomock.Any()).
					Return(map[int]*dynamo.User{
						123: {ID: 123, UserName: "creator1"},
					}, nil)

				mockDB.EXPECT().
					GetBGMCategoriesByIDs(gomock.Any(), gomock.Any()).
					Return([]*pg.AiuBgmCategory{
						{ID: 1, Name: "Category 1", CoverUrl: pgtype.Text{String: "http://example.com/cat1.jpg", Valid: true}},
					}, nil)

				mockDB.EXPECT().
					CheckBGMsLikedByUser(gomock.Any(), gomock.Any()).
					Return([]*pg.CheckBGMsLikedByUserRow{
						{BgmID: 1, CreatedAt: pgtype.Timestamptz{Valid: true}},
					}, nil)
			},
			input: &types.GetUserLikedBGMReq{
				UserID: 456, // 查询其他用户
				Page:   1,
				Size:   10,
			},
			expectedCode:      values.ErrorCodeSuccess,
			expectedItemCount: 1,
			expectedTotal:     1,
		},
		{
			name: "database error on count",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetUserLikedBGMsCount 返回错误
				mockDB.EXPECT().
					GetUserLikedBGMsCount(gomock.Any(), gomock.Any()).
					Return(int64(0), xerrors.New(500, "database error"))
			},
			input: &types.GetUserLikedBGMReq{
				Page: 1,
				Size: 10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "internal error",
		},
		{
			name: "database error on get bgms",
			setupMocks: func(mockDB *pgmock.MockQuerier, _ *dynamomock.MockDB, _ *s3mock.MockS3) {
				// 设置 DB.GetUserLikedBGMsCount 成功
				mockDB.EXPECT().
					GetUserLikedBGMsCount(gomock.Any(), gomock.Any()).
					Return(int64(10), nil)

				// 设置 DB.GetUserLikedBGMs 返回错误
				mockDB.EXPECT().
					GetUserLikedBGMs(gomock.Any(), gomock.Any()).
					Return(nil, xerrors.New(500, "database error"))
			},
			input: &types.GetUserLikedBGMReq{
				Page: 1,
				Size: 10,
			},
			expectedCode:   values.ErrorCodeInternalDBError,
			expectedErrMsg: "internal error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟对象和测试环境
			ctrl, logic, mockDB, mockDynamo, mockS3 := setupGetUserLikedBGMTest(t, currentUserID)
			defer ctrl.Finish()

			// 配置模拟对象的行为
			if tt.setupMocks != nil {
				tt.setupMocks(mockDB, mockDynamo, mockS3)
			}

			// 执行测试
			resp, err := logic.GetUserLikedBGM(tt.input)

			// 验证测试结果
			if tt.expectedErrMsg != "" {
				// 如果期望有错误，验证错误信息
				assert.Error(t, err)
				xError, ok := err.(*xerrors.CodeMsg)
				assert.True(t, ok, "Error should be of type *xerrors.CodeMsg")
				assert.Equal(t, tt.expectedCode, xError.Code)
				assert.Equal(t, tt.expectedErrMsg, xError.Msg)
				assert.Nil(t, resp)
			} else {
				// 如果期望成功，验证返回值
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedCode, resp.Code)
				assert.Equal(t, "success", resp.Msg)

				// 验证分页信息
				assert.Equal(t, tt.expectedTotal, resp.Data.Total)
				assert.Equal(t, tt.input.Page, resp.Data.Page)
				assert.Equal(t, tt.input.Size, resp.Data.Size)

				// 验证返回的BGM列表长度
				assert.Equal(t, tt.expectedItemCount, len(resp.Data.Items))
			}
		})
	}
}
