package bgm

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetCreatedByUserLogic struct {
	logx.Logger
	ctx      context.Context
	svcCtx   *svc.ServiceContext
	coverter *coverter
}

func NewGetCreatedByUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCreatedByUserLogic {
	return &GetCreatedByUserLogic{
		Logger:   logx.WithContext(ctx),
		ctx:      ctx,
		svcCtx:   svcCtx,
		coverter: new<PERSON>over<PERSON>(svcCtx),
	}
}

func (l *GetCreatedByUserLogic) GetCreatedByUser(req *types.GetCreatedByUserReq) (*types.GetCreatedByUserResp, error) {
	logc.Infof(l.ctx, "req: %+v", req)
	currentUser := servermsg.GetCurrentUser(l.ctx)
	queryUserID := req.UserID
	count, err := l.svcCtx.DB.GetBGMCreatedByUserCount(l.ctx, &pg.GetBGMCreatedByUserCountParams{
		QueryUserId:    int32(queryUserID),
		FilterByPublic: currentUser.UserID != queryUserID,
	})
	if err != nil {
		return nil, err
	}
	bgms, err := l.svcCtx.DB.GetBGMCreatedByUser(l.ctx, &pg.GetBGMCreatedByUserParams{
		QueryUserId:    int32(queryUserID),
		FilterByPublic: currentUser.UserID != queryUserID,
		PageSize:       int32(req.Size),
		Page:           int32(req.Page),
	})
	if err != nil {
		return nil, err
	}
	bgmBaseInfos, err := l.coverter.BatchToBGMBaseInfo(l.ctx, bgms)
	if err != nil {
		return nil, err
	}

	return &types.GetCreatedByUserResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: types.BGMPagination{
			Pagination: utils.GetPageInfo(int(count), req.Page, req.Size),
			Items:      bgmBaseInfos,
		},
	}, nil
}
