package common

import (
	"context"

	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetHealthLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetHealthLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetHealthLogic {
	return &GetHealthLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetHealthLogic) GetHealth(_ *types.HealthReq) (*types.HealthRsp, error) {
	return &types.HealthRsp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
	}, nil
}
