// Code generated by MockGen. DO NOT EDIT.
// Source: redis.go
//
// Generated by this command:
//
//	mockgen -source=redis.go -destination=redismock/redis.go -package=redismock
//

// Package redismock is a generated GoMock package.
package redismock

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockRedis is a mock of Redis interface.
type MockRedis struct {
	ctrl     *gomock.Controller
	recorder *MockRedisMockRecorder
	isgomock struct{}
}

// MockRedisMockRecorder is the mock recorder for MockRedis.
type MockRedisMockRecorder struct {
	mock *MockRedis
}

// NewMockRedis creates a new mock instance.
func NewMockRedis(ctrl *gomock.Controller) *MockRedis {
	mock := &MockRedis{ctrl: ctrl}
	mock.recorder = &MockRedisMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRedis) EXPECT() *MockRedisMockRecorder {
	return m.recorder
}

// GetHotSekaiIDs mocks base method.
func (m *MockRedis) GetHotSekaiIDs(ctx context.Context, appVersion string) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHotSekaiIDs", ctx, appVersion)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHotSekaiIDs indicates an expected call of GetHotSekaiIDs.
func (mr *MockRedisMockRecorder) GetHotSekaiIDs(ctx, appVersion any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHotSekaiIDs", reflect.TypeOf((*MockRedis)(nil).GetHotSekaiIDs), ctx, appVersion)
}
