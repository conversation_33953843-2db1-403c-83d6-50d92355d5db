// Package redis 封装了业务上对 redis 的访问
package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/hashicorp/go-version"
	"github.com/samber/lo"
	"github.com/sekai-app/sekai-go/internal/crates/utils"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/stores/redis"
)

// Redis 封装了业务上的redis操作
//
//go:generate mockgen -source=redis.go -destination=redismock/redis.go -package=redismock
type Redis interface {
	// GetHotSekaiIDs 获取热门sekai的ID列表
	// Deprecated: 无用待移除代码，这里仅作为 redis 使用示例
	GetHotSekaiIDs(ctx context.Context, appVersion string) ([]int64, error)
}

// NewRedis creates a new Redis instance
func NewRedis(redis *redis.Redis) Redis {
	return &redisImpl{redis: redis}
}

type redisImpl struct {
	redis *redis.Redis
}

type sekaiHotList struct {
	ID         int64 `json:"id"`
	MinVersion int   `json:"min_ver"`
}

// GetHotSekaiIDs 从Redis获取热门Sekai的ID列表
// Deprecated: 无用待移除代码，这里仅作为 redis 使用示例
func (r *redisImpl) GetHotSekaiIDs(ctx context.Context, appVersion string) ([]int64, error) {
	// 从Redis获取热门Sekai排名
	value, err := r.redis.Get("sekai_hot_list")
	if err != nil {
		// 如果key不存在，返回空列表
		if err == redis.Nil {
			return []int64{}, nil
		}
		return nil, err
	}
	if value == "" {
		logc.Infof(ctx, "sekai_hot_list is empty")
		return []int64{}, nil
	}

	// 尝试解析为JSON数组
	var hotList []*sekaiHotList
	if err := json.Unmarshal([]byte(value), &hotList); err != nil {
		// 如果解析JSON失败，记录日志
		logc.Infof(ctx, "Failed to parse hot sekai IDs as JSON array: %v, value: %s", err, value)
		return nil, err
	}

	// 过滤掉appVersion小于等于当前版本的sekai
	ids := make([]int64, 0, len(hotList))
	for _, item := range hotList {
		// Convert MinVersion from integer (e.g., 1017000) to semver format (e.g., "1.17.0")
		// Each segment is 3 digits: major (1), minor (017), patch (000)
		minVersionStr := fmt.Sprintf("%d.%d.%d",
			item.MinVersion/1000000,        // Major version
			(item.MinVersion%1000000)/1000, // Minor version
			item.MinVersion%1000)           // Patch version
		v1, err := version.NewVersion(minVersionStr)
		if err != nil {
			logc.Infof(ctx, "Failed to parse min version: %v, value: %v, minVersionStr: %s", err, item.MinVersion, minVersionStr)
			continue
		}
		v2, err := version.NewVersion(appVersion)
		if err != nil {
			logc.Infof(ctx, "Failed to parse app version: %v, value: %s", err, appVersion)
			continue
		}
		if v1.LessThanOrEqual(v2) {
			ids = append(ids, item.ID)
		}
	}

	return ids, nil
}

// S3PresingCache 利用 redis 实现 s3.Cache 接口
type S3PresingCache struct {
	redis *redis.Redis
}

// NewS3PresingCache 创建一个 S3PresingCache 实例
func NewS3PresingCache(redis *redis.Redis) *S3PresingCache {
	return &S3PresingCache{redis: redis}
}

func (c *S3PresingCache) getCacheKey(key string, resolution string) string {
	return fmt.Sprintf("%s:s3_presigned:%s:%s", utils.CovertEnvName(), key, resolution)
}

// Get 获取预签名缓存
func (c *S3PresingCache) Get(ctx context.Context, resolution, key string) (string, error) {
	return c.redis.GetCtx(ctx, c.getCacheKey(key, resolution))
}

// MGet 获取多个预签名缓存
func (c *S3PresingCache) MGet(ctx context.Context, resolution string, keys ...string) (map[string]string, error) {
	cacheKeys := lo.Map(keys, func(item string, _ int) string {
		return c.getCacheKey(item, resolution)
	})
	values, err := c.redis.MgetCtx(ctx, cacheKeys...)
	if err != nil {
		return nil, err
	}

	z := lo.Zip2(keys, values)

	return lo.SliceToMap(z, func(item lo.Tuple2[string, string]) (string, string) {
		return item.A, item.B
	}), nil
}

// Set 设置预签名缓存
func (c *S3PresingCache) Set(ctx context.Context,
	resolution, key string, value string, expiration time.Duration) error {

	return c.redis.SetexCtx(ctx, c.getCacheKey(key, resolution), value, int(expiration.Seconds()))
}

// MSet 设置多个预签名缓存
func (c *S3PresingCache) MSet(ctx context.Context,
	resolution string, values map[string]string, expiration time.Duration) error {

	return c.redis.PipelinedCtx(ctx, func(p redis.Pipeliner) error {
		for k, v := range values {
			p.Set(ctx, c.getCacheKey(k, resolution), v, expiration)
		}
		return nil
	})
}
