package redis

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/stores/redis/redistest"
)

func TestS3PresingCache_GetCacheKey(t *testing.T) {
	// Set up environment variable for testing
	oldEnv := os.Getenv("AIU_ENV")
	defer os.Setenv("AIU_ENV", oldEnv)

	tests := []struct {
		name       string
		envValue   string
		key        string
		resolution string
		expected   string
	}{
		{
			name:       "production environment",
			envValue:   "production",
			key:        "test-key",
			resolution: "original",
			expected:   "prod:s3_presigned:test-key:original",
		},
		{
			name:       "stage environment",
			envValue:   "stage",
			key:        "test-key",
			resolution: "original",
			expected:   "prod:s3_presigned:test-key:original",
		},
		{
			name:       "development environment",
			envValue:   "development",
			key:        "test-key",
			resolution: "original",
			expected:   "stage:s3_presigned:test-key:original",
		},
		{
			name:       "empty environment",
			envValue:   "",
			key:        "test-key",
			resolution: "original",
			expected:   "stage:s3_presigned:test-key:original",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment for test
			os.Setenv("AIU_ENV", tt.envValue)

			// Create a redis client using redistest
			rds := redistest.CreateRedis(t)

			cache := NewS3PresingCache(rds)
			result := cache.getCacheKey(tt.key, tt.resolution)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestS3PresingCache_Get(t *testing.T) {
	// Create a redis client using redistest
	rds := redistest.CreateRedis(t)

	ctx := context.Background()
	cache := NewS3PresingCache(rds)

	// Set environment variable for testing
	oldEnv := os.Getenv("AIU_ENV")
	os.Setenv("AIU_ENV", "development")
	defer os.Setenv("AIU_ENV", oldEnv)

	// Test data
	resolution := "original"
	key := "test-key"
	value := "http://example.com/test.jpg"

	// Cache key
	cacheKey := cache.getCacheKey(key, resolution)

	// Set value in Redis
	err := rds.SetCtx(ctx, cacheKey, value)
	assert.NoError(t, err)

	// Test Get
	result, err := cache.Get(ctx, resolution, key)
	assert.NoError(t, err)
	assert.Equal(t, value, result)

	// Test Get with non-existent key
	nonExistentResult, err := cache.Get(ctx, resolution, "non-existent-key")
	assert.NoError(t, err)
	assert.Equal(t, "", nonExistentResult)
}

func TestS3PresingCache_MGet(t *testing.T) {
	// Create a redis client using redistest
	rds := redistest.CreateRedis(t)

	ctx := context.Background()
	cache := NewS3PresingCache(rds)

	// Set environment variable for testing
	oldEnv := os.Getenv("AIU_ENV")
	os.Setenv("AIU_ENV", "development")
	defer os.Setenv("AIU_ENV", oldEnv)

	// Test data
	resolution := "original"
	keys := []string{"key1", "key2", "key3"}
	values := map[string]string{
		"key1": "http://example.com/1.jpg",
		"key2": "http://example.com/2.jpg",
		"key3": "http://example.com/3.jpg",
	}

	// Set values in Redis
	for _, key := range keys {
		cacheKey := cache.getCacheKey(key, resolution)
		err := rds.SetCtx(ctx, cacheKey, values[key])
		assert.NoError(t, err)
	}

	// Test MGet
	result, err := cache.MGet(ctx, resolution, keys...)
	assert.NoError(t, err)
	assert.Equal(t, values, result)

	// Test MGet with mixed existing and non-existing keys
	mixedKeys := []string{"key1", "non-existent", "key3"}
	expectedMixedResult := map[string]string{
		"key1":         "http://example.com/1.jpg",
		"non-existent": "",
		"key3":         "http://example.com/3.jpg",
	}

	mixedResult, err := cache.MGet(ctx, resolution, mixedKeys...)
	assert.NoError(t, err)
	assert.Equal(t, expectedMixedResult, mixedResult)
}

func TestS3PresingCache_Set(t *testing.T) {
	// Create a redis client using redistest
	rds := redistest.CreateRedis(t)

	ctx := context.Background()
	cache := NewS3PresingCache(rds)

	// Set environment variable for testing
	oldEnv := os.Getenv("AIU_ENV")
	os.Setenv("AIU_ENV", "development")
	defer os.Setenv("AIU_ENV", oldEnv)

	// Test data
	resolution := "original"
	key := "test-key"
	value := "http://example.com/test.jpg"
	expiration := 30 * time.Second

	// Test Set
	err := cache.Set(ctx, resolution, key, value, expiration)
	assert.NoError(t, err)

	// Verify value was set correctly
	cacheKey := cache.getCacheKey(key, resolution)
	result, err := rds.GetCtx(ctx, cacheKey)
	assert.NoError(t, err)
	assert.Equal(t, value, result)

	// Test TTL (we can only check that a TTL was set, not its exact value in miniredis)
	ttl, err := rds.TtlCtx(ctx, cacheKey)
	assert.NoError(t, err)
	assert.True(t, ttl > 0, "TTL should be greater than 0")
}

func TestS3PresingCache_MSet(t *testing.T) {
	// Create a redis client using redistest
	rds := redistest.CreateRedis(t)

	ctx := context.Background()
	cache := NewS3PresingCache(rds)

	// Set environment variable for testing
	oldEnv := os.Getenv("AIU_ENV")
	os.Setenv("AIU_ENV", "development")
	defer os.Setenv("AIU_ENV", oldEnv)

	// Test data
	resolution := "original"
	values := map[string]string{
		"key1": "http://example.com/1.jpg",
		"key2": "http://example.com/2.jpg",
		"key3": "http://example.com/3.jpg",
	}
	expiration := 30 * time.Second

	// Test MSet
	err := cache.MSet(ctx, resolution, values, expiration)
	assert.NoError(t, err)

	// Verify values were set correctly
	for key, expectedValue := range values {
		cacheKey := cache.getCacheKey(key, resolution)

		// Check value
		result, err := rds.GetCtx(ctx, cacheKey)
		assert.NoError(t, err)
		assert.Equal(t, expectedValue, result)

		// Check TTL
		ttl, err := rds.TtlCtx(ctx, cacheKey)
		assert.NoError(t, err)
		assert.True(t, ttl > 0, "TTL should be greater than 0")
	}
}
