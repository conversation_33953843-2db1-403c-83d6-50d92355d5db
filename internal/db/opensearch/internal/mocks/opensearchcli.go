// Code generated by MockGen. DO NOT EDIT.
// Source: opensearchcli.go
//
// Generated by this command:
//
//	mockgen -source=opensearchcli.go -destination=internal/mocks/opensearchcli.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockopenSearchClientAPI is a mock of openSearchClientAPI interface.
type MockopenSearchClientAPI struct {
	ctrl     *gomock.Controller
	recorder *MockopenSearchClientAPIMockRecorder
	isgomock struct{}
}

// MockopenSearchClientAPIMockRecorder is the mock recorder for MockopenSearchClientAPI.
type MockopenSearchClientAPIMockRecorder struct {
	mock *MockopenSearchClientAPI
}

// NewMockopenSearchClientAPI creates a new mock instance.
func NewMockopenSearchClientAPI(ctrl *gomock.Controller) *MockopenSearchClientAPI {
	mock := &MockopenSearchClientAPI{ctrl: ctrl}
	mock.recorder = &MockopenSearchClientAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockopenSearchClientAPI) EXPECT() *MockopenSearchClientAPIMockRecorder {
	return m.recorder
}

// Search mocks base method.
func (m *MockopenSearchClientAPI) Search(ctx context.Context, index string, query map[string]any, result any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Search", ctx, index, query, result)
	ret0, _ := ret[0].(error)
	return ret0
}

// Search indicates an expected call of Search.
func (mr *MockopenSearchClientAPIMockRecorder) Search(ctx, index, query, result any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Search", reflect.TypeOf((*MockopenSearchClientAPI)(nil).Search), ctx, index, query, result)
}
