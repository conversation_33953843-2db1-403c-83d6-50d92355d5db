// Package opensearch 提供OpenSearch的封装
package opensearch

import (
	"context"
	"strconv"
	"strings"
)

// KeywordEntity 表示一个关键字实体
type KeywordEntity struct {
	KeywordName string `json:"keywordName"`
	KeywordType string `json:"keywordType"`
	UseCount    int    `json:"useCount"`
}

// OpenSearch 是OpenSearch的封装
//
//go:generate mockgen -source=opensearch.go -destination=opensearchmock/mock_opensearch.go -package=opensearchmock
type OpenSearch interface {
	// SearchBGMByName 搜索BGM
	SearchBGMByName(ctx context.Context, userID int, name string, page int, size int) ([]SearchHit[BGM], int, error)
	// SearchSekaiKeywords 搜索sekai关键词
	SearchSekaiKeywords(ctx context.Context, prefix string, size int) ([]string, int, error)
	// SearchSekaiByKeyword 根据关键词搜索sekai
	SearchSekaiByKeyword(ctx context.Context, req SearchSekaiReq) (*SearchResult[Sekai], error)
	// SearchStoryByKeyword 根据关键词搜索story
	SearchStoryByKeyword(ctx context.Context, req SearchStoryReq) (*SearchResult[Story], error)
	// SearchVoiceByKeyword 根据关键词搜索voice
	SearchVoiceByKeyword(ctx context.Context, req SearchVoiceReq) (*SearchResult[Voice], error)
}

// Config OpenSearch的配置
type Config struct {
	Addr     string
	User     string
	Password string
}

// SearchSekaiReq 搜索sekai的请求
type SearchSekaiReq struct {
	Keyword    string
	AppVersion string
	UserID     int
	Page       int
	Size       int
}

// SearchStoryReq 搜索story的请求
type SearchStoryReq struct {
	Keyword string
	UserID  int
	Page    int
	Size    int
}

// SearchVoiceReq 搜索voice的请求
type SearchVoiceReq struct {
	Keyword string
	UserID  int
	Page    int
	Size    int
}

// SearchHit represents a single search result with score
type SearchHit[T any] struct {
	Score  float64 `json:"_score"`
	Source T       `json:"_source"`
}

// SearchResult represents the response from OpenSearch
type SearchResult[T any] struct {
	Hits struct {
		Total struct {
			Value int `json:"value"`
		} `json:"total"`
		MaxScore float64        `json:"max_score"`
		Hits     []SearchHit[T] `json:"hits"`
	} `json:"hits"`
}

type opensearchImpl struct {
	cli openSearchClientAPI
}

// NewOpenSearch 创建一个OpenSearch实例
func NewOpenSearch(cfg Config) (OpenSearch, error) {
	cli, err := newOpenSearchClient(cfg)
	if err != nil {
		return nil, err
	}

	return &opensearchImpl{cli: cli}, nil
}

func getQueryNameCondition(nameField string, nameValue string) []map[string]any {
	return []map[string]any{
		{
			"match": map[string]any{nameField + ".searchable": map[string]any{"query": nameValue, "boost": 0.5}},
		},
		{
			"match_phrase": map[string]any{
				nameField + ".searchable": map[string]any{"query": nameValue, "boost": 0.8},
			},
		},
		{
			"match_phrase_prefix": map[string]any{
				nameField + ".searchable": map[string]any{"query": nameValue, "boost": 0.3},
			},
		},
		{
			"term": map[string]any{nameField + ".searchable": map[string]any{"value": nameValue, "boost": 1.0}},
		},
		{
			"fuzzy": map[string]any{
				nameField + ".searchable": map[string]any{"value": nameValue, "fuzziness": 1, "boost": 0.1},
			},
		},
	}
}

// SearchSekaiKeywords 根据前缀搜索关键词
func (o *opensearchImpl) SearchSekaiKeywords(ctx context.Context, prefix string, size int) ([]string, int, error) {
	// 如果前缀为空，返回空结果
	if prefix == "" {
		return []string{}, 0, nil
	}

	// 创建搜索查询
	query := map[string]any{
		"_source": []string{"keywordName", "keywordType", "useCount"},
		"size":    size,
		"query": map[string]any{
			"bool": map[string]any{
				"should": []map[string]any{
					{
						// 精确前缀匹配，最高权重
						"match_phrase_prefix": map[string]any{
							"keywordName": map[string]any{
								"query": prefix,
								"boost": 3.0,
							},
						},
					},
				},
			},
		},
		// 基于使用次数和相关性分数的自定义排序
		"sort": []map[string]any{
			{
				"_score": map[string]any{
					"order": "desc",
				},
			},
			{
				"useCount": map[string]any{
					"order": "desc",
				},
			},
		},
	}

	// 定义响应结构
	type KeywordSearchResponse struct {
		Hits struct {
			Total struct {
				Value int `json:"value"`
			} `json:"total"`
			Hits []struct {
				Score  float64       `json:"_score"`
				Source KeywordEntity `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	}

	var response KeywordSearchResponse
	err := o.cli.Search(ctx, "character-tags-index", query, &response)
	if err != nil {
		return nil, 0, err
	}

	// 提取关键词
	keywords := make([]string, 0, len(response.Hits.Hits))
	for _, hit := range response.Hits.Hits {
		keywords = append(keywords, hit.Source.KeywordName)
	}

	return keywords, response.Hits.Total.Value, nil
}

func (o *opensearchImpl) SearchBGMByName(ctx context.Context,
	userID int, name string, page int, size int) ([]SearchHit[BGM], int, error) {
	query := map[string]any{
		"_source": []string{"id", "name"},
		"from":    (page - 1) * size,
		"size":    size,
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"term": map[string]any{
							"enable": map[string]any{"value": true},
						},
					},
					{
						"term": map[string]any{
							"nsfw": map[string]any{"value": false},
						},
					},
					{
						"bool": map[string]any{
							"should": []map[string]any{
								{
									"term": map[string]any{
										"creatorUserId": map[string]any{"value": userID},
									},
								},
								{
									"term": map[string]any{
										"public": map[string]any{"value": true},
									},
								},
							},
						},
					},
					{
						"bool": map[string]any{
							"should": getQueryNameCondition("name", name),
						},
					},
				},
			},
		},
	}

	var response SearchResult[BGM]
	err := o.cli.Search(ctx, "aiu-bgm-table", query, &response)
	if err != nil {
		return nil, 0, err
	}

	return response.Hits.Hits, response.Hits.Total.Value, nil
}

// SearchSekaiByKeyword 根据关键词搜索sekai
func (o *opensearchImpl) SearchSekaiByKeyword(ctx context.Context, req SearchSekaiReq) (*SearchResult[Sekai], error) {

	// 将appVersion转为数字，如：1.17.0 转为 1017000
	appVersionInt := 0
	if req.AppVersion != "" {
		parts := strings.Split(req.AppVersion, ".")
		// 处理最多3段版本号
		for i := 0; i < len(parts) && i < 3; i++ {
			num, err := strconv.Atoi(parts[i])
			if err != nil {
				continue
			}
			// 每段版本号保留3位数字
			appVersionInt = appVersionInt*1000 + num
		}
	}

	query := map[string]any{
		"from":  (req.Page - 1) * req.Size,
		"size":  req.Size,
		"query": getSekaiQuery(req.Keyword, appVersionInt, req.UserID),
	}

	var response SearchResult[Sekai]
	err := o.cli.Search(ctx, (&Sekai{}).Index(), query, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// getSekaiQuery 获取sekai的搜索查询
// nolint:funlen
func getSekaiQuery(keyword string, appVersion int, userID int) map[string]any {
	return map[string]any{
		"function_score": map[string]any{
			"query": map[string]any{
				"bool": map[string]any{
					"must": []map[string]any{
						// 作品是发布的
						{
							"term": map[string]any{
								"published": map[string]any{"value": true},
							},
						},
						// 用户搜索自己的作品, 或者作品是公开的
						{
							"bool": map[string]any{
								"should": []map[string]any{
									{"term": map[string]any{"creator_user_id": map[string]any{"value": userID}}},
									{"term": map[string]any{"is_public": map[string]any{"value": true}}},
								},
							},
						},
						// 用户搜索自己的作品, 或者作品不是NSFW的
						{
							"bool": map[string]any{
								"should": []map[string]any{
									{"term": map[string]any{"creator_user_id": map[string]any{"value": userID}}},
									{"term": map[string]any{"nsfw": map[string]any{"value": false}}},
								},
							},
						},
						// 作品的最低版本
						{
							"range": map[string]any{
								"min_app_version": map[string]any{
									"lte": appVersion,
								},
							},
						},
					},
					"should": []map[string]any{
						// 标题
						{
							"match_phrase": map[string]any{
								"title": map[string]any{
									"query": keyword,
									"boost": 10 * 3,
								},
							},
						},
						{
							"match": map[string]any{
								"title": map[string]any{
									"query": keyword,
									"boost": 8 * 2,
								},
							},
						},
						{
							"fuzzy": map[string]any{
								"title": map[string]any{
									"value":     keyword,
									"fuzziness": 1,
									"boost":     6 * 0.5,
								},
							},
						},
						// 角色名
						{
							"match_phrase": map[string]any{
								"charNames": map[string]any{
									"query": keyword,
									"boost": 9 * 3,
								},
							},
						},
						{
							"match": map[string]any{
								"charNames": map[string]any{
									"query": keyword,
									"boost": 7 * 2,
								},
							},
						},
						{
							"fuzzy": map[string]any{
								"charNames": map[string]any{
									"value":     keyword,
									"fuzziness": 1,
									"boost":     5 * 0.5,
								},
							},
						},
						// 分类名
						{
							"match_phrase": map[string]any{
								"category_names": map[string]any{
									"query": keyword,
									"boost": 8 * 3,
								},
							},
						},
						{
							"match": map[string]any{
								"category_names": map[string]any{
									"query": keyword,
									"boost": 6 * 2,
								},
							},
						},
						{
							"fuzzy": map[string]any{
								"category_names": map[string]any{
									"value":     keyword,
									"fuzziness": 1,
									"boost":     4 * 0.5,
								},
							},
						},
						// 作者名
						{
							"match_phrase": map[string]any{
								"creator_user_name": map[string]any{
									"query": keyword,
									"boost": 7 * 3,
								},
							},
						},
						{
							"match": map[string]any{
								"creator_user_name": map[string]any{
									"query": keyword,
									"boost": 5 * 2,
								},
							},
						},
						{
							"fuzzy": map[string]any{
								"creator_user_name": map[string]any{
									"value":     keyword,
									"fuzziness": 1,
									"boost":     3 * 0.5,
								},
							},
						},
						// 简介
						{
							"match_phrase": map[string]any{
								"intro": map[string]any{
									"query": keyword,
									"boost": 5 * 3,
								},
							},
						},
						{
							"match": map[string]any{
								"intro": map[string]any{
									"query": keyword,
									"boost": 3 * 2,
								},
							},
						},
						{
							"fuzzy": map[string]any{
								"intro": map[string]any{
									"value":     keyword,
									"fuzziness": 1,
									"boost":     2 * 0.5,
								},
							},
						},
					},
				},
			},
			"functions": []map[string]any{
				{
					"field_value_factor": map[string]any{
						"field":   "score",
						"missing": 0.1,
					},
				},
			},
			"boost_mode": "multiply",
		},
	}
}

// getStoryQuery 获取sekai的搜索查询
// nolint:funlen
func getStoryQuery(keyword string, userID int) map[string]any {
	return map[string]any{
		"function_score": map[string]any{
			"query": map[string]any{
				"bool": map[string]any{
					"must": []map[string]any{
						{
							"term": map[string]any{
								"isDelete": map[string]any{"value": false},
							},
						},
						{
							"term": map[string]any{
								"baseUniverseId": map[string]any{"value": 0},
							},
						},
						{
							"bool": map[string]any{
								"must_not": []map[string]any{
									{
										"terms": map[string]any{
											"source": []int{1, 5},
										},
									},
								},
							},
						},
						{
							"bool": map[string]any{
								"should": []map[string]any{
									{"term": map[string]any{"creatorUserId": map[string]any{"value": userID}}},
									{"term": map[string]any{"public": map[string]any{"value": true}}},
								},
							},
						},
						{
							"bool": map[string]any{
								"should": []map[string]any{
									{"term": map[string]any{"creatorUserId": map[string]any{"value": userID}}},
									{"term": map[string]any{"nsfw": map[string]any{"value": false}}},
								},
							},
						},
					},
					"should": []map[string]any{
						{
							"match_phrase": map[string]any{
								"highlight.text": map[string]any{
									"query": keyword,
									"boost": 10 * 3,
								},
							},
						},
						{
							"match": map[string]any{
								"highlight.text": map[string]any{
									"query": keyword,
									"boost": 8 * 2,
								},
							},
						},
						{
							"fuzzy": map[string]any{
								"highlight.text": map[string]any{
									"value":     keyword,
									"fuzziness": 1,
									"boost":     6 * 0.5,
								},
							},
						},
						{
							"match_phrase": map[string]any{
								"char_names": map[string]any{
									"query": keyword,
									"boost": 9 * 3,
								},
							},
						},
						{
							"match": map[string]any{
								"char_names": map[string]any{
									"query": keyword,
									"boost": 7 * 2,
								},
							},
						},
						{
							"fuzzy": map[string]any{
								"char_names": map[string]any{
									"value":     keyword,
									"fuzziness": 1,
									"boost":     5 * 0.5,
								},
							},
						},
						{
							"match_phrase": map[string]any{
								"tag_names": map[string]any{
									"query": keyword,
									"boost": 8 * 3,
								},
							},
						},
						{
							"match": map[string]any{
								"tag_names": map[string]any{
									"query": keyword,
									"boost": 6 * 2,
								},
							},
						},
						{
							"fuzzy": map[string]any{
								"tag_names": map[string]any{
									"value":     keyword,
									"fuzziness": 1,
									"boost":     4 * 0.5,
								},
							},
						},
						{
							"match_phrase": map[string]any{
								"creatorUserName": map[string]any{
									"query": keyword,
									"boost": 7 * 3,
								},
							},
						},
						{
							"match": map[string]any{
								"creatorUserName": map[string]any{
									"query": keyword,
									"boost": 5 * 2,
								},
							},
						},
						{
							"fuzzy": map[string]any{
								"creatorUserName": map[string]any{
									"value":     keyword,
									"fuzziness": 1,
									"boost":     3 * 0.5,
								},
							},
						},
					},
				},
			},
			"functions": []map[string]any{
				{
					"weight": 1,
				},
				{
					"field_value_factor": map[string]any{
						"field":    "viewCount",
						"modifier": "sqrt",
						"missing":  0,
					},
				},
			},
			"score_mode": "sum",
			"boost_mode": "multiply",
		},
	}
}

// SearchStoryByKeyword 根据关键词搜索story
func (o *opensearchImpl) SearchStoryByKeyword(ctx context.Context, req SearchStoryReq) (*SearchResult[Story], error) {
	query := map[string]any{
		"from":  (req.Page - 1) * req.Size,
		"size":  req.Size,
		"query": getStoryQuery(req.Keyword, req.UserID),
	}

	var response SearchResult[Story]
	err := o.cli.Search(ctx, (&Story{}).Index(), query, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

func (o *opensearchImpl) SearchVoiceByKeyword(ctx context.Context, req SearchVoiceReq) (*SearchResult[Voice], error) {
	query := map[string]any{
		"_source": []string{"voiceId", "displayName"},
		"from":    (req.Page - 1) * req.Size,
		"size":    req.Size,
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{"term": map[string]any{"enable": map[string]any{"value": true}}},
					{"term": map[string]any{"nsfw": map[string]any{"value": false}}},
					{
						"bool": map[string]any{
							"should": []map[string]any{
								{"term": map[string]any{"creatorUserId": map[string]any{"value": req.UserID}}},
								{"term": map[string]any{"displayEnable": map[string]any{"value": true}}},
							},
						},
					},
					{
						"bool": map[string]any{
							"should": getQueryNameCondition("displayName", req.Keyword),
						},
					},
				},
			},
		},
	}

	var response SearchResult[Voice]
	err := o.cli.Search(ctx, (&Voice{}).Index(), query, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}
