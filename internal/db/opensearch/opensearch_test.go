package opensearch

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"

	"github.com/sekai-app/sekai-go/internal/db/opensearch/internal/mocks"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestOpenSearchImpl_SearchBGMByName(t *testing.T) {
	// Set up test cases
	tests := []struct {
		name          string
		userID        int
		searchName    string
		page          int
		size          int
		mockSetup     func(*mocks.MockopenSearchClientAPI)
		expectedHits  []SearchHit[BGM]
		expectedTotal int
		expectedErr   error
	}{
		{
			name:       "successful search",
			userID:     123,
			searchName: "test bgm",
			page:       1,
			size:       10,
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"aiu-bgm-table",
						gomock.Any(), // We'll validate this separately
						gomock.Any(),
					).
					DoAndReturn(func(_ context.Context, _ string, query map[string]any, result any) error {
						// Validate query structure
						assert.Equal(t, []string{"id", "name"}, query["_source"])
						assert.Equal(t, 0, query["from"]) // page 1 - 1 * 10
						assert.Equal(t, 10, query["size"])

						// Validate query structure
						boolQuery, ok := query["query"].(map[string]any)["bool"].(map[string]any)
						assert.True(t, ok, "Expected bool query structure")

						mustClauses, ok := boolQuery["must"].([]map[string]any)
						assert.True(t, ok, "Expected must clauses")
						assert.Len(t, mustClauses, 4, "Expected 4 must clauses")

						// Check enable clause
						enableClause := mustClauses[0]["term"].(map[string]any)["enable"].(map[string]any)
						assert.Equal(t, true, enableClause["value"])

						// Check nsfw clause
						nsfwClause := mustClauses[1]["term"].(map[string]any)["nsfw"].(map[string]any)
						assert.Equal(t, false, nsfwClause["value"])

						// Check creator/public clause
						creatorPublicClause := mustClauses[2]["bool"].(map[string]any)["should"].([]map[string]any)
						assert.Len(t, creatorPublicClause, 2, "Expected 2 should clauses in creator/public filter")
						assert.Equal(t, 123, creatorPublicClause[0]["term"].(map[string]any)["creatorUserId"].(map[string]any)["value"])
						assert.Equal(t, true, creatorPublicClause[1]["term"].(map[string]any)["public"].(map[string]any)["value"])

						// Check name search clause
						nameQueryClause := mustClauses[3]["bool"].(map[string]any)["should"].([]map[string]any)
						assert.Len(t, nameQueryClause, 5, "Expected 5 search conditions for name")

						// Set mock response
						response := result.(*SearchResult[BGM])
						response.Hits.Total.Value = 2
						response.Hits.Hits = []SearchHit[BGM]{
							{
								Score: 1.5,
								Source: BGM{
									ID:   1,
									Name: "test bgm 1",
								},
							},
							{
								Score: 1.2,
								Source: BGM{
									ID:   2,
									Name: "test bgm 2",
								},
							},
						}
						return nil
					})
			},
			expectedHits: []SearchHit[BGM]{
				{
					Score: 1.5,
					Source: BGM{
						ID:   1,
						Name: "test bgm 1",
					},
				},
				{
					Score: 1.2,
					Source: BGM{
						ID:   2,
						Name: "test bgm 2",
					},
				},
			},
			expectedTotal: 2,
			expectedErr:   nil,
		},
		{
			name:       "search with pagination",
			userID:     123,
			searchName: "test bgm",
			page:       2,
			size:       5,
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"aiu-bgm-table",
						gomock.Any(),
						gomock.Any(),
					).
					DoAndReturn(func(_ context.Context, _ string, query map[string]any, result any) error {
						// Validate pagination parameters
						assert.Equal(t, 5, query["from"], "From should be (page-1)*size = 5")
						assert.Equal(t, 5, query["size"], "Size should match the input size")

						// Set mock response
						response := result.(*SearchResult[BGM])
						response.Hits.Total.Value = 7
						response.Hits.Hits = []SearchHit[BGM]{
							{
								Score: 1.0,
								Source: BGM{
									ID:   6,
									Name: "another test bgm",
								},
							},
							{
								Score: 0.9,
								Source: BGM{
									ID:   7,
									Name: "final test bgm",
								},
							},
						}
						return nil
					})
			},
			expectedHits: []SearchHit[BGM]{
				{
					Score: 1.0,
					Source: BGM{
						ID:   6,
						Name: "another test bgm",
					},
				},
				{
					Score: 0.9,
					Source: BGM{
						ID:   7,
						Name: "final test bgm",
					},
				},
			},
			expectedTotal: 7,
			expectedErr:   nil,
		},
		{
			name:       "search error",
			userID:     123,
			searchName: "error bgm",
			page:       1,
			size:       10,
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"aiu-bgm-table",
						gomock.Any(),
						gomock.Any(),
					).
					Return(errors.New("search error"))
			},
			expectedHits:  nil,
			expectedTotal: 0,
			expectedErr:   errors.New("search error"),
		},
		{
			name:       "empty result",
			userID:     123,
			searchName: "nonexistent bgm",
			page:       1,
			size:       10,
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"aiu-bgm-table",
						gomock.Any(),
						gomock.Any(),
					).
					DoAndReturn(func(_ context.Context, _ string, _ map[string]any, result any) error {
						// Set mock response with empty results
						response := result.(*SearchResult[BGM])
						response.Hits.Total.Value = 0
						response.Hits.Hits = []SearchHit[BGM]{}
						return nil
					})
			},
			expectedHits:  []SearchHit[BGM]{},
			expectedTotal: 0,
			expectedErr:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set up mock controller
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Create mock client
			mockClient := mocks.NewMockopenSearchClientAPI(ctrl)

			// Set up mock expectations
			tt.mockSetup(mockClient)

			// Create opensearch instance with mocked client
			os := &opensearchImpl{cli: mockClient}

			// Call method under test
			hits, total, err := os.SearchBGMByName(
				context.Background(),
				tt.userID,
				tt.searchName,
				tt.page,
				tt.size,
			)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedTotal, total)
				assert.Equal(t, tt.expectedHits, hits)
			}
		})
	}
}

func TestGetQueryNameCondition(t *testing.T) {
	conditions := getQueryNameCondition("field", "value")

	assert.Len(t, conditions, 5, "Should return 5 query conditions")

	// Check match condition
	assert.Contains(t, conditions[0], "match")
	matchCond := conditions[0]["match"].(map[string]any)
	assert.Contains(t, matchCond, "field.searchable")
	matchField := matchCond["field.searchable"].(map[string]any)
	assert.Equal(t, "value", matchField["query"])
	assert.Equal(t, 0.5, matchField["boost"])

	// Check match_phrase condition
	assert.Contains(t, conditions[1], "match_phrase")
	matchPhraseField := conditions[1]["match_phrase"].(map[string]any)["field.searchable"].(map[string]any)
	assert.Equal(t, "value", matchPhraseField["query"])
	assert.Equal(t, 0.8, matchPhraseField["boost"])

	// Check match_phrase_prefix condition
	assert.Contains(t, conditions[2], "match_phrase_prefix")
	matchPhrasePrefixField := conditions[2]["match_phrase_prefix"].(map[string]any)["field.searchable"].(map[string]any)
	assert.Equal(t, "value", matchPhrasePrefixField["query"])
	assert.Equal(t, 0.3, matchPhrasePrefixField["boost"])

	// Check term condition
	assert.Contains(t, conditions[3], "term")
	termField := conditions[3]["term"].(map[string]any)["field.searchable"].(map[string]any)
	assert.Equal(t, "value", termField["value"])
	assert.Equal(t, 1.0, termField["boost"])

	// Check fuzzy condition
	assert.Contains(t, conditions[4], "fuzzy")
	fuzzyField := conditions[4]["fuzzy"].(map[string]any)["field.searchable"].(map[string]any)
	assert.Equal(t, "value", fuzzyField["value"])
	assert.Equal(t, 1, fuzzyField["fuzziness"])
	assert.Equal(t, 0.1, fuzzyField["boost"])
}

func TestOpenSearchImpl_SearchSekaiByKeyword(t *testing.T) {
	// Set up test cases
	tests := []struct {
		name         string
		req          SearchSekaiReq
		mockSetup    func(*mocks.MockopenSearchClientAPI)
		expectedHits []SearchHit[Sekai]
		expectedErr  error
	}{
		{
			name: "successful search with keyword",
			req: SearchSekaiReq{
				Keyword:    "test sekai",
				AppVersion: "1.17.0",
				UserID:     123,
				Page:       1,
				Size:       10,
			},
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"aiu_sekai_info_table",
						gomock.Any(), // We'll validate this separately
						gomock.Any(),
					).
					DoAndReturn(func(_ context.Context, _ string, query map[string]any, result any) error {
						// Validate query structure
						assert.Equal(t, 0, query["from"]) // (page 1 - 1) * 10
						assert.Equal(t, 10, query["size"])

						// Validate app version conversion
						appVersionInt := 1017000 // 1.17.0 converted to 1017000
						queryObj := query["query"].(map[string]any)["function_score"].(map[string]any)["query"].(map[string]any)["bool"].(map[string]any)
						mustClauses := queryObj["must"].([]map[string]any)

						// Check app version range clause
						versionClause := mustClauses[3]["range"].(map[string]any)["min_app_version"].(map[string]any)
						assert.Equal(t, appVersionInt, versionClause["lte"])

						// Set mock response
						response := result.(*SearchResult[Sekai])
						response.Hits.Total.Value = 2
						response.Hits.Hits = []SearchHit[Sekai]{
							{
								Score: 1.5,
								Source: Sekai{
									ID: 1,
								},
							},
							{
								Score: 1.2,
								Source: Sekai{
									ID: 2,
								},
							},
						}
						return nil
					})
			},
			expectedHits: []SearchHit[Sekai]{
				{
					Score: 1.5,
					Source: Sekai{
						ID: 1,
					},
				},
				{
					Score: 1.2,
					Source: Sekai{
						ID: 2,
					},
				},
			},
			expectedErr: nil,
		},
		{
			name: "search with pagination",
			req: SearchSekaiReq{
				Keyword:    "test sekai",
				AppVersion: "1.2.3",
				UserID:     123,
				Page:       2,
				Size:       5,
			},
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"aiu_sekai_info_table",
						gomock.Any(),
						gomock.Any(),
					).
					DoAndReturn(func(_ context.Context, _ string, query map[string]any, result any) error {
						// Validate pagination parameters
						assert.Equal(t, 5, query["from"], "From should be (page-1)*size = 5")
						assert.Equal(t, 5, query["size"], "Size should match the input size")

						// Validate app version conversion
						appVersionInt := 1002003 // 1.2.3 converted to 1002003
						queryObj := query["query"].(map[string]any)["function_score"].(map[string]any)["query"].(map[string]any)["bool"].(map[string]any)
						mustClauses := queryObj["must"].([]map[string]any)

						// Check app version range clause
						versionClause := mustClauses[3]["range"].(map[string]any)["min_app_version"].(map[string]any)
						assert.Equal(t, appVersionInt, versionClause["lte"])

						// Set mock response
						response := result.(*SearchResult[Sekai])
						response.Hits.Total.Value = 7
						response.Hits.Hits = []SearchHit[Sekai]{
							{
								Score: 1.0,
								Source: Sekai{
									ID: 6,
								},
							},
							{
								Score: 0.9,
								Source: Sekai{
									ID: 7,
								},
							},
						}
						return nil
					})
			},
			expectedHits: []SearchHit[Sekai]{
				{
					Score: 1.0,
					Source: Sekai{
						ID: 6,
					},
				},
				{
					Score: 0.9,
					Source: Sekai{
						ID: 7,
					},
				},
			},
			expectedErr: nil,
		},
		{
			name: "invalid app version format",
			req: SearchSekaiReq{
				Keyword:    "test sekai",
				AppVersion: "invalid.version",
				UserID:     123,
				Page:       1,
				Size:       10,
			},
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"aiu_sekai_info_table",
						gomock.Any(),
						gomock.Any(),
					).
					DoAndReturn(func(_ context.Context, _ string, query map[string]any, result any) error {
						// For invalid version, the appVersionInt should be 0
						queryObj := query["query"].(map[string]any)["function_score"].(map[string]any)["query"].(map[string]any)["bool"].(map[string]any)
						mustClauses := queryObj["must"].([]map[string]any)

						// Check app version range clause
						versionClause := mustClauses[3]["range"].(map[string]any)["min_app_version"].(map[string]any)
						assert.Equal(t, 0, versionClause["lte"])

						// Set mock response
						response := result.(*SearchResult[Sekai])
						response.Hits.Total.Value = 1
						response.Hits.Hits = []SearchHit[Sekai]{
							{
								Score: 1.0,
								Source: Sekai{
									ID: 1,
								},
							},
						}
						return nil
					})
			},
			expectedHits: []SearchHit[Sekai]{
				{
					Score: 1.0,
					Source: Sekai{
						ID: 1,
					},
				},
			},
			expectedErr: nil,
		},
		{
			name: "search error",
			req: SearchSekaiReq{
				Keyword:    "error sekai",
				AppVersion: "1.0.0",
				UserID:     123,
				Page:       1,
				Size:       10,
			},
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"aiu_sekai_info_table",
						gomock.Any(),
						gomock.Any(),
					).
					Return(errors.New("search error"))
			},
			expectedHits: nil,
			expectedErr:  errors.New("search error"),
		},
		{
			name: "empty result",
			req: SearchSekaiReq{
				Keyword:    "nonexistent sekai",
				AppVersion: "1.0.0",
				UserID:     123,
				Page:       1,
				Size:       10,
			},
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"aiu_sekai_info_table",
						gomock.Any(),
						gomock.Any(),
					).
					DoAndReturn(func(_ context.Context, _ string, _ map[string]any, result any) error {
						// Set mock response with empty results
						response := result.(*SearchResult[Sekai])
						response.Hits.Total.Value = 0
						response.Hits.Hits = []SearchHit[Sekai]{}
						return nil
					})
			},
			expectedHits: []SearchHit[Sekai]{},
			expectedErr:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set up mock controller
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Create mock client
			mockClient := mocks.NewMockopenSearchClientAPI(ctrl)

			// Set up mock expectations
			tt.mockSetup(mockClient)

			// Create opensearch instance with mocked client
			os := &opensearchImpl{cli: mockClient}

			// Call method under test
			result, err := os.SearchSekaiByKeyword(context.Background(), tt.req)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr.Error())
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, len(tt.expectedHits), len(result.Hits.Hits))
				assert.Equal(t, tt.expectedHits, result.Hits.Hits)
			}
		})
	}
}

func TestOpenSearchImpl_SearchSekaiKeywords(t *testing.T) {
	// Set up test cases
	tests := []struct {
		name          string
		prefix        string
		size          int
		mockSetup     func(*mocks.MockopenSearchClientAPI)
		expectedKeys  []string
		expectedTotal int
		expectedErr   error
	}{
		{
			name:   "successful search with prefix",
			prefix: "char",
			size:   10,
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"character-tags-index",
						gomock.Any(), // We'll validate this separately
						gomock.Any(),
					).
					DoAndReturn(func(_ context.Context, _ string, query map[string]any, result any) error {
						// Validate query structure
						assert.Equal(t, []string{"keywordName", "keywordType", "useCount"}, query["_source"])
						assert.Equal(t, 10, query["size"])

						// Validate query bool should clause
						shouldClause := query["query"].(map[string]any)["bool"].(map[string]any)["should"].([]map[string]any)
						assert.Len(t, shouldClause, 1, "Expected 1 should clause")

						// Check match_phrase_prefix condition
						matchPhrasePrefix := shouldClause[0]["match_phrase_prefix"].(map[string]any)["keywordName"].(map[string]any)
						assert.Equal(t, "char", matchPhrasePrefix["query"])
						assert.Equal(t, 3.0, matchPhrasePrefix["boost"])

						// Validate sort criteria
						sortCriteria := query["sort"].([]map[string]any)
						assert.Len(t, sortCriteria, 2, "Expected 2 sort criteria")
						assert.Equal(t, "desc", sortCriteria[0]["_score"].(map[string]any)["order"])
						assert.Equal(t, "desc", sortCriteria[1]["useCount"].(map[string]any)["order"])

						// 使用反射来设置内部字段，而不是直接类型转换
						respVal := reflect.ValueOf(result).Elem()
						hitsField := respVal.FieldByName("Hits")
						totalField := hitsField.FieldByName("Total")
						totalValueField := totalField.FieldByName("Value")
						totalValueField.SetInt(3)

						// 设置hits数组
						// hitsSliceField := hitsField.FieldByName("Hits")
						// hitsType需要与实现中的类型匹配

						// 使用JSON进行模拟响应设置
						mockResponse := map[string]interface{}{
							"hits": map[string]interface{}{
								"total": map[string]interface{}{
									"value": 3,
								},
								"hits": []map[string]interface{}{
									{
										"_score": 1.5,
										"_source": map[string]interface{}{
											"keywordName": "character1",
											"keywordType": "char",
											"useCount":    100,
										},
									},
									{
										"_score": 1.2,
										"_source": map[string]interface{}{
											"keywordName": "character2",
											"keywordType": "char",
											"useCount":    80,
										},
									},
									{
										"_score": 1.0,
										"_source": map[string]interface{}{
											"keywordName": "character3",
											"keywordType": "char",
											"useCount":    60,
										},
									},
								},
							},
						}

						// 转换为JSON并填充到结果中
						mockJSON, _ := json.Marshal(mockResponse)
						json.Unmarshal(mockJSON, result)

						return nil
					})
			},
			expectedKeys:  []string{"character1", "character2", "character3"},
			expectedTotal: 3,
			expectedErr:   nil,
		},
		{
			name:   "empty prefix returns empty results",
			prefix: "",
			size:   10,
			mockSetup: func(_ *mocks.MockopenSearchClientAPI) {
				// No mock call expected since empty prefix returns immediately
			},
			expectedKeys:  []string{},
			expectedTotal: 0,
			expectedErr:   nil,
		},
		{
			name:   "search error",
			prefix: "error",
			size:   10,
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"character-tags-index",
						gomock.Any(),
						gomock.Any(),
					).
					Return(errors.New("search error"))
			},
			expectedKeys:  nil,
			expectedTotal: 0,
			expectedErr:   errors.New("search error"),
		},
		{
			name:   "empty result",
			prefix: "nonexistent",
			size:   10,
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"character-tags-index",
						gomock.Any(),
						gomock.Any(),
					).
					DoAndReturn(func(_ context.Context, _ string, _ map[string]any, result any) error {
						// 使用JSON进行模拟响应设置 - 空结果
						mockResponse := map[string]interface{}{
							"hits": map[string]interface{}{
								"total": map[string]interface{}{
									"value": 0,
								},
								"hits": []map[string]interface{}{},
							},
						}

						// 转换为JSON并填充到结果中
						mockJSON, _ := json.Marshal(mockResponse)
						json.Unmarshal(mockJSON, result)

						return nil
					})
			},
			expectedKeys:  []string{},
			expectedTotal: 0,
			expectedErr:   nil,
		},
		{
			name:   "custom size parameter",
			prefix: "char",
			size:   5,
			mockSetup: func(mockClient *mocks.MockopenSearchClientAPI) {
				mockClient.EXPECT().
					Search(
						gomock.Any(),
						"character-tags-index",
						gomock.Any(),
						gomock.Any(),
					).
					DoAndReturn(func(_ context.Context, _ string, query map[string]any, result any) error {
						// Validate size parameter is passed correctly
						assert.Equal(t, 5, query["size"], "Size should match the input size")

						// 使用JSON进行模拟响应设置
						mockResponse := map[string]interface{}{
							"hits": map[string]interface{}{
								"total": map[string]interface{}{
									"value": 2,
								},
								"hits": []map[string]interface{}{
									{
										"_score": 1.5,
										"_source": map[string]interface{}{
											"keywordName": "character1",
											"keywordType": "char",
											"useCount":    100,
										},
									},
									{
										"_score": 1.2,
										"_source": map[string]interface{}{
											"keywordName": "character2",
											"keywordType": "char",
											"useCount":    80,
										},
									},
								},
							},
						}

						// 转换为JSON并填充到结果中
						mockJSON, _ := json.Marshal(mockResponse)
						json.Unmarshal(mockJSON, result)

						return nil
					})
			},
			expectedKeys:  []string{"character1", "character2"},
			expectedTotal: 2,
			expectedErr:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set up mock controller
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Create mock client
			mockClient := mocks.NewMockopenSearchClientAPI(ctrl)

			// Set up mock expectations
			tt.mockSetup(mockClient)

			// Create opensearch instance with mocked client
			os := &opensearchImpl{cli: mockClient}

			// Call method under test
			keywords, total, err := os.SearchSekaiKeywords(
				context.Background(),
				tt.prefix,
				tt.size,
			)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedTotal, total)
				assert.Equal(t, tt.expectedKeys, keywords)
			}
		})
	}
}
