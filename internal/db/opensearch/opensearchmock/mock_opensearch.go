// Code generated by MockGen. DO NOT EDIT.
// Source: opensearch.go
//
// Generated by this command:
//
//	mockgen -source=opensearch.go -destination=opensearchmock/mock_opensearch.go -package=opensearchmock
//

// Package opensearchmock is a generated GoMock package.
package opensearchmock

import (
	context "context"
	reflect "reflect"

	opensearch "github.com/sekai-app/sekai-go/internal/db/opensearch"
	gomock "go.uber.org/mock/gomock"
)

// MockOpenSearch is a mock of OpenSearch interface.
type MockOpenSearch struct {
	ctrl     *gomock.Controller
	recorder *MockOpenSearchMockRecorder
	isgomock struct{}
}

// MockOpenSearchMockRecorder is the mock recorder for MockOpenSearch.
type MockOpenSearchMockRecorder struct {
	mock *MockOpenSearch
}

// NewMockOpenSearch creates a new mock instance.
func NewMockOpenSearch(ctrl *gomock.Controller) *MockOpenSearch {
	mock := &MockOpenSearch{ctrl: ctrl}
	mock.recorder = &MockOpenSearchMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOpenSearch) EXPECT() *MockOpenSearchMockRecorder {
	return m.recorder
}

// SearchBGMByName mocks base method.
func (m *MockOpenSearch) SearchBGMByName(ctx context.Context, userID int, name string, page, size int) ([]opensearch.SearchHit[opensearch.BGM], int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchBGMByName", ctx, userID, name, page, size)
	ret0, _ := ret[0].([]opensearch.SearchHit[opensearch.BGM])
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SearchBGMByName indicates an expected call of SearchBGMByName.
func (mr *MockOpenSearchMockRecorder) SearchBGMByName(ctx, userID, name, page, size any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchBGMByName", reflect.TypeOf((*MockOpenSearch)(nil).SearchBGMByName), ctx, userID, name, page, size)
}

// SearchSekaiByKeyword mocks base method.
func (m *MockOpenSearch) SearchSekaiByKeyword(ctx context.Context, req opensearch.SearchSekaiReq) (*opensearch.SearchResult[opensearch.Sekai], error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchSekaiByKeyword", ctx, req)
	ret0, _ := ret[0].(*opensearch.SearchResult[opensearch.Sekai])
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchSekaiByKeyword indicates an expected call of SearchSekaiByKeyword.
func (mr *MockOpenSearchMockRecorder) SearchSekaiByKeyword(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchSekaiByKeyword", reflect.TypeOf((*MockOpenSearch)(nil).SearchSekaiByKeyword), ctx, req)
}

// SearchSekaiKeywords mocks base method.
func (m *MockOpenSearch) SearchSekaiKeywords(ctx context.Context, prefix string, size int) ([]string, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchSekaiKeywords", ctx, prefix, size)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SearchSekaiKeywords indicates an expected call of SearchSekaiKeywords.
func (mr *MockOpenSearchMockRecorder) SearchSekaiKeywords(ctx, prefix, size any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchSekaiKeywords", reflect.TypeOf((*MockOpenSearch)(nil).SearchSekaiKeywords), ctx, prefix, size)
}

// SearchStoryByKeyword mocks base method.
func (m *MockOpenSearch) SearchStoryByKeyword(ctx context.Context, req opensearch.SearchStoryReq) (*opensearch.SearchResult[opensearch.Story], error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchStoryByKeyword", ctx, req)
	ret0, _ := ret[0].(*opensearch.SearchResult[opensearch.Story])
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchStoryByKeyword indicates an expected call of SearchStoryByKeyword.
func (mr *MockOpenSearchMockRecorder) SearchStoryByKeyword(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchStoryByKeyword", reflect.TypeOf((*MockOpenSearch)(nil).SearchStoryByKeyword), ctx, req)
}

// SearchVoiceByKeyword mocks base method.
func (m *MockOpenSearch) SearchVoiceByKeyword(ctx context.Context, req opensearch.SearchVoiceReq) (*opensearch.SearchResult[opensearch.Voice], error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchVoiceByKeyword", ctx, req)
	ret0, _ := ret[0].(*opensearch.SearchResult[opensearch.Voice])
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchVoiceByKeyword indicates an expected call of SearchVoiceByKeyword.
func (mr *MockOpenSearchMockRecorder) SearchVoiceByKeyword(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchVoiceByKeyword", reflect.TypeOf((*MockOpenSearch)(nil).SearchVoiceByKeyword), ctx, req)
}
