package opensearch

// BGM 是OpenSearch中的BGM模型
type BGM struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

// Index 是BGM的索引
func (b *BGM) Index() string {
	return "aiu-bgm-table"
}

// Sekai 是OpenSearch中的Sekai模型
type Sekai struct {
	ID int `json:"sekai_id"`
	// 若需要，可以添加其他字段
}

// Index 是Sekai的索引
func (s *Sekai) Index() string {
	return "aiu_sekai_info_table"
}

// Story represents a story document in opensearch
type Story struct {
	ID int `json:"universeId"`
	// 若需要，可以添加其他字段
}

// Index returns the index name for story
func (s *Story) Index() string {
	return "aiu-universe-table"
}

// Voice represents a voice document in opensearch
type Voice struct {
	VoiceID     int    `json:"voiceId"`
	DisplayName string `json:"displayName"`
}

// Index returns the index name for voice
func (v *Voice) Index() string {
	return "aiu-voice-table"
}
