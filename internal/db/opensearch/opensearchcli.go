package opensearch

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"time"

	"github.com/opensearch-project/opensearch-go"
	"github.com/sekai-app/sekai-go/pkg/httpclimetric"
	"github.com/zeromicro/go-zero/core/logc"
)

// openSearchClientAPI 由于 opensearch sdk 没有提供mock方法，这里包装一层是 OpenSearch 接口，方便单元测试
//
//go:generate mockgen -source=opensearchcli.go -destination=internal/mocks/opensearchcli.go -package=mocks
type openSearchClientAPI interface {
	Search(ctx context.Context, index string, query map[string]any, result any) error
}

type opensearchClient struct {
	cli *opensearch.Client
}

func newOpenSearchClient(cfg Config) (openSearchClientAPI, error) {
	cli, err := opensearch.NewClient(opensearch.Config{
		Addresses: []string{cfg.Addr},
		Username:  cfg.User,
		Password:  cfg.Password,
		Transport: httpclimetric.NewTransport(&http.Transport{
			DialContext: (&net.Dialer{
				Timeout: 5 * time.Second, // 连接超时
			}).DialContext,
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		}),
	})
	if err != nil {
		return nil, err
	}
	return &opensearchClient{cli: cli}, nil
}

func (o *opensearchClient) Search(ctx context.Context, index string, query map[string]any, result any) error {
	// Convert body to JSON
	bodyJSON, err := json.Marshal(query)
	if err != nil {
		return fmt.Errorf("failed to marshal search body: %w", err)
	}
	logc.Debugf(ctx, "search query: %s", string(bodyJSON))

	// Perform the search request
	searchRsp, err := o.cli.Search(
		o.cli.Search.WithContext(ctx),
		o.cli.Search.WithIndex(index),
		o.cli.Search.WithBody(bytes.NewReader(bodyJSON)),
	)
	if err != nil {
		return fmt.Errorf("search request failed: %v", err)
	}
	defer searchRsp.Body.Close()

	if searchRsp.IsError() {
		var e map[string]any
		if err := json.NewDecoder(searchRsp.Body).Decode(&e); err != nil {
			return fmt.Errorf("search response error: %s and failed to parse error details: %v", searchRsp.String(), err)
		}
		return fmt.Errorf("search response error: %v", e)
	}

	// Parse the response
	if err := json.NewDecoder(searchRsp.Body).Decode(result); err != nil {
		return fmt.Errorf("failed to decode search response: %w", err)
	}

	return nil
}
