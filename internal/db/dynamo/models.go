package dynamo

import (
	"fmt"
	"os"
)

func getTableSuffix() string {
	env := os.Getenv("AIU_ENV")
	// nolint
	if env == "production" || env == "stage" {
		return "prod"
	}
	return "stage"
}

// User 用户表
type User struct {
	ID        int    `dynamo:"userId"`
	UserName  string `dynamo:"userName"`
	AvatarURL string `dynamo:"avatarUrl"`
	// todo 迁移更多字段
}

func (u *User) TableName() string {
	return fmt.Sprintf("aiu_%s_user_table", getTableSuffix())
}

// TopInfo 排行榜信息表,用保存不同的排行榜
type TopInfo struct {
	Key   string `dynamo:"key"`
	Value string `dynamo:"value"`
}

func (t *TopInfo) TableName() string {
	return fmt.Sprintf("aiu_%s_top_info_table", getTableSuffix())
}

// BGMMetaInfo BGM元信息表
type BGMMetaInfo struct {
	ID        int `dynamo:"id"`
	UseCount  int `dynamo:"useCount"`
	LikeCount int `dynamo:"likeCount"`
}

func (b *BGMMetaInfo) TableName() string {
	return fmt.Sprintf("aiu_%s_bgm_meta_info_table", getTableSuffix())
}

// VoiceMetaInfo 语音元信息表
type VoiceMetaInfo struct {
	ID        int `dynamo:"id"`
	UseCount  int `dynamo:"useCount"`
	LikeCount int `dynamo:"likeCount"`
}

func (v *VoiceMetaInfo) TableName() string {
	return fmt.Sprintf("aiu_%s_voice_meta_info_table", getTableSuffix())
}

// StoryViewCount 故事浏览量表
type StoryViewCount struct {
	StoryID   int `dynamo:"sekaiId"`
	ViewCount int `dynamo:"viewCount"`
}

func (s *StoryViewCount) TableName() string {
	return fmt.Sprintf("aiu_%s_sekai_view_count", getTableSuffix())
}
