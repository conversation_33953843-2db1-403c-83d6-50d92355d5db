package dynamo

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_getTableSuffix(t *testing.T) {
	tests := []struct {
		name        string
		envSetup    func()
		envTeardown func()
		want        string
	}{
		{
			name: "production environment",
			envSetup: func() {
				os.Setenv("AIU_ENV", "production")
			},
			envTeardown: func() {
				os.Unsetenv("AIU_ENV")
			},
			want: "prod",
		},
		{
			name: "stage environment",
			envSetup: func() {
				os.Setenv("AIU_ENV", "stage")
			},
			envTeardown: func() {
				os.Unsetenv("AIU_ENV")
			},
			want: "prod",
		},
		{
			name: "development environment",
			envSetup: func() {
				os.Setenv("AIU_ENV", "development")
			},
			envTeardown: func() {
				os.Unsetenv("AIU_ENV")
			},
			want: "stage",
		},
		{
			name: "empty environment",
			envSetup: func() {
				os.Unsetenv("AIU_ENV")
			},
			envTeardown: func() {},
			want:        "stage",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup environment
			tt.envSetup()
			defer tt.envTeardown()

			// Execute function
			got := getTableSuffix()

			// Assert results
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestUser_TableName(t *testing.T) {
	tests := []struct {
		name     string
		envSetup func()
		want     string
	}{
		{
			name: "production environment",
			envSetup: func() {
				os.Setenv("AIU_ENV", "production")
			},
			want: "aiu_prod_user_table",
		},
		{
			name: "development environment",
			envSetup: func() {
				os.Setenv("AIU_ENV", "development")
			},
			want: "aiu_stage_user_table",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup environment
			tt.envSetup()
			defer os.Unsetenv("AIU_ENV")

			// Create test object
			user := &User{}

			// Execute function
			got := user.TableName()

			// Assert results
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestTopInfo_TableName(t *testing.T) {
	tests := []struct {
		name     string
		envSetup func()
		want     string
	}{
		{
			name: "production environment",
			envSetup: func() {
				os.Setenv("AIU_ENV", "production")
			},
			want: "aiu_prod_top_info_table",
		},
		{
			name: "development environment",
			envSetup: func() {
				os.Setenv("AIU_ENV", "development")
			},
			want: "aiu_stage_top_info_table",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup environment
			tt.envSetup()
			defer os.Unsetenv("AIU_ENV")

			// Create test object
			topInfo := &TopInfo{}

			// Execute function
			got := topInfo.TableName()

			// Assert results
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestBGMMetaInfo_TableName(t *testing.T) {
	tests := []struct {
		name     string
		envSetup func()
		want     string
	}{
		{
			name: "production environment",
			envSetup: func() {
				os.Setenv("AIU_ENV", "production")
			},
			want: "aiu_prod_bgm_meta_info_table",
		},
		{
			name: "development environment",
			envSetup: func() {
				os.Setenv("AIU_ENV", "development")
			},
			want: "aiu_stage_bgm_meta_info_table",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup environment
			tt.envSetup()
			defer os.Unsetenv("AIU_ENV")

			// Create test object
			bgmInfo := &BGMMetaInfo{}

			// Execute function
			got := bgmInfo.TableName()

			// Assert results
			assert.Equal(t, tt.want, got)
		})
	}
}
