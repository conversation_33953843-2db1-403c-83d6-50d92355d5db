// Code generated by MockGen. DO NOT EDIT.
// Source: dynamo.go
//
// Generated by this command:
//
//	mockgen -source=dynamo.go -destination=dynamomock/mock_dynamo.go -package=dynamomock
//

// Package dynamomock is a generated GoMock package.
package dynamomock

import (
	context "context"
	reflect "reflect"

	dynamo "github.com/sekai-app/sekai-go/internal/db/dynamo"
	gomock "go.uber.org/mock/gomock"
)

// MockDB is a mock of DB interface.
type MockDB struct {
	ctrl     *gomock.Controller
	recorder *MockDBMockRecorder
	isgomock struct{}
}

// MockDBMockRecorder is the mock recorder for MockDB.
type MockDBMockRecorder struct {
	mock *MockDB
}

// NewMockDB creates a new mock instance.
func NewMockDB(ctrl *gomock.Controller) *MockDB {
	mock := &MockDB{ctrl: ctrl}
	mock.recorder = &MockDBMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDB) EXPECT() *MockDBMockRecorder {
	return m.recorder
}

// BatchGetBGMMetaInfo mocks base method.
func (m *MockDB) BatchGetBGMMetaInfo(ctx context.Context, ids []int) (map[int]*dynamo.BGMMetaInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBGMMetaInfo", ctx, ids)
	ret0, _ := ret[0].(map[int]*dynamo.BGMMetaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBGMMetaInfo indicates an expected call of BatchGetBGMMetaInfo.
func (mr *MockDBMockRecorder) BatchGetBGMMetaInfo(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBGMMetaInfo", reflect.TypeOf((*MockDB)(nil).BatchGetBGMMetaInfo), ctx, ids)
}

// BatchGetStoryViewCount mocks base method.
func (m *MockDB) BatchGetStoryViewCount(ctx context.Context, ids []int) (map[int]*dynamo.StoryViewCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetStoryViewCount", ctx, ids)
	ret0, _ := ret[0].(map[int]*dynamo.StoryViewCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetStoryViewCount indicates an expected call of BatchGetStoryViewCount.
func (mr *MockDBMockRecorder) BatchGetStoryViewCount(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetStoryViewCount", reflect.TypeOf((*MockDB)(nil).BatchGetStoryViewCount), ctx, ids)
}

// BatchGetUsers mocks base method.
func (m *MockDB) BatchGetUsers(ctx context.Context, ids []int) (map[int]*dynamo.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUsers", ctx, ids)
	ret0, _ := ret[0].(map[int]*dynamo.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUsers indicates an expected call of BatchGetUsers.
func (mr *MockDBMockRecorder) BatchGetUsers(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUsers", reflect.TypeOf((*MockDB)(nil).BatchGetUsers), ctx, ids)
}

// BatchGetVoiceMetaInfo mocks base method.
func (m *MockDB) BatchGetVoiceMetaInfo(ctx context.Context, ids []int) (map[int]*dynamo.VoiceMetaInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetVoiceMetaInfo", ctx, ids)
	ret0, _ := ret[0].(map[int]*dynamo.VoiceMetaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetVoiceMetaInfo indicates an expected call of BatchGetVoiceMetaInfo.
func (mr *MockDBMockRecorder) BatchGetVoiceMetaInfo(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetVoiceMetaInfo", reflect.TypeOf((*MockDB)(nil).BatchGetVoiceMetaInfo), ctx, ids)
}

// CreateUser mocks base method.
func (m *MockDB) CreateUser(ctx context.Context, user *dynamo.User) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUser", ctx, user)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateUser indicates an expected call of CreateUser.
func (mr *MockDBMockRecorder) CreateUser(ctx, user any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUser", reflect.TypeOf((*MockDB)(nil).CreateUser), ctx, user)
}

// GetTopInfo mocks base method.
func (m *MockDB) GetTopInfo(ctx context.Context, key string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopInfo", ctx, key)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopInfo indicates an expected call of GetTopInfo.
func (mr *MockDBMockRecorder) GetTopInfo(ctx, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopInfo", reflect.TypeOf((*MockDB)(nil).GetTopInfo), ctx, key)
}

// GetUser mocks base method.
func (m *MockDB) GetUser(ctx context.Context, id int) (*dynamo.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUser", ctx, id)
	ret0, _ := ret[0].(*dynamo.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUser indicates an expected call of GetUser.
func (mr *MockDBMockRecorder) GetUser(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUser", reflect.TypeOf((*MockDB)(nil).GetUser), ctx, id)
}

// UpdateUser mocks base method.
func (m *MockDB) UpdateUser(ctx context.Context, user *dynamo.User) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUser", ctx, user)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUser indicates an expected call of UpdateUser.
func (mr *MockDBMockRecorder) UpdateUser(ctx, user any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUser", reflect.TypeOf((*MockDB)(nil).UpdateUser), ctx, user)
}
