package dynamo

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/guregu/dynamo/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

// DB dynamoDB 接口
//
//go:generate mockgen -source=dynamo.go -destination=dynamomock/mock_dynamo.go -package=dynamomock
type DB interface {
	// CreateUser 创建用户
	CreateUser(ctx context.Context, user *User) error
	// GetUser 获取用户
	GetUser(ctx context.Context, id int) (*User, error)
	// BatchGetUsers 批量获取用户
	BatchGetUsers(ctx context.Context, ids []int) (map[int]*User, error)
	// UpdateUser 更新用户
	UpdateUser(ctx context.Context, user *User) error
	// GetTopInfo 获取排行榜信息,key 为排行榜的key
	GetTopInfo(ctx context.Context, key string) (string, error)
	// BatchGetBGMMetaInfo 批量获取BGM元信息
	BatchGetBGMMetaInfo(ctx context.Context, ids []int) (map[int]*BGMMetaInfo, error)
	// BatchGetStoryViewCount 批量获取故事浏览量
	BatchGetStoryViewCount(ctx context.Context, ids []int) (map[int]*StoryViewCount, error)
	// BatchGetVoiceMetaInfo 批量获取语音元信息
	BatchGetVoiceMetaInfo(ctx context.Context, ids []int) (map[int]*VoiceMetaInfo, error)
}

// Config 配置
type Config struct {
	Region string
}

type dynamoDB struct {
	db *dynamo.DB
}

// NewDynamoDB 创建一个 DynamoDB 客户端
func NewDynamoDB(cfg Config) (DB, error) {
	awsCfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion(cfg.Region))
	if err != nil {
		return nil, err
	}

	db := dynamo.New(awsCfg)
	_, err = db.ListTables().All(context.TODO())
	if err != nil {
		return nil, errors.Wrapf(err, "failed to list tables")
	}

	return &dynamoDB{
		db: db,
	}, nil
}

// CreateUser 创建用户
func (d *dynamoDB) CreateUser(ctx context.Context, user *User) error {
	return d.db.Table(user.TableName()).Put(user).Run(ctx)

}

// GetUser 获取用户
func (d *dynamoDB) GetUser(ctx context.Context, id int) (*User, error) {
	var user User
	err := d.db.Table(user.TableName()).Get("userId", id).One(ctx, &user)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// BatchGetUsers 批量获取用户
func (d *dynamoDB) BatchGetUsers(ctx context.Context, ids []int) (map[int]*User, error) {
	var keys []dynamo.Keyed
	for _, id := range ids {
		keys = append(keys, dynamo.Keys{id})
	}
	var users []*User
	err := d.db.Table((&User{}).TableName()).Batch("userId").Get(keys...).All(ctx, &users)
	if err == dynamo.ErrNotFound {
		return map[int]*User{}, nil
	}
	if err != nil {
		return nil, err
	}
	userMap := make(map[int]*User)
	for _, user := range users {
		userMap[user.ID] = user
	}
	return userMap, nil
}

// UpdateUser 更新用户
func (d *dynamoDB) UpdateUser(ctx context.Context, user *User) error {
	// todo: 带乐观锁检查
	// return d.db.Table(user.TableName()).Put(user).If("version = ?", user.Version-1).Run(ctx)

	// 直接更新，高并发下可能会有互相覆盖的问题，更好的版本可能需要进行乐观锁检查
	return d.db.Table(user.TableName()).Put(user).Run(ctx)
}

// GetTopInfo 获取排行榜信息
func (d *dynamoDB) GetTopInfo(ctx context.Context, key string) (string, error) {
	var topInfo TopInfo
	err := d.db.Table((&TopInfo{}).TableName()).Get("key", key).One(ctx, &topInfo)
	if err != nil {
		return "", err
	}
	return topInfo.Value, nil
}

// BatchGetBGMMetaInfo 批量获取BGM元信息
func (d *dynamoDB) BatchGetBGMMetaInfo(ctx context.Context, ids []int) (map[int]*BGMMetaInfo, error) {
	var keys []dynamo.Keyed
	for _, id := range ids {
		keys = append(keys, dynamo.Keys{id})
	}
	var bgmMetaInfos []*BGMMetaInfo
	err := d.db.Table((&BGMMetaInfo{}).TableName()).Batch("id").Get(keys...).All(ctx, &bgmMetaInfos)
	if err == dynamo.ErrNotFound {
		return map[int]*BGMMetaInfo{}, nil
	}
	if err != nil {
		return nil, err
	}
	bgmMetaInfoMap := make(map[int]*BGMMetaInfo)
	for _, bgmMetaInfo := range bgmMetaInfos {
		bgmMetaInfoMap[bgmMetaInfo.ID] = bgmMetaInfo
	}
	return bgmMetaInfoMap, nil
}

// BatchGetVoiceMetaInfo 批量获取语音元信息
func (d *dynamoDB) BatchGetVoiceMetaInfo(ctx context.Context, ids []int) (map[int]*VoiceMetaInfo, error) {
	var keys []dynamo.Keyed
	for _, id := range ids {
		keys = append(keys, dynamo.Keys{id})
	}
	var voiceMetaInfos []*VoiceMetaInfo
	err := d.db.Table((&VoiceMetaInfo{}).TableName()).Batch("id").Get(keys...).All(ctx, &voiceMetaInfos)
	if err == dynamo.ErrNotFound {
		return map[int]*VoiceMetaInfo{}, nil
	}
	if err != nil {
		return nil, err
	}
	voiceMetaInfoMap := make(map[int]*VoiceMetaInfo)
	for _, voiceMetaInfo := range voiceMetaInfos {
		voiceMetaInfoMap[voiceMetaInfo.ID] = voiceMetaInfo
	}
	return voiceMetaInfoMap, nil
}

// BatchGetStoryViewCount 批量获取故事浏览量
func (d *dynamoDB) BatchGetStoryViewCount(ctx context.Context, ids []int) (map[int]*StoryViewCount, error) {
	var keys []dynamo.Keyed
	for _, id := range ids {
		keys = append(keys, dynamo.Keys{id})
	}

	var storyViewCounts []*StoryViewCount
	err := d.db.Table((&StoryViewCount{}).TableName()).Batch("sekaiId").Get(keys...).All(ctx, &storyViewCounts)
	if err == dynamo.ErrNotFound {
		return map[int]*StoryViewCount{}, nil
	}
	if err != nil {
		return nil, err
	}

	return lo.Associate(storyViewCounts, func(storyViewCount *StoryViewCount) (int, *StoryViewCount) {
		return storyViewCount.StoryID, storyViewCount
	}), nil
}
