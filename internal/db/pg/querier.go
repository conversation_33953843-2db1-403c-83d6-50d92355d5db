// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0

package pg

import (
	"context"
)

type Querier interface {
	// CheckBGMsLikedByUser 检查用户是否点赞了BGM，若点赞了则返回点赞时间
	CheckBGMsLikedByUser(ctx context.Context, arg *CheckBGMsLikedByUserParams) ([]*CheckBGMsLikedByUserRow, error)
	// CountDiscoverPageConfigByType 根据类型统计发现页面配置的总数。仅统计状态为 active 且未被删除的配置。
	CountDiscoverPageConfigByType(ctx context.Context, type_ string) (int64, error)
	CreateBGM(ctx context.Context, arg *CreateBGMParams) (*AiuBgmTable, error)
	CreateVoice(ctx context.Context, arg *CreateVoiceParams) (*AiuVoiceTable, error)
	DeleteUserVoiceLike(ctx context.Context, arg *DeleteUserVoiceLikeParams) (int64, error)
	GetAccents(ctx context.Context, arg *GetAccentsParams) ([]*AiuVoiceAccentTable, error)
	GetAccentsByIDs(ctx context.Context, accentIds []int32) ([]*AiuVoiceAccentTable, error)
	GetAccentsCount(ctx context.Context) (int64, error)
	GetBGMByID(ctx context.Context, id int64) (*AiuBgmTable, error)
	GetBGMByIDs(ctx context.Context, arg *GetBGMByIDsParams) ([]*AiuBgmTable, error)
	GetBGMCategories(ctx context.Context, arg *GetBGMCategoriesParams) ([]*AiuBgmCategory, error)
	GetBGMCategoriesByIDs(ctx context.Context, ids []int32) ([]*AiuBgmCategory, error)
	GetBGMCategoriesCount(ctx context.Context) (int64, error)
	GetBGMCreatedByUser(ctx context.Context, arg *GetBGMCreatedByUserParams) ([]*AiuBgmTable, error)
	GetBGMCreatedByUserCount(ctx context.Context, arg *GetBGMCreatedByUserCountParams) (int64, error)
	// GetBGMsByCategory returns paginated BGMs in a specific category
	GetBGMsByCategory(ctx context.Context, arg *GetBGMsByCategoryParams) ([]*AiuBgmTable, error)
	// GetBGMsByCategoryCount returns the total count of BGMs in a specific category
	GetBGMsByCategoryCount(ctx context.Context, categoryid int32) (int64, error)
	GetBgmByID(ctx context.Context, id int64) (*AiuBgmTable, error)
	GetCharByUniverseIDs(ctx context.Context, universeids []int64) ([]*AiuCharacterTable, error)
	// GetDiscoverPageConfigByType 根据类型查询发现页面配置，支持分页。仅检索状态为 active 且未被删除的配置，按 sort_order 升序，然后按 created_at 降序排列。
	GetDiscoverPageConfigByType(ctx context.Context, arg *GetDiscoverPageConfigByTypeParams) ([]*GetDiscoverPageConfigByTypeRow, error)
	GetEventGifURL(ctx context.Context, eventID []int64) ([]*GetEventGifURLRow, error)
	GetSekaiCategoriesByLabelIDs(ctx context.Context, labelIds []int32) ([]*AiuLabelTable, error)
	GetSekaiIDWithUserLiked(ctx context.Context, arg *GetSekaiIDWithUserLikedParams) ([]*GetSekaiIDWithUserLikedRow, error)
	GetSekaiViewCountByIDs(ctx context.Context, sekaiIds []int64) ([]*GetSekaiViewCountByIDsRow, error)
	// 根据ID列表获取sekai信息，并按照指定的顺序返回
	GetSekaisByIDsWithOrder(ctx context.Context, arg *GetSekaisByIDsWithOrderParams) ([]*AiuSekaiInfoTable, error)
	GetStoriesByIDs(ctx context.Context, arg *GetStoriesByIDsParams) ([]*AiuUniverseTable, error)
	// GetUserLikedBGMs 获取用户点赞的BGM
	GetUserLikedBGMs(ctx context.Context, arg *GetUserLikedBGMsParams) ([]*AiuBgmTable, error)
	// GetUserLikedBGMsCount 获取用户点赞的BGM数量
	GetUserLikedBGMsCount(ctx context.Context, arg *GetUserLikedBGMsCountParams) (int64, error)
	GetVoiceByID(ctx context.Context, voiceID int32) (*AiuVoiceTable, error)
	GetVoiceCategories(ctx context.Context, arg *GetVoiceCategoriesParams) ([]*AiuVoiceCategory, error)
	GetVoiceCategoriesByIDs(ctx context.Context, categoryIds []int32) ([]*AiuVoiceCategory, error)
	GetVoiceCategoriesCount(ctx context.Context, creationtype int32) (int64, error)
	GetVoiceCountByCreatorUserID(ctx context.Context, userid int32) (int64, error)
	GetVoicesByCategory(ctx context.Context, arg *GetVoicesByCategoryParams) ([]*GetVoicesByCategoryRow, error)
	GetVoicesByCategoryCount(ctx context.Context, arg *GetVoicesByCategoryCountParams) (int64, error)
	GetVoicesByCreatorUserID(ctx context.Context, arg *GetVoicesByCreatorUserIDParams) ([]*GetVoicesByCreatorUserIDRow, error)
	GetVoicesByCreatorUserIDCount(ctx context.Context, arg *GetVoicesByCreatorUserIDCountParams) (int64, error)
	GetVoicesLikedByUserID(ctx context.Context, arg *GetVoicesLikedByUserIDParams) ([]*AiuVoiceTable, error)
	GetVoicesLikedByUserIDCount(ctx context.Context, arg *GetVoicesLikedByUserIDCountParams) (int64, error)
	GetVoicesWithLikeStatus(ctx context.Context, arg *GetVoicesWithLikeStatusParams) ([]*GetVoicesWithLikeStatusRow, error)
	SoftDeleteBGM(ctx context.Context, arg *SoftDeleteBGMParams) (*AiuBgmTable, error)
	// UpdateBGM updates the BGM information by ID
	UpdateBGM(ctx context.Context, arg *UpdateBGMParams) (*AiuBgmTable, error)
	UpdateCharTagsByID(ctx context.Context, arg *UpdateCharTagsByIDParams) error
	UpdateVoice(ctx context.Context, arg *UpdateVoiceParams) (*AiuVoiceTable, error)
	UpsertUserVoiceLike(ctx context.Context, arg *UpsertUserVoiceLikeParams) (int64, error)
}

var _ Querier = (*Queries)(nil)
