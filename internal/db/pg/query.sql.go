// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: query.sql

package pg

import (
	"context"
)

const getBgmByID = `-- name: GetBgmByID :one
SELECT id, url, name, "coverUrl", duration, tags, "referenceCount", "creatorUserId", "creationType", enable, public, "createTimeUtc", "premiseIds", categories, nsfw FROM "aiu-bgm-table" WHERE id = $1
`

func (q *Queries) GetBgmByID(ctx context.Context, id int64) (*AiuBgmTable, error) {
	row := q.db.QueryRow(ctx, getBgmByID, id)
	var i AiuBgmTable
	err := row.Scan(
		&i.ID,
		&i.Url,
		&i.Name,
		&i.CoverUrl,
		&i.Duration,
		&i.Tags,
		&i.ReferenceCount,
		&i.CreatorUserId,
		&i.CreationType,
		&i.Enable,
		&i.Public,
		&i.CreateTimeUtc,
		&i.PremiseIds,
		&i.Categories,
		&i.Nsfw,
	)
	return &i, err
}

const getCharByUniverseIDs = `-- name: GetCharByUniverseIDs :many
WITH subquery AS (
    SELECT
        MAX("aiu-character-table"."charId") AS max_charId,
        "aiu-character-table"."charName"
    FROM
        "aiu-character-table"
    WHERE
        "aiu-character-table"."charId" IN (
            SELECT UNNEST("aiu-universe-table"."charIds")
            FROM "aiu-universe-table"
            WHERE "aiu-universe-table"."universeId" IN ($1)
        )
    GROUP BY
        "aiu-character-table"."charName"
)
SELECT
    "aiu-character-table"."charId", "aiu-character-table"."charName", "aiu-character-table"."tagIds", "aiu-character-table".tags, "aiu-character-table"."charDescription", "aiu-character-table"."charAvatarUrl", "aiu-character-table"."createTimeUtc", "aiu-character-table"."creatorUserId", "aiu-character-table"."delFlag", "aiu-character-table"."updateTime", "aiu-character-table"."isFamous", "aiu-character-table".searchable, "aiu-character-table"."creationType", "aiu-character-table".pose, "aiu-character-table".language, "aiu-character-table"."baseCharId", "aiu-character-table".enable, "aiu-character-table".voice, "aiu-character-table"."poseKeywords", "aiu-character-table"."bodyShape", "aiu-character-table".complete, "aiu-character-table"."charOrigin", "aiu-character-table".alias, "aiu-character-table".skins, "aiu-character-table"."referenceImageUrl", "aiu-character-table".weight, "aiu-character-table".public, "aiu-character-table".pronounce, "aiu-character-table"."speakingStyle", "aiu-character-table"."baseCharType", "aiu-character-table"."userCreatedPose", "aiu-character-table"."enableVoice", "aiu-character-table".nsfw, "aiu-character-table"."hashTags", "aiu-character-table".background, "aiu-character-table"."textNsfw", "aiu-character-table"."imageNsfw", "aiu-character-table"."operatorHashTags", "aiu-character-table"."aiHashTags", "aiu-character-table"."faceImage"
FROM
    "aiu-character-table"
WHERE
    "aiu-character-table"."charId" IN (SELECT max_charId FROM subquery)
ORDER BY
    "aiu-character-table"."charId" DESC
`

func (q *Queries) GetCharByUniverseIDs(ctx context.Context, universeids []int64) ([]*AiuCharacterTable, error) {
	rows, err := q.db.Query(ctx, getCharByUniverseIDs, universeids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuCharacterTable
	for rows.Next() {
		var i AiuCharacterTable
		if err := rows.Scan(
			&i.CharId,
			&i.CharName,
			&i.TagIds,
			&i.Tags,
			&i.CharDescription,
			&i.CharAvatarUrl,
			&i.CreateTimeUtc,
			&i.CreatorUserId,
			&i.DelFlag,
			&i.UpdateTime,
			&i.IsFamous,
			&i.Searchable,
			&i.CreationType,
			&i.Pose,
			&i.Language,
			&i.BaseCharId,
			&i.Enable,
			&i.Voice,
			&i.PoseKeywords,
			&i.BodyShape,
			&i.Complete,
			&i.CharOrigin,
			&i.Alias,
			&i.Skins,
			&i.ReferenceImageUrl,
			&i.Weight,
			&i.Public,
			&i.Pronounce,
			&i.SpeakingStyle,
			&i.BaseCharType,
			&i.UserCreatedPose,
			&i.EnableVoice,
			&i.Nsfw,
			&i.HashTags,
			&i.Background,
			&i.TextNsfw,
			&i.ImageNsfw,
			&i.OperatorHashTags,
			&i.AiHashTags,
			&i.FaceImage,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSekaiIDWithUserLiked = `-- name: GetSekaiIDWithUserLiked :many
SELECT
    t1.element_id AS sekai_id,
    COUNT(t1.id) AS like_count,
    (
        SELECT COUNT(aiu_user_like_table.id) > 0
        FROM aiu_user_like_table
        WHERE
            aiu_user_like_table.element_id = t1.element_id
            AND aiu_user_like_table.user_id = $1
            AND aiu_user_like_table.element_type = 'sekai'
            AND aiu_user_like_table.deleted_at IS NULL
    ) AS is_liked
FROM
    aiu_user_like_table AS t1
WHERE
    t1.element_id = ANY($2::int[])
    AND t1.element_type = 'sekai'
    AND t1.deleted_at IS NULL
GROUP BY
    t1.element_id
`

type GetSekaiIDWithUserLikedParams struct {
	UserID   int64   `json:"user_id"`
	SekaiIDs []int32 `json:"sekaiIDs"`
}

type GetSekaiIDWithUserLikedRow struct {
	SekaiID   int64 `json:"sekai_id"`
	LikeCount int64 `json:"like_count"`
	IsLiked   bool  `json:"is_liked"`
}

func (q *Queries) GetSekaiIDWithUserLiked(ctx context.Context, arg *GetSekaiIDWithUserLikedParams) ([]*GetSekaiIDWithUserLikedRow, error) {
	rows, err := q.db.Query(ctx, getSekaiIDWithUserLiked, arg.UserID, arg.SekaiIDs)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*GetSekaiIDWithUserLikedRow
	for rows.Next() {
		var i GetSekaiIDWithUserLikedRow
		if err := rows.Scan(&i.SekaiID, &i.LikeCount, &i.IsLiked); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateCharTagsByID = `-- name: UpdateCharTagsByID :exec
UPDATE "aiu-character-table"
  set tags = $2
WHERE "charId" = $1
`

type UpdateCharTagsByIDParams struct {
	CharId int64  `json:"charId"`
	Tags   []byte `json:"tags"`
}

func (q *Queries) UpdateCharTagsByID(ctx context.Context, arg *UpdateCharTagsByIDParams) error {
	_, err := q.db.Exec(ctx, updateCharTagsByID, arg.CharId, arg.Tags)
	return err
}
