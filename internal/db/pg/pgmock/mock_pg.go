// Code generated by MockGen. DO NOT EDIT.
// Source: querier.go
//
// Generated by this command:
//
//	mockgen -source=querier.go -destination=pgmock/mock_pg.go -package=pgmock
//

// Package pgmock is a generated GoMock package.
package pgmock

import (
	context "context"
	reflect "reflect"

	pg "github.com/sekai-app/sekai-go/internal/db/pg"
	gomock "go.uber.org/mock/gomock"
)

// MockQuerier is a mock of Querier interface.
type MockQuerier struct {
	ctrl     *gomock.Controller
	recorder *MockQuerierMockRecorder
	isgomock struct{}
}

// MockQuerierMockRecorder is the mock recorder for MockQuerier.
type MockQuerierMockRecorder struct {
	mock *MockQuerier
}

// NewMockQuerier creates a new mock instance.
func NewMockQuerier(ctrl *gomock.Controller) *MockQuerier {
	mock := &MockQuerier{ctrl: ctrl}
	mock.recorder = &MockQuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuerier) EXPECT() *MockQuerierMockRecorder {
	return m.recorder
}

// CheckBGMsLikedByUser mocks base method.
func (m *MockQuerier) CheckBGMsLikedByUser(ctx context.Context, arg *pg.CheckBGMsLikedByUserParams) ([]*pg.CheckBGMsLikedByUserRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBGMsLikedByUser", ctx, arg)
	ret0, _ := ret[0].([]*pg.CheckBGMsLikedByUserRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckBGMsLikedByUser indicates an expected call of CheckBGMsLikedByUser.
func (mr *MockQuerierMockRecorder) CheckBGMsLikedByUser(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBGMsLikedByUser", reflect.TypeOf((*MockQuerier)(nil).CheckBGMsLikedByUser), ctx, arg)
}

// CountDiscoverPageConfigByType mocks base method.
func (m *MockQuerier) CountDiscoverPageConfigByType(ctx context.Context, type_ string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountDiscoverPageConfigByType", ctx, type_)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountDiscoverPageConfigByType indicates an expected call of CountDiscoverPageConfigByType.
func (mr *MockQuerierMockRecorder) CountDiscoverPageConfigByType(ctx, type_ any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountDiscoverPageConfigByType", reflect.TypeOf((*MockQuerier)(nil).CountDiscoverPageConfigByType), ctx, type_)
}

// CreateBGM mocks base method.
func (m *MockQuerier) CreateBGM(ctx context.Context, arg *pg.CreateBGMParams) (*pg.AiuBgmTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBGM", ctx, arg)
	ret0, _ := ret[0].(*pg.AiuBgmTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBGM indicates an expected call of CreateBGM.
func (mr *MockQuerierMockRecorder) CreateBGM(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBGM", reflect.TypeOf((*MockQuerier)(nil).CreateBGM), ctx, arg)
}

// CreateVoice mocks base method.
func (m *MockQuerier) CreateVoice(ctx context.Context, arg *pg.CreateVoiceParams) (*pg.AiuVoiceTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateVoice", ctx, arg)
	ret0, _ := ret[0].(*pg.AiuVoiceTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVoice indicates an expected call of CreateVoice.
func (mr *MockQuerierMockRecorder) CreateVoice(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVoice", reflect.TypeOf((*MockQuerier)(nil).CreateVoice), ctx, arg)
}

// DeleteUserVoiceLike mocks base method.
func (m *MockQuerier) DeleteUserVoiceLike(ctx context.Context, arg *pg.DeleteUserVoiceLikeParams) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserVoiceLike", ctx, arg)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteUserVoiceLike indicates an expected call of DeleteUserVoiceLike.
func (mr *MockQuerierMockRecorder) DeleteUserVoiceLike(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserVoiceLike", reflect.TypeOf((*MockQuerier)(nil).DeleteUserVoiceLike), ctx, arg)
}

// GetAccents mocks base method.
func (m *MockQuerier) GetAccents(ctx context.Context, arg *pg.GetAccentsParams) ([]*pg.AiuVoiceAccentTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccents", ctx, arg)
	ret0, _ := ret[0].([]*pg.AiuVoiceAccentTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccents indicates an expected call of GetAccents.
func (mr *MockQuerierMockRecorder) GetAccents(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccents", reflect.TypeOf((*MockQuerier)(nil).GetAccents), ctx, arg)
}

// GetAccentsByIDs mocks base method.
func (m *MockQuerier) GetAccentsByIDs(ctx context.Context, accentIds []int32) ([]*pg.AiuVoiceAccentTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccentsByIDs", ctx, accentIds)
	ret0, _ := ret[0].([]*pg.AiuVoiceAccentTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccentsByIDs indicates an expected call of GetAccentsByIDs.
func (mr *MockQuerierMockRecorder) GetAccentsByIDs(ctx, accentIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccentsByIDs", reflect.TypeOf((*MockQuerier)(nil).GetAccentsByIDs), ctx, accentIds)
}

// GetAccentsCount mocks base method.
func (m *MockQuerier) GetAccentsCount(ctx context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccentsCount", ctx)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccentsCount indicates an expected call of GetAccentsCount.
func (mr *MockQuerierMockRecorder) GetAccentsCount(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccentsCount", reflect.TypeOf((*MockQuerier)(nil).GetAccentsCount), ctx)
}

// GetBGMByID mocks base method.
func (m *MockQuerier) GetBGMByID(ctx context.Context, id int64) (*pg.AiuBgmTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBGMByID", ctx, id)
	ret0, _ := ret[0].(*pg.AiuBgmTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBGMByID indicates an expected call of GetBGMByID.
func (mr *MockQuerierMockRecorder) GetBGMByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBGMByID", reflect.TypeOf((*MockQuerier)(nil).GetBGMByID), ctx, id)
}

// GetBGMByIDs mocks base method.
func (m *MockQuerier) GetBGMByIDs(ctx context.Context, arg *pg.GetBGMByIDsParams) ([]*pg.AiuBgmTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBGMByIDs", ctx, arg)
	ret0, _ := ret[0].([]*pg.AiuBgmTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBGMByIDs indicates an expected call of GetBGMByIDs.
func (mr *MockQuerierMockRecorder) GetBGMByIDs(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBGMByIDs", reflect.TypeOf((*MockQuerier)(nil).GetBGMByIDs), ctx, arg)
}

// GetBGMCategories mocks base method.
func (m *MockQuerier) GetBGMCategories(ctx context.Context, arg *pg.GetBGMCategoriesParams) ([]*pg.AiuBgmCategory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBGMCategories", ctx, arg)
	ret0, _ := ret[0].([]*pg.AiuBgmCategory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBGMCategories indicates an expected call of GetBGMCategories.
func (mr *MockQuerierMockRecorder) GetBGMCategories(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBGMCategories", reflect.TypeOf((*MockQuerier)(nil).GetBGMCategories), ctx, arg)
}

// GetBGMCategoriesByIDs mocks base method.
func (m *MockQuerier) GetBGMCategoriesByIDs(ctx context.Context, ids []int32) ([]*pg.AiuBgmCategory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBGMCategoriesByIDs", ctx, ids)
	ret0, _ := ret[0].([]*pg.AiuBgmCategory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBGMCategoriesByIDs indicates an expected call of GetBGMCategoriesByIDs.
func (mr *MockQuerierMockRecorder) GetBGMCategoriesByIDs(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBGMCategoriesByIDs", reflect.TypeOf((*MockQuerier)(nil).GetBGMCategoriesByIDs), ctx, ids)
}

// GetBGMCategoriesCount mocks base method.
func (m *MockQuerier) GetBGMCategoriesCount(ctx context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBGMCategoriesCount", ctx)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBGMCategoriesCount indicates an expected call of GetBGMCategoriesCount.
func (mr *MockQuerierMockRecorder) GetBGMCategoriesCount(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBGMCategoriesCount", reflect.TypeOf((*MockQuerier)(nil).GetBGMCategoriesCount), ctx)
}

// GetBGMCreatedByUser mocks base method.
func (m *MockQuerier) GetBGMCreatedByUser(ctx context.Context, arg *pg.GetBGMCreatedByUserParams) ([]*pg.AiuBgmTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBGMCreatedByUser", ctx, arg)
	ret0, _ := ret[0].([]*pg.AiuBgmTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBGMCreatedByUser indicates an expected call of GetBGMCreatedByUser.
func (mr *MockQuerierMockRecorder) GetBGMCreatedByUser(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBGMCreatedByUser", reflect.TypeOf((*MockQuerier)(nil).GetBGMCreatedByUser), ctx, arg)
}

// GetBGMCreatedByUserCount mocks base method.
func (m *MockQuerier) GetBGMCreatedByUserCount(ctx context.Context, arg *pg.GetBGMCreatedByUserCountParams) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBGMCreatedByUserCount", ctx, arg)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBGMCreatedByUserCount indicates an expected call of GetBGMCreatedByUserCount.
func (mr *MockQuerierMockRecorder) GetBGMCreatedByUserCount(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBGMCreatedByUserCount", reflect.TypeOf((*MockQuerier)(nil).GetBGMCreatedByUserCount), ctx, arg)
}

// GetBGMsByCategory mocks base method.
func (m *MockQuerier) GetBGMsByCategory(ctx context.Context, arg *pg.GetBGMsByCategoryParams) ([]*pg.AiuBgmTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBGMsByCategory", ctx, arg)
	ret0, _ := ret[0].([]*pg.AiuBgmTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBGMsByCategory indicates an expected call of GetBGMsByCategory.
func (mr *MockQuerierMockRecorder) GetBGMsByCategory(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBGMsByCategory", reflect.TypeOf((*MockQuerier)(nil).GetBGMsByCategory), ctx, arg)
}

// GetBGMsByCategoryCount mocks base method.
func (m *MockQuerier) GetBGMsByCategoryCount(ctx context.Context, categoryid int32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBGMsByCategoryCount", ctx, categoryid)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBGMsByCategoryCount indicates an expected call of GetBGMsByCategoryCount.
func (mr *MockQuerierMockRecorder) GetBGMsByCategoryCount(ctx, categoryid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBGMsByCategoryCount", reflect.TypeOf((*MockQuerier)(nil).GetBGMsByCategoryCount), ctx, categoryid)
}

// GetBgmByID mocks base method.
func (m *MockQuerier) GetBgmByID(ctx context.Context, id int64) (*pg.AiuBgmTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBgmByID", ctx, id)
	ret0, _ := ret[0].(*pg.AiuBgmTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBgmByID indicates an expected call of GetBgmByID.
func (mr *MockQuerierMockRecorder) GetBgmByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBgmByID", reflect.TypeOf((*MockQuerier)(nil).GetBgmByID), ctx, id)
}

// GetCharByUniverseIDs mocks base method.
func (m *MockQuerier) GetCharByUniverseIDs(ctx context.Context, universeids []int64) ([]*pg.AiuCharacterTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCharByUniverseIDs", ctx, universeids)
	ret0, _ := ret[0].([]*pg.AiuCharacterTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCharByUniverseIDs indicates an expected call of GetCharByUniverseIDs.
func (mr *MockQuerierMockRecorder) GetCharByUniverseIDs(ctx, universeids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCharByUniverseIDs", reflect.TypeOf((*MockQuerier)(nil).GetCharByUniverseIDs), ctx, universeids)
}

// GetDiscoverPageConfigByType mocks base method.
func (m *MockQuerier) GetDiscoverPageConfigByType(ctx context.Context, arg *pg.GetDiscoverPageConfigByTypeParams) ([]*pg.GetDiscoverPageConfigByTypeRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDiscoverPageConfigByType", ctx, arg)
	ret0, _ := ret[0].([]*pg.GetDiscoverPageConfigByTypeRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDiscoverPageConfigByType indicates an expected call of GetDiscoverPageConfigByType.
func (mr *MockQuerierMockRecorder) GetDiscoverPageConfigByType(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiscoverPageConfigByType", reflect.TypeOf((*MockQuerier)(nil).GetDiscoverPageConfigByType), ctx, arg)
}

// GetEventGifURL mocks base method.
func (m *MockQuerier) GetEventGifURL(ctx context.Context, eventID []int64) ([]*pg.GetEventGifURLRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEventGifURL", ctx, eventID)
	ret0, _ := ret[0].([]*pg.GetEventGifURLRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEventGifURL indicates an expected call of GetEventGifURL.
func (mr *MockQuerierMockRecorder) GetEventGifURL(ctx, eventID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEventGifURL", reflect.TypeOf((*MockQuerier)(nil).GetEventGifURL), ctx, eventID)
}

// GetSekaiCategoriesByLabelIDs mocks base method.
func (m *MockQuerier) GetSekaiCategoriesByLabelIDs(ctx context.Context, labelIds []int32) ([]*pg.AiuLabelTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSekaiCategoriesByLabelIDs", ctx, labelIds)
	ret0, _ := ret[0].([]*pg.AiuLabelTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSekaiCategoriesByLabelIDs indicates an expected call of GetSekaiCategoriesByLabelIDs.
func (mr *MockQuerierMockRecorder) GetSekaiCategoriesByLabelIDs(ctx, labelIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSekaiCategoriesByLabelIDs", reflect.TypeOf((*MockQuerier)(nil).GetSekaiCategoriesByLabelIDs), ctx, labelIds)
}

// GetSekaiIDWithUserLiked mocks base method.
func (m *MockQuerier) GetSekaiIDWithUserLiked(ctx context.Context, arg *pg.GetSekaiIDWithUserLikedParams) ([]*pg.GetSekaiIDWithUserLikedRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSekaiIDWithUserLiked", ctx, arg)
	ret0, _ := ret[0].([]*pg.GetSekaiIDWithUserLikedRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSekaiIDWithUserLiked indicates an expected call of GetSekaiIDWithUserLiked.
func (mr *MockQuerierMockRecorder) GetSekaiIDWithUserLiked(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSekaiIDWithUserLiked", reflect.TypeOf((*MockQuerier)(nil).GetSekaiIDWithUserLiked), ctx, arg)
}

// GetSekaiViewCountByIDs mocks base method.
func (m *MockQuerier) GetSekaiViewCountByIDs(ctx context.Context, sekaiIds []int64) ([]*pg.GetSekaiViewCountByIDsRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSekaiViewCountByIDs", ctx, sekaiIds)
	ret0, _ := ret[0].([]*pg.GetSekaiViewCountByIDsRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSekaiViewCountByIDs indicates an expected call of GetSekaiViewCountByIDs.
func (mr *MockQuerierMockRecorder) GetSekaiViewCountByIDs(ctx, sekaiIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSekaiViewCountByIDs", reflect.TypeOf((*MockQuerier)(nil).GetSekaiViewCountByIDs), ctx, sekaiIds)
}

// GetSekaisByIDsWithOrder mocks base method.
func (m *MockQuerier) GetSekaisByIDsWithOrder(ctx context.Context, arg *pg.GetSekaisByIDsWithOrderParams) ([]*pg.AiuSekaiInfoTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSekaisByIDsWithOrder", ctx, arg)
	ret0, _ := ret[0].([]*pg.AiuSekaiInfoTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSekaisByIDsWithOrder indicates an expected call of GetSekaisByIDsWithOrder.
func (mr *MockQuerierMockRecorder) GetSekaisByIDsWithOrder(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSekaisByIDsWithOrder", reflect.TypeOf((*MockQuerier)(nil).GetSekaisByIDsWithOrder), ctx, arg)
}

// GetStoriesByIDs mocks base method.
func (m *MockQuerier) GetStoriesByIDs(ctx context.Context, arg *pg.GetStoriesByIDsParams) ([]*pg.AiuUniverseTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStoriesByIDs", ctx, arg)
	ret0, _ := ret[0].([]*pg.AiuUniverseTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStoriesByIDs indicates an expected call of GetStoriesByIDs.
func (mr *MockQuerierMockRecorder) GetStoriesByIDs(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStoriesByIDs", reflect.TypeOf((*MockQuerier)(nil).GetStoriesByIDs), ctx, arg)
}

// GetUserLikedBGMs mocks base method.
func (m *MockQuerier) GetUserLikedBGMs(ctx context.Context, arg *pg.GetUserLikedBGMsParams) ([]*pg.AiuBgmTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLikedBGMs", ctx, arg)
	ret0, _ := ret[0].([]*pg.AiuBgmTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserLikedBGMs indicates an expected call of GetUserLikedBGMs.
func (mr *MockQuerierMockRecorder) GetUserLikedBGMs(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLikedBGMs", reflect.TypeOf((*MockQuerier)(nil).GetUserLikedBGMs), ctx, arg)
}

// GetUserLikedBGMsCount mocks base method.
func (m *MockQuerier) GetUserLikedBGMsCount(ctx context.Context, arg *pg.GetUserLikedBGMsCountParams) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLikedBGMsCount", ctx, arg)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserLikedBGMsCount indicates an expected call of GetUserLikedBGMsCount.
func (mr *MockQuerierMockRecorder) GetUserLikedBGMsCount(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLikedBGMsCount", reflect.TypeOf((*MockQuerier)(nil).GetUserLikedBGMsCount), ctx, arg)
}

// GetVoiceByID mocks base method.
func (m *MockQuerier) GetVoiceByID(ctx context.Context, voiceID int32) (*pg.AiuVoiceTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoiceByID", ctx, voiceID)
	ret0, _ := ret[0].(*pg.AiuVoiceTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoiceByID indicates an expected call of GetVoiceByID.
func (mr *MockQuerierMockRecorder) GetVoiceByID(ctx, voiceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoiceByID", reflect.TypeOf((*MockQuerier)(nil).GetVoiceByID), ctx, voiceID)
}

// GetVoiceCategories mocks base method.
func (m *MockQuerier) GetVoiceCategories(ctx context.Context, arg *pg.GetVoiceCategoriesParams) ([]*pg.AiuVoiceCategory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoiceCategories", ctx, arg)
	ret0, _ := ret[0].([]*pg.AiuVoiceCategory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoiceCategories indicates an expected call of GetVoiceCategories.
func (mr *MockQuerierMockRecorder) GetVoiceCategories(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoiceCategories", reflect.TypeOf((*MockQuerier)(nil).GetVoiceCategories), ctx, arg)
}

// GetVoiceCategoriesByIDs mocks base method.
func (m *MockQuerier) GetVoiceCategoriesByIDs(ctx context.Context, categoryIds []int32) ([]*pg.AiuVoiceCategory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoiceCategoriesByIDs", ctx, categoryIds)
	ret0, _ := ret[0].([]*pg.AiuVoiceCategory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoiceCategoriesByIDs indicates an expected call of GetVoiceCategoriesByIDs.
func (mr *MockQuerierMockRecorder) GetVoiceCategoriesByIDs(ctx, categoryIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoiceCategoriesByIDs", reflect.TypeOf((*MockQuerier)(nil).GetVoiceCategoriesByIDs), ctx, categoryIds)
}

// GetVoiceCategoriesCount mocks base method.
func (m *MockQuerier) GetVoiceCategoriesCount(ctx context.Context, creationtype int32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoiceCategoriesCount", ctx, creationtype)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoiceCategoriesCount indicates an expected call of GetVoiceCategoriesCount.
func (mr *MockQuerierMockRecorder) GetVoiceCategoriesCount(ctx, creationtype any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoiceCategoriesCount", reflect.TypeOf((*MockQuerier)(nil).GetVoiceCategoriesCount), ctx, creationtype)
}

// GetVoiceCountByCreatorUserID mocks base method.
func (m *MockQuerier) GetVoiceCountByCreatorUserID(ctx context.Context, userid int32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoiceCountByCreatorUserID", ctx, userid)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoiceCountByCreatorUserID indicates an expected call of GetVoiceCountByCreatorUserID.
func (mr *MockQuerierMockRecorder) GetVoiceCountByCreatorUserID(ctx, userid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoiceCountByCreatorUserID", reflect.TypeOf((*MockQuerier)(nil).GetVoiceCountByCreatorUserID), ctx, userid)
}

// GetVoicesByCategory mocks base method.
func (m *MockQuerier) GetVoicesByCategory(ctx context.Context, arg *pg.GetVoicesByCategoryParams) ([]*pg.GetVoicesByCategoryRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoicesByCategory", ctx, arg)
	ret0, _ := ret[0].([]*pg.GetVoicesByCategoryRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoicesByCategory indicates an expected call of GetVoicesByCategory.
func (mr *MockQuerierMockRecorder) GetVoicesByCategory(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoicesByCategory", reflect.TypeOf((*MockQuerier)(nil).GetVoicesByCategory), ctx, arg)
}

// GetVoicesByCategoryCount mocks base method.
func (m *MockQuerier) GetVoicesByCategoryCount(ctx context.Context, arg *pg.GetVoicesByCategoryCountParams) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoicesByCategoryCount", ctx, arg)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoicesByCategoryCount indicates an expected call of GetVoicesByCategoryCount.
func (mr *MockQuerierMockRecorder) GetVoicesByCategoryCount(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoicesByCategoryCount", reflect.TypeOf((*MockQuerier)(nil).GetVoicesByCategoryCount), ctx, arg)
}

// GetVoicesByCreatorUserID mocks base method.
func (m *MockQuerier) GetVoicesByCreatorUserID(ctx context.Context, arg *pg.GetVoicesByCreatorUserIDParams) ([]*pg.GetVoicesByCreatorUserIDRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoicesByCreatorUserID", ctx, arg)
	ret0, _ := ret[0].([]*pg.GetVoicesByCreatorUserIDRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoicesByCreatorUserID indicates an expected call of GetVoicesByCreatorUserID.
func (mr *MockQuerierMockRecorder) GetVoicesByCreatorUserID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoicesByCreatorUserID", reflect.TypeOf((*MockQuerier)(nil).GetVoicesByCreatorUserID), ctx, arg)
}

// GetVoicesByCreatorUserIDCount mocks base method.
func (m *MockQuerier) GetVoicesByCreatorUserIDCount(ctx context.Context, arg *pg.GetVoicesByCreatorUserIDCountParams) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoicesByCreatorUserIDCount", ctx, arg)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoicesByCreatorUserIDCount indicates an expected call of GetVoicesByCreatorUserIDCount.
func (mr *MockQuerierMockRecorder) GetVoicesByCreatorUserIDCount(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoicesByCreatorUserIDCount", reflect.TypeOf((*MockQuerier)(nil).GetVoicesByCreatorUserIDCount), ctx, arg)
}

// GetVoicesLikedByUserID mocks base method.
func (m *MockQuerier) GetVoicesLikedByUserID(ctx context.Context, arg *pg.GetVoicesLikedByUserIDParams) ([]*pg.AiuVoiceTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoicesLikedByUserID", ctx, arg)
	ret0, _ := ret[0].([]*pg.AiuVoiceTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoicesLikedByUserID indicates an expected call of GetVoicesLikedByUserID.
func (mr *MockQuerierMockRecorder) GetVoicesLikedByUserID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoicesLikedByUserID", reflect.TypeOf((*MockQuerier)(nil).GetVoicesLikedByUserID), ctx, arg)
}

// GetVoicesLikedByUserIDCount mocks base method.
func (m *MockQuerier) GetVoicesLikedByUserIDCount(ctx context.Context, arg *pg.GetVoicesLikedByUserIDCountParams) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoicesLikedByUserIDCount", ctx, arg)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoicesLikedByUserIDCount indicates an expected call of GetVoicesLikedByUserIDCount.
func (mr *MockQuerierMockRecorder) GetVoicesLikedByUserIDCount(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoicesLikedByUserIDCount", reflect.TypeOf((*MockQuerier)(nil).GetVoicesLikedByUserIDCount), ctx, arg)
}

// GetVoicesWithLikeStatus mocks base method.
func (m *MockQuerier) GetVoicesWithLikeStatus(ctx context.Context, arg *pg.GetVoicesWithLikeStatusParams) ([]*pg.GetVoicesWithLikeStatusRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVoicesWithLikeStatus", ctx, arg)
	ret0, _ := ret[0].([]*pg.GetVoicesWithLikeStatusRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVoicesWithLikeStatus indicates an expected call of GetVoicesWithLikeStatus.
func (mr *MockQuerierMockRecorder) GetVoicesWithLikeStatus(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVoicesWithLikeStatus", reflect.TypeOf((*MockQuerier)(nil).GetVoicesWithLikeStatus), ctx, arg)
}

// SoftDeleteBGM mocks base method.
func (m *MockQuerier) SoftDeleteBGM(ctx context.Context, arg *pg.SoftDeleteBGMParams) (*pg.AiuBgmTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SoftDeleteBGM", ctx, arg)
	ret0, _ := ret[0].(*pg.AiuBgmTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SoftDeleteBGM indicates an expected call of SoftDeleteBGM.
func (mr *MockQuerierMockRecorder) SoftDeleteBGM(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SoftDeleteBGM", reflect.TypeOf((*MockQuerier)(nil).SoftDeleteBGM), ctx, arg)
}

// UpdateBGM mocks base method.
func (m *MockQuerier) UpdateBGM(ctx context.Context, arg *pg.UpdateBGMParams) (*pg.AiuBgmTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBGM", ctx, arg)
	ret0, _ := ret[0].(*pg.AiuBgmTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBGM indicates an expected call of UpdateBGM.
func (mr *MockQuerierMockRecorder) UpdateBGM(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBGM", reflect.TypeOf((*MockQuerier)(nil).UpdateBGM), ctx, arg)
}

// UpdateCharTagsByID mocks base method.
func (m *MockQuerier) UpdateCharTagsByID(ctx context.Context, arg *pg.UpdateCharTagsByIDParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCharTagsByID", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCharTagsByID indicates an expected call of UpdateCharTagsByID.
func (mr *MockQuerierMockRecorder) UpdateCharTagsByID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCharTagsByID", reflect.TypeOf((*MockQuerier)(nil).UpdateCharTagsByID), ctx, arg)
}

// UpdateVoice mocks base method.
func (m *MockQuerier) UpdateVoice(ctx context.Context, arg *pg.UpdateVoiceParams) (*pg.AiuVoiceTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateVoice", ctx, arg)
	ret0, _ := ret[0].(*pg.AiuVoiceTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateVoice indicates an expected call of UpdateVoice.
func (mr *MockQuerierMockRecorder) UpdateVoice(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVoice", reflect.TypeOf((*MockQuerier)(nil).UpdateVoice), ctx, arg)
}

// UpsertUserVoiceLike mocks base method.
func (m *MockQuerier) UpsertUserVoiceLike(ctx context.Context, arg *pg.UpsertUserVoiceLikeParams) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertUserVoiceLike", ctx, arg)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertUserVoiceLike indicates an expected call of UpsertUserVoiceLike.
func (mr *MockQuerierMockRecorder) UpsertUserVoiceLike(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertUserVoiceLike", reflect.TypeOf((*MockQuerier)(nil).UpsertUserVoiceLike), ctx, arg)
}
