-- name: GetStoriesByIDs :many
SELECT 
    *
FROM 
    "aiu-universe-table"
WHERE 
    "universeId" = ANY(@story_ids::bigint[])
    AND (public = @is_public OR NOT @filter_by_public::boolean)
    AND "isDelete" = false
ORDER BY 
    array_position(@story_ids::bigint[], "universeId");

-- name: GetEventGifURL :many
SELECT "eventId", "gifUrl" FROM "aiu-event-table" WHERE "eventId" = ANY(@event_id::bigint[]);