-- name: GetSekaisByIDsWithOrder :many
-- 根据ID列表获取sekai信息，并按照指定的顺序返回
SELECT 
    s.*
FROM aiu_sekai_info_table s
JOIN UNNEST(@sekai_ids::bigint[]) WITH ORDINALITY AS ids(id, ordering) ON s.sekai_id = ids.id
WHERE 
    (s.published = @published OR NOT @filter_by_published::boolean)
    AND (s.is_public = @is_public OR NOT @filter_by_public::boolean)
    AND s.deleted_at IS NULL
ORDER BY ids.ordering;

-- name: GetSekaiViewCountByIDs :many
SELECT sekai_id, count(sekai_id) as view_count
FROM aiu_sekai_session_table
WHERE sekai_id = ANY(@sekai_ids::bigint[])
GROUP BY sekai_id;

-- name: GetSekaiCategoriesByLabelIDs :many
SELECT * FROM aiu_label_table
WHERE id = ANY(@label_ids::int[]);
