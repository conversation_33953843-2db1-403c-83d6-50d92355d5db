-- name: GetDiscoverPageConfigByType :many
-- GetDiscoverPageConfigByType 根据类型查询发现页面配置，支持分页。仅检索状态为 active 且未被删除的配置，按 sort_order 升序，然后按 created_at 降序排列。
SELECT
    id,
    type,
    data,
    status,
    sort_order,
    created_at,
    updated_at
FROM
    aiu_discover_page_config
WHERE
    type = sqlc.arg('type')
  AND status = 'active'
  AND is_deleted = false
ORDER BY
    sort_order ASC,
    created_at DESC
LIMIT sqlc.arg('pageSize')::int
OFFSET (sqlc.arg('page')::int - 1) * sqlc.arg('pageSize')::int;

-- name: CountDiscoverPageConfigByType :one
-- CountDiscoverPageConfigByType 根据类型统计发现页面配置的总数。仅统计状态为 active 且未被删除的配置。
SELECT
    count(*)
FROM
    aiu_discover_page_config
WHERE
    type = sqlc.arg('type')
  AND status = 'active'
  AND is_deleted = false;