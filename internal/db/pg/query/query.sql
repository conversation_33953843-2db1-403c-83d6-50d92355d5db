-- name: GetCharByUniverseIDs :many
WITH subquery AS (
    SELECT
        MAX("aiu-character-table"."charId") AS max_charId,
        "aiu-character-table"."charName"
    FROM
        "aiu-character-table"
    WHERE
        "aiu-character-table"."charId" IN (
            SELECT UNNEST("aiu-universe-table"."charIds")
            FROM "aiu-universe-table"
            WHERE "aiu-universe-table"."universeId" IN (sqlc.slice('universeIDs'))
        )
    GROUP BY
        "aiu-character-table"."charName"
)
SELECT
    "aiu-character-table".*
FROM
    "aiu-character-table"
WHERE
    "aiu-character-table"."charId" IN (SELECT max_charId FROM subquery)
ORDER BY
    "aiu-character-table"."charId" DESC;


-- name: GetSekaiIDWithUserLiked :many
SELECT
    t1.element_id AS sekai_id,
    COUNT(t1.id) AS like_count,
    (
        SELECT COUNT(aiu_user_like_table.id) > 0
        FROM aiu_user_like_table
        WHERE
            aiu_user_like_table.element_id = t1.element_id
            AND aiu_user_like_table.user_id = $1
            AND aiu_user_like_table.element_type = 'sekai'
            AND aiu_user_like_table.deleted_at IS NULL
    ) AS is_liked
FROM
    aiu_user_like_table AS t1
WHERE
    t1.element_id = ANY(sqlc.arg('sekaiIDs')::int[])
    AND t1.element_type = 'sekai'
    AND t1.deleted_at IS NULL
GROUP BY
    t1.element_id;

-- name: UpdateCharTagsByID :exec
UPDATE "aiu-character-table"
  set tags = $2
WHERE "charId" = $1;

-- name: GetBgmByID :one
SELECT * FROM "aiu-bgm-table" WHERE id = $1;