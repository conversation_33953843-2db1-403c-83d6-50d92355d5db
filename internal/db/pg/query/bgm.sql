-- name: CreateBGM :one
INSERT INTO "aiu-bgm-table" (
    url,
    name,
    "coverUrl",
    duration,
    tags,
    "referenceCount",
    "creatorUserId",
    "creationType",
    enable,
    public,
    "createTimeUtc",
    "premiseIds",
    categories,
    nsfw
) VALUES (
    $1,
    $2,
    $3,
    $4,
    $5,
    $6,
    $7,
    $8,
    $9,
    $10,
    $11,
    $12,
    $13,
    $14
) RETURNING *;

-- name: SoftDeleteBGM :one
UPDATE "aiu-bgm-table"
SET
    enable = false
WHERE
    id = $1 AND "creatorUserId" = $2 AND enable = true
RETURNING *;

-- name: GetBGMByID :one
SELECT * FROM "aiu-bgm-table"
WHERE
    id = $1;

-- name: GetBGMCreatedByUserCount :one
SELECT COUNT(*) FROM "aiu-bgm-table"
WHERE
    "creatorUserId" = sqlc.arg('queryUserId')::int
    AND enable = true
    AND (public = true OR NOT @filter_by_public::boolean);

-- name: GetBGMCreatedByUser :many
SELECT * FROM "aiu-bgm-table"
WHERE
    "creatorUserId" = sqlc.arg('queryUserId')::int
    AND enable = true
    AND (public = true OR NOT @filter_by_public::boolean)
ORDER BY
    "createTimeUtc" DESC
LIMIT sqlc.arg('pageSize')::int 
OFFSET (sqlc.arg('page')::int - 1) * sqlc.arg('pageSize')::int;

-- name: GetBGMByIDs :many
SELECT * FROM "aiu-bgm-table"
WHERE
    id = ANY(@ids::int[])
    AND (nsfw = @nsfw OR NOT @filter_by_nsfw::boolean)
    AND (enable = @enable OR NOT @filter_by_enable::boolean)
    AND (public = @public OR NOT @filter_by_public::boolean);

-- name: GetBGMCategoriesByIDs :many
SELECT * FROM aiu_bgm_category 
WHERE id = ANY(sqlc.arg('IDs')::int[]);

-- name: GetBGMCategoriesCount :one
SELECT COUNT(*) FROM aiu_bgm_category;

-- name: GetBGMCategories :many
SELECT * FROM aiu_bgm_category 
ORDER BY name ASC 
OFFSET (sqlc.arg('Page')::int - 1) * sqlc.arg('PageSize')::int
LIMIT sqlc.arg('PageSize')::int;

-- name: GetUserLikedBGMsCount :one
-- GetUserLikedBGMsCount 获取用户点赞的BGM数量
SELECT COUNT(*)
FROM "aiu-bgm-table" bgm
JOIN aiu_user_like_bgm ON 
    aiu_user_like_bgm.user_id = sqlc.arg('userID')::int 
    AND aiu_user_like_bgm.bgm_id = bgm.id 
    AND aiu_user_like_bgm.is_delete = false
WHERE 
    -- 如果查询用户ID与当前用户ID不同，需要检查public字段
    bgm.public = true OR NOT @filter_by_public::boolean;

-- name: GetUserLikedBGMs :many
-- GetUserLikedBGMs 获取用户点赞的BGM
SELECT bgm.*
FROM "aiu-bgm-table" bgm
JOIN aiu_user_like_bgm ON 
    aiu_user_like_bgm.user_id = sqlc.arg('userID')::int 
    AND aiu_user_like_bgm.bgm_id = bgm.id 
    AND aiu_user_like_bgm.is_delete = false
WHERE 
    -- 如果查询用户ID与当前用户ID不同，需要检查public字段
    bgm.public = true OR NOT @filter_by_public::boolean
ORDER BY aiu_user_like_bgm.created_at DESC
LIMIT sqlc.arg('pageSize')::int
OFFSET (sqlc.arg('page')::int - 1) * sqlc.arg('pageSize')::int;

-- name: CheckBGMsLikedByUser :many
-- CheckBGMsLikedByUser 检查用户是否点赞了BGM，若点赞了则返回点赞时间
SELECT bgm_id, created_at FROM aiu_user_like_bgm
WHERE
    user_id = sqlc.arg('userID')::int
    AND bgm_id = ANY(sqlc.arg('bgmIDs')::int[])
    AND is_delete = false;

-- name: UpdateBGM :one
-- UpdateBGM updates the BGM information by ID
UPDATE "aiu-bgm-table"
SET
    name = CASE 
        WHEN sqlc.narg('name')::text IS NOT NULL THEN sqlc.narg('name')::text 
        ELSE name 
    END,
    url = CASE 
        WHEN sqlc.narg('URL')::text IS NOT NULL THEN sqlc.narg('URL')::text 
        ELSE url 
    END,
    "coverUrl" = CASE 
        WHEN sqlc.narg('coverURL')::text IS NOT NULL THEN sqlc.narg('coverURL')::text 
        ELSE "coverUrl" 
    END,
    duration = CASE 
        WHEN sqlc.narg('duration')::integer IS NOT NULL THEN sqlc.narg('duration')::integer 
        ELSE duration 
    END,
    tags = CASE 
        WHEN sqlc.narg('tags')::text[] IS NOT NULL THEN sqlc.narg('tags')::text[] 
        ELSE tags 
    END,
    public = CASE 
        WHEN sqlc.narg('public')::boolean IS NOT NULL THEN sqlc.narg('public')::boolean 
        ELSE public 
    END,
    categories = CASE 
        WHEN sqlc.narg('categories')::integer[] IS NOT NULL THEN sqlc.narg('categories')::integer[] 
        ELSE categories 
    END,
    "premiseIds" = CASE 
        WHEN sqlc.narg('premiseIDs')::text[] IS NOT NULL THEN sqlc.narg('premiseIDs')::text[] 
        ELSE "premiseIds" 
    END,
    "createTimeUtc" = CASE 
        WHEN sqlc.narg('createTimeUTC')::bigint IS NOT NULL THEN sqlc.narg('createTimeUTC')::bigint 
        ELSE "createTimeUtc" 
    END
WHERE
    id = sqlc.arg('ID')
    AND "creatorUserId" = sqlc.arg('creatorUserID')
    AND enable = true
RETURNING *;

-- name: GetBGMsByCategoryCount :one
-- GetBGMsByCategoryCount returns the total count of BGMs in a specific category
SELECT COUNT(*)
FROM "aiu-bgm-table"
WHERE
    sqlc.arg('categoryID')::int = ANY(categories)
    AND enable = true
    AND public = true;

-- name: GetBGMsByCategory :many
-- GetBGMsByCategory returns paginated BGMs in a specific category
SELECT *
FROM "aiu-bgm-table"
WHERE
    sqlc.arg('categoryID')::int = ANY(categories)
    AND enable = true
    AND public = true
ORDER BY "createTimeUtc" DESC
LIMIT sqlc.arg('pageSize')::int
OFFSET (sqlc.arg('page')::int - 1) * sqlc.arg('pageSize')::int;
