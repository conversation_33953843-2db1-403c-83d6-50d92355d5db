-- name: GetAccents :many
SELECT * FROM aiu_voice_accent_table
WHERE is_delete = false
ORDER BY id ASC
LIMIT sqlc.arg('pageSize')::int
OFFSET (sqlc.arg('page')::int - 1) * sqlc.arg('pageSize')::int;

-- name: GetAccentsCount :one
SELECT COUNT(*) FROM aiu_voice_accent_table
WHERE is_delete = false;

-- name: GetAccentsByIDs :many
SELECT * FROM aiu_voice_accent_table
WHERE id = ANY(sqlc.arg('accent_ids')::int[]) AND
    is_delete = false;

-- name: GetVoiceCategories :many
SELECT * FROM aiu_voice_category
WHERE deleted_at IS NULL AND creation_type = sqlc.arg('creationType')::int
ORDER BY weight DESC
LIMIT sqlc.arg('pageSize')::int
OFFSET (sqlc.arg('page')::int - 1) * sqlc.arg('pageSize')::int;

-- name: GetVoiceCategoriesCount :one
SELECT COUNT(*) FROM aiu_voice_category
WHERE deleted_at IS NULL AND creation_type = sqlc.arg('creationType')::int;

-- name: GetVoicesWithLikeStatus :many
SELECT 
    sqlc.embed(v),
    CASE WHEN uvl.id IS NOT NULL THEN TRUE ELSE FALSE END AS is_liked
FROM 
    "aiu-voice-table" v
LEFT JOIN 
    aiu_user_like_voice uvl ON 
        uvl.user_id = sqlc.arg('user_id') AND 
        uvl.voice_id = v."voiceId" AND 
        uvl.deleted_at IS NULL
WHERE 
    v."voiceId" = ANY(sqlc.arg('voice_ids')::int[]) AND
    (NOT @filter_by_display_enable::boolean OR v."displayEnable" = TRUE) AND
    (NOT @filter_by_enable::boolean OR v.enable = TRUE)
ORDER BY 
    array_position(sqlc.arg('voice_ids')::int[], v."voiceId");

-- name: GetVoiceCategoriesByIDs :many
SELECT * FROM aiu_voice_category
WHERE id = ANY(sqlc.arg('category_ids')::int[]) AND
    deleted_at IS NULL
ORDER BY weight DESC;


-- name: CreateVoice :one
INSERT INTO "aiu-voice-table" (
    "displayName",
    "sampleUrl",
    "recordUrl",
    "recordText",
    "processedRecordUrl",
    "displayEnable",
    "charName",
    "labsTag",
    "labsId",
    "rvcTag",
    "rvcId",
    "rvcTranspose",
    "creatorUserId",
    "creationType",
    enable,
    "createTime",
    duration,
    "accentId"
) VALUES (
    sqlc.arg('displayName')::text,
    sqlc.arg('sampleUrl')::text,
    sqlc.arg('recordUrl')::text,
    sqlc.arg('recordText')::text,
    sqlc.arg('processedRecordUrl')::text,
    sqlc.arg('displayEnable')::boolean,
    sqlc.arg('charName')::text,
    sqlc.arg('labsTag')::text[],
    sqlc.arg('labsId')::text,
    sqlc.arg('rvcTag')::text[],
    sqlc.arg('rvcId')::text,
    sqlc.arg('rvcTranspose')::int,
    sqlc.arg('creatorUserId')::bigint,
    sqlc.arg('creationType')::int,
    false,
    sqlc.arg('createTime')::bigint,
    sqlc.arg('duration')::int,
    sqlc.arg('accentId')::bigint
)
RETURNING *;


-- name: GetVoiceCountByCreatorUserID :one
SELECT COUNT(*) FROM "aiu-voice-table" 
WHERE "creatorUserId"=sqlc.arg('userID')::int AND enable=true;

-- name: GetVoicesByCategoryCount :one
SELECT COUNT(*) FROM "aiu-voice-table"
WHERE (
    -- 官方音乐
    (categories @> ARRAY[sqlc.arg('category_id')::int] AND 
     "creationType" = 2 AND 
     "displayEnable" = true)
    OR
    -- 用户创建且公开的
    (categories @> ARRAY[sqlc.arg('category_id')::int] AND 
     "creationType" = 0 AND 
     "displayEnable" = true AND 
     enable = true)
    OR
    -- 用户私有的 (当user_id不为0时)
    (CASE 
        WHEN sqlc.arg('user_id')::bigint != 0 THEN
            categories @> ARRAY[sqlc.arg('category_id')::int] AND 
            "creationType" = 0 AND 
            "creatorUserId" = sqlc.arg('user_id')::bigint AND 
            enable = true
        ELSE false
    END)
);

-- name: GetVoicesByCategory :many
SELECT 
    sqlc.embed(v),
    CASE WHEN uvl.id IS NOT NULL THEN true ELSE false END as is_liked
FROM "aiu-voice-table" v
LEFT JOIN "aiu_user_like_voice" uvl ON 
    uvl."user_id" = sqlc.arg('user_id')::bigint AND 
    uvl."voice_id" = v."voiceId" AND 
    uvl."deleted_at" IS NULL
WHERE (
    -- 官方音乐
    (v.categories @> ARRAY[sqlc.arg('category_id')::int] AND 
     v."creationType" = 2 AND 
     v."displayEnable" = true)
    OR
    -- 用户创建且公开的
    (v.categories @> ARRAY[sqlc.arg('category_id')::int] AND 
     v."creationType" = 0 AND 
     v."displayEnable" = true AND 
     v.enable = true)
    OR
    -- 用户私有的 (当user_id不为0时)
    (CASE 
        WHEN sqlc.arg('user_id')::bigint != 0 THEN
            v.categories @> ARRAY[sqlc.arg('category_id')::int] AND 
            v."creationType" = 0 AND 
            v."creatorUserId" = sqlc.arg('user_id')::bigint AND 
            v.enable = true
        ELSE false
    END)
)
ORDER BY v."displayName" ASC
LIMIT sqlc.arg('pageSize')::int
OFFSET (sqlc.arg('page')::int - 1) * sqlc.arg('pageSize')::int;

-- name: GetVoicesByCreatorUserIDCount :one
SELECT 
    COUNT(*)
FROM "aiu-voice-table" v
LEFT JOIN "aiu_user_like_voice" uvl ON 
    uvl."user_id" = sqlc.arg('user_id')::bigint AND 
    uvl."voice_id" = v."voiceId" AND 
    uvl."deleted_at" IS NULL
WHERE 
    v.enable = true AND
    v."creatorUserId" = sqlc.arg('query_user_id')::bigint AND
    (v."displayEnable" = true OR NOT @filter_by_display_enable::boolean);

-- name: GetVoicesByCreatorUserID :many
SELECT 
    sqlc.embed(v),
    CASE WHEN uvl.id IS NOT NULL THEN true ELSE false END as is_liked
FROM "aiu-voice-table" v
LEFT JOIN "aiu_user_like_voice" uvl ON 
    uvl."user_id" = sqlc.arg('user_id')::bigint AND 
    uvl."voice_id" = v."voiceId" AND 
    uvl."deleted_at" IS NULL
WHERE 
    v.enable = true AND
    v."creatorUserId" = sqlc.arg('query_user_id')::bigint AND
    (v."displayEnable" = true OR NOT @filter_by_display_enable::boolean)
ORDER BY v."createTime" DESC
LIMIT sqlc.arg('pageSize')::int
OFFSET (sqlc.arg('page')::int - 1) * sqlc.arg('pageSize')::int;


-- name: GetVoicesLikedByUserIDCount :one
SELECT 
    COUNT(*)
FROM "aiu-voice-table" v
JOIN "aiu_user_like_voice" uvl ON 
    uvl."user_id" = sqlc.arg('query_user_id')::bigint AND 
    uvl."voice_id" = v."voiceId" AND 
    uvl."deleted_at" IS NULL
WHERE 
    v.enable = true AND
    (
        -- 官方音乐
        (v."creationType" = 2 AND v."displayEnable" = true) OR
        -- 用户创建且公开的
        (v."creationType" = 0 AND v."displayEnable" = true) OR
        -- 用户私有的
        (
            CASE WHEN sqlc.arg('user_id')::bigint = sqlc.arg('query_user_id')::bigint THEN
                v."creatorUserId" = sqlc.arg('user_id')::bigint AND v."creationType" = 0
            ELSE false END
        )
    )
;


-- name: GetVoicesLikedByUserID :many
SELECT 
    v.*
FROM "aiu-voice-table" v
JOIN "aiu_user_like_voice" uvl ON 
    uvl."user_id" = sqlc.arg('query_user_id')::bigint AND 
    uvl."voice_id" = v."voiceId" AND 
    uvl."deleted_at" IS NULL
WHERE 
    v.enable = true AND
    (
        -- 官方音乐
        (v."creationType" = 2 AND v."displayEnable" = true) OR
        -- 用户创建且公开的
        (v."creationType" = 0 AND v."displayEnable" = true) OR
        -- 用户私有的
        (
            CASE WHEN sqlc.arg('user_id')::bigint = sqlc.arg('query_user_id')::bigint THEN
                v."creatorUserId" = sqlc.arg('user_id')::bigint AND v."creationType" = 0
            ELSE false END
        )
    )
ORDER BY uvl."created_at" DESC
LIMIT sqlc.arg('pageSize')::int
OFFSET (sqlc.arg('page')::int - 1) * sqlc.arg('pageSize')::int;

-- name: GetVoiceByID :one
SELECT * FROM "aiu-voice-table" WHERE "voiceId" = sqlc.arg('voice_id')::int;

-- name: UpsertUserVoiceLike :execrows
INSERT INTO aiu_user_like_voice (
    user_id,
    voice_id,
    deleted_at
) VALUES (
    sqlc.arg('user_id')::bigint,
    sqlc.arg('voice_id')::int,
    NULL
)
ON CONFLICT (user_id, voice_id) 
DO UPDATE SET 
    deleted_at = NULL;

-- name: DeleteUserVoiceLike :execrows
UPDATE aiu_user_like_voice
SET deleted_at = NOW()
WHERE user_id = sqlc.arg('user_id')::bigint
AND voice_id = sqlc.arg('voice_id')::int;

-- name: UpdateVoice :one
UPDATE "aiu-voice-table"
SET
    "displayName" = CASE 
        WHEN sqlc.narg('displayName')::text IS NOT NULL THEN sqlc.narg('displayName')::text 
        ELSE "displayName" 
    END,
    enable = CASE 
        WHEN sqlc.narg('enable')::boolean IS NOT NULL THEN sqlc.narg('enable')::boolean 
        ELSE enable 
    END,
    "displayEnable" = CASE 
        WHEN sqlc.narg('public')::boolean IS NOT NULL THEN sqlc.narg('public')::boolean 
        ELSE "displayEnable" 
    END,
    categories = CASE 
        WHEN sqlc.narg('categories')::integer[] IS NOT NULL THEN sqlc.narg('categories')::integer[] 
        ELSE categories 
    END,
    "accentId" = CASE 
        WHEN sqlc.narg('accentId')::bigint IS NOT NULL THEN sqlc.narg('accentId')::bigint 
        ELSE "accentId" 
    END
WHERE
    "voiceId" = sqlc.arg('voiceID')::int
RETURNING *;