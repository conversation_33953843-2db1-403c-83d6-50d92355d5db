// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: sekai.sql

package pg

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const getSekaiCategoriesByLabelIDs = `-- name: GetSekaiCategoriesByLabelIDs :many
SELECT id, label_name, display_name, parent_id, emoji, created_at, updated_at, order_num, show_in_for_you, show_in_new_user_selection FROM aiu_label_table
WHERE id = ANY($1::int[])
`

func (q *Queries) GetSekaiCategoriesByLabelIDs(ctx context.Context, labelIds []int32) ([]*AiuLabelTable, error) {
	rows, err := q.db.Query(ctx, getSekaiCategoriesByLabelIDs, labelIds)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuLabelTable
	for rows.Next() {
		var i AiuLabelTable
		if err := rows.Scan(
			&i.ID,
			&i.LabelName,
			&i.DisplayName,
			&i.ParentID,
			&i.Emoji,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.OrderNum,
			&i.ShowInForYou,
			&i.ShowInNewUserSelection,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSekaiViewCountByIDs = `-- name: GetSekaiViewCountByIDs :many
SELECT sekai_id, count(sekai_id) as view_count
FROM aiu_sekai_session_table
WHERE sekai_id = ANY($1::bigint[])
GROUP BY sekai_id
`

type GetSekaiViewCountByIDsRow struct {
	SekaiID   int64 `json:"sekai_id"`
	ViewCount int64 `json:"view_count"`
}

func (q *Queries) GetSekaiViewCountByIDs(ctx context.Context, sekaiIds []int64) ([]*GetSekaiViewCountByIDsRow, error) {
	rows, err := q.db.Query(ctx, getSekaiViewCountByIDs, sekaiIds)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*GetSekaiViewCountByIDsRow
	for rows.Next() {
		var i GetSekaiViewCountByIDsRow
		if err := rows.Scan(&i.SekaiID, &i.ViewCount); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSekaisByIDsWithOrder = `-- name: GetSekaisByIDsWithOrder :many
SELECT 
    s.sekai_id, s.selected_char_ids, s.selected_user_inserted_char_ids, s.what_if, s.char_ids, s.user_char_id, s.action_button, s.background_image, s.background_keywords, s.title, s.intro, s.bgm, s.narrator_voice_id, s.enable_narrator_voice, s.created_at, s.updated_at, s.deleted_at, s.creator_user_id, s.bgm_volume, s.creator_user_name, s.sekai_type, s.cover_url, s.source_id, s.is_public, s.ai_hash_tags, s.selected_user_char_id, s.published, s.image_nsfw, s.text_nsfw, s.nsfw, s.selected_copy_char_ids, s.summary, s.background_image_info, s.bgm_tag, s.greeting, s.player_info, s.extra, s.template_id, s.show_settings, s.hash_tags, s.min_app_version, s.background_image_type, s.comment_count, s.ai_category, s.user_category, s.cover_type, s.cover_image, s.cover_char
FROM aiu_sekai_info_table s
JOIN UNNEST($1::bigint[]) WITH ORDINALITY AS ids(id, ordering) ON s.sekai_id = ids.id
WHERE 
    (s.published = $2 OR NOT $3::boolean)
    AND (s.is_public = $4 OR NOT $5::boolean)
    AND s.deleted_at IS NULL
ORDER BY ids.ordering
`

type GetSekaisByIDsWithOrderParams struct {
	SekaiIds          []int64     `json:"sekai_ids"`
	Published         pgtype.Bool `json:"published"`
	FilterByPublished bool        `json:"filter_by_published"`
	IsPublic          pgtype.Bool `json:"is_public"`
	FilterByPublic    bool        `json:"filter_by_public"`
}

// 根据ID列表获取sekai信息，并按照指定的顺序返回
func (q *Queries) GetSekaisByIDsWithOrder(ctx context.Context, arg *GetSekaisByIDsWithOrderParams) ([]*AiuSekaiInfoTable, error) {
	rows, err := q.db.Query(ctx, getSekaisByIDsWithOrder,
		arg.SekaiIds,
		arg.Published,
		arg.FilterByPublished,
		arg.IsPublic,
		arg.FilterByPublic,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuSekaiInfoTable
	for rows.Next() {
		var i AiuSekaiInfoTable
		if err := rows.Scan(
			&i.SekaiID,
			&i.SelectedCharIds,
			&i.SelectedUserInsertedCharIds,
			&i.WhatIf,
			&i.CharIds,
			&i.UserCharID,
			&i.ActionButton,
			&i.BackgroundImage,
			&i.BackgroundKeywords,
			&i.Title,
			&i.Intro,
			&i.Bgm,
			&i.NarratorVoiceID,
			&i.EnableNarratorVoice,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.CreatorUserID,
			&i.BgmVolume,
			&i.CreatorUserName,
			&i.SekaiType,
			&i.CoverUrl,
			&i.SourceID,
			&i.IsPublic,
			&i.AiHashTags,
			&i.SelectedUserCharID,
			&i.Published,
			&i.ImageNsfw,
			&i.TextNsfw,
			&i.Nsfw,
			&i.SelectedCopyCharIds,
			&i.Summary,
			&i.BackgroundImageInfo,
			&i.BgmTag,
			&i.Greeting,
			&i.PlayerInfo,
			&i.Extra,
			&i.TemplateID,
			&i.ShowSettings,
			&i.HashTags,
			&i.MinAppVersion,
			&i.BackgroundImageType,
			&i.CommentCount,
			&i.AiCategory,
			&i.UserCategory,
			&i.CoverType,
			&i.CoverImage,
			&i.CoverChar,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
