// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: voice.sql

package pg

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createVoice = `-- name: CreateVoice :one
INSERT INTO "aiu-voice-table" (
    "displayName",
    "sampleUrl",
    "recordUrl",
    "recordText",
    "processedRecordUrl",
    "displayEnable",
    "charName",
    "labsTag",
    "labsId",
    "rvcTag",
    "rvcId",
    "rvcTranspose",
    "creatorUserId",
    "creationType",
    enable,
    "createTime",
    duration,
    "accentId"
) VALUES (
    $1::text,
    $2::text,
    $3::text,
    $4::text,
    $5::text,
    $6::boolean,
    $7::text,
    $8::text[],
    $9::text,
    $10::text[],
    $11::text,
    $12::int,
    $13::bigint,
    $14::int,
    false,
    $15::bigint,
    $16::int,
    $17::bigint
)
RETURNING "voiceId", "displayName", "sampleUrl", "displayEnable", "charName", "labsTag", "labsId", "rvcTag", "rvcId", "creatorUserId", "creationType", enable, "createTime", duration, "rvcTranspose", "recordUrl", weight, "processedRecordUrl", "recordText", categories, nsfw, "accentId"
`

type CreateVoiceParams struct {
	DisplayName        string   `json:"displayName"`
	SampleUrl          string   `json:"sampleUrl"`
	RecordUrl          string   `json:"recordUrl"`
	RecordText         string   `json:"recordText"`
	ProcessedRecordUrl string   `json:"processedRecordUrl"`
	DisplayEnable      bool     `json:"displayEnable"`
	CharName           string   `json:"charName"`
	LabsTag            []string `json:"labsTag"`
	LabsId             string   `json:"labsId"`
	RvcTag             []string `json:"rvcTag"`
	RvcId              string   `json:"rvcId"`
	RvcTranspose       int32    `json:"rvcTranspose"`
	CreatorUserId      int64    `json:"creatorUserId"`
	CreationType       int32    `json:"creationType"`
	CreateTime         int64    `json:"createTime"`
	Duration           int32    `json:"duration"`
	AccentId           int64    `json:"accentId"`
}

func (q *Queries) CreateVoice(ctx context.Context, arg *CreateVoiceParams) (*AiuVoiceTable, error) {
	row := q.db.QueryRow(ctx, createVoice,
		arg.DisplayName,
		arg.SampleUrl,
		arg.RecordUrl,
		arg.RecordText,
		arg.ProcessedRecordUrl,
		arg.DisplayEnable,
		arg.CharName,
		arg.LabsTag,
		arg.LabsId,
		arg.RvcTag,
		arg.RvcId,
		arg.RvcTranspose,
		arg.CreatorUserId,
		arg.CreationType,
		arg.CreateTime,
		arg.Duration,
		arg.AccentId,
	)
	var i AiuVoiceTable
	err := row.Scan(
		&i.VoiceId,
		&i.DisplayName,
		&i.SampleUrl,
		&i.DisplayEnable,
		&i.CharName,
		&i.LabsTag,
		&i.LabsId,
		&i.RvcTag,
		&i.RvcId,
		&i.CreatorUserId,
		&i.CreationType,
		&i.Enable,
		&i.CreateTime,
		&i.Duration,
		&i.RvcTranspose,
		&i.RecordUrl,
		&i.Weight,
		&i.ProcessedRecordUrl,
		&i.RecordText,
		&i.Categories,
		&i.Nsfw,
		&i.AccentId,
	)
	return &i, err
}

const deleteUserVoiceLike = `-- name: DeleteUserVoiceLike :execrows
UPDATE aiu_user_like_voice
SET deleted_at = NOW()
WHERE user_id = $1::bigint
AND voice_id = $2::int
`

type DeleteUserVoiceLikeParams struct {
	UserID  int64 `json:"user_id"`
	VoiceID int32 `json:"voice_id"`
}

func (q *Queries) DeleteUserVoiceLike(ctx context.Context, arg *DeleteUserVoiceLikeParams) (int64, error) {
	result, err := q.db.Exec(ctx, deleteUserVoiceLike, arg.UserID, arg.VoiceID)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const getAccents = `-- name: GetAccents :many
SELECT id, voice_name, accent_name, is_delete, created_at, updated_at FROM aiu_voice_accent_table
WHERE is_delete = false
ORDER BY id ASC
LIMIT $2::int
OFFSET ($1::int - 1) * $2::int
`

type GetAccentsParams struct {
	Page     int32 `json:"page"`
	PageSize int32 `json:"pageSize"`
}

func (q *Queries) GetAccents(ctx context.Context, arg *GetAccentsParams) ([]*AiuVoiceAccentTable, error) {
	rows, err := q.db.Query(ctx, getAccents, arg.Page, arg.PageSize)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuVoiceAccentTable
	for rows.Next() {
		var i AiuVoiceAccentTable
		if err := rows.Scan(
			&i.ID,
			&i.VoiceName,
			&i.AccentName,
			&i.IsDelete,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAccentsByIDs = `-- name: GetAccentsByIDs :many
SELECT id, voice_name, accent_name, is_delete, created_at, updated_at FROM aiu_voice_accent_table
WHERE id = ANY($1::int[]) AND
    is_delete = false
`

func (q *Queries) GetAccentsByIDs(ctx context.Context, accentIds []int32) ([]*AiuVoiceAccentTable, error) {
	rows, err := q.db.Query(ctx, getAccentsByIDs, accentIds)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuVoiceAccentTable
	for rows.Next() {
		var i AiuVoiceAccentTable
		if err := rows.Scan(
			&i.ID,
			&i.VoiceName,
			&i.AccentName,
			&i.IsDelete,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAccentsCount = `-- name: GetAccentsCount :one
SELECT COUNT(*) FROM aiu_voice_accent_table
WHERE is_delete = false
`

func (q *Queries) GetAccentsCount(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, getAccentsCount)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getVoiceByID = `-- name: GetVoiceByID :one
SELECT "voiceId", "displayName", "sampleUrl", "displayEnable", "charName", "labsTag", "labsId", "rvcTag", "rvcId", "creatorUserId", "creationType", enable, "createTime", duration, "rvcTranspose", "recordUrl", weight, "processedRecordUrl", "recordText", categories, nsfw, "accentId" FROM "aiu-voice-table" WHERE "voiceId" = $1::int
`

func (q *Queries) GetVoiceByID(ctx context.Context, voiceID int32) (*AiuVoiceTable, error) {
	row := q.db.QueryRow(ctx, getVoiceByID, voiceID)
	var i AiuVoiceTable
	err := row.Scan(
		&i.VoiceId,
		&i.DisplayName,
		&i.SampleUrl,
		&i.DisplayEnable,
		&i.CharName,
		&i.LabsTag,
		&i.LabsId,
		&i.RvcTag,
		&i.RvcId,
		&i.CreatorUserId,
		&i.CreationType,
		&i.Enable,
		&i.CreateTime,
		&i.Duration,
		&i.RvcTranspose,
		&i.RecordUrl,
		&i.Weight,
		&i.ProcessedRecordUrl,
		&i.RecordText,
		&i.Categories,
		&i.Nsfw,
		&i.AccentId,
	)
	return &i, err
}

const getVoiceCategories = `-- name: GetVoiceCategories :many
SELECT id, name, creation_type, weight, cover_url, created_at, updated_at, deleted_at FROM aiu_voice_category
WHERE deleted_at IS NULL AND creation_type = $1::int
ORDER BY weight DESC
LIMIT $3::int
OFFSET ($2::int - 1) * $3::int
`

type GetVoiceCategoriesParams struct {
	CreationType int32 `json:"creationType"`
	Page         int32 `json:"page"`
	PageSize     int32 `json:"pageSize"`
}

func (q *Queries) GetVoiceCategories(ctx context.Context, arg *GetVoiceCategoriesParams) ([]*AiuVoiceCategory, error) {
	rows, err := q.db.Query(ctx, getVoiceCategories, arg.CreationType, arg.Page, arg.PageSize)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuVoiceCategory
	for rows.Next() {
		var i AiuVoiceCategory
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.CreationType,
			&i.Weight,
			&i.CoverUrl,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getVoiceCategoriesByIDs = `-- name: GetVoiceCategoriesByIDs :many
SELECT id, name, creation_type, weight, cover_url, created_at, updated_at, deleted_at FROM aiu_voice_category
WHERE id = ANY($1::int[]) AND
    deleted_at IS NULL
ORDER BY weight DESC
`

func (q *Queries) GetVoiceCategoriesByIDs(ctx context.Context, categoryIds []int32) ([]*AiuVoiceCategory, error) {
	rows, err := q.db.Query(ctx, getVoiceCategoriesByIDs, categoryIds)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuVoiceCategory
	for rows.Next() {
		var i AiuVoiceCategory
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.CreationType,
			&i.Weight,
			&i.CoverUrl,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getVoiceCategoriesCount = `-- name: GetVoiceCategoriesCount :one
SELECT COUNT(*) FROM aiu_voice_category
WHERE deleted_at IS NULL AND creation_type = $1::int
`

func (q *Queries) GetVoiceCategoriesCount(ctx context.Context, creationtype int32) (int64, error) {
	row := q.db.QueryRow(ctx, getVoiceCategoriesCount, creationtype)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getVoiceCountByCreatorUserID = `-- name: GetVoiceCountByCreatorUserID :one
SELECT COUNT(*) FROM "aiu-voice-table" 
WHERE "creatorUserId"=$1::int AND enable=true
`

func (q *Queries) GetVoiceCountByCreatorUserID(ctx context.Context, userid int32) (int64, error) {
	row := q.db.QueryRow(ctx, getVoiceCountByCreatorUserID, userid)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getVoicesByCategory = `-- name: GetVoicesByCategory :many
SELECT 
    v."voiceId", v."displayName", v."sampleUrl", v."displayEnable", v."charName", v."labsTag", v."labsId", v."rvcTag", v."rvcId", v."creatorUserId", v."creationType", v.enable, v."createTime", v.duration, v."rvcTranspose", v."recordUrl", v.weight, v."processedRecordUrl", v."recordText", v.categories, v.nsfw, v."accentId",
    CASE WHEN uvl.id IS NOT NULL THEN true ELSE false END as is_liked
FROM "aiu-voice-table" v
LEFT JOIN "aiu_user_like_voice" uvl ON 
    uvl."user_id" = $1::bigint AND 
    uvl."voice_id" = v."voiceId" AND 
    uvl."deleted_at" IS NULL
WHERE (
    -- 官方音乐
    (v.categories @> ARRAY[$2::int] AND 
     v."creationType" = 2 AND 
     v."displayEnable" = true)
    OR
    -- 用户创建且公开的
    (v.categories @> ARRAY[$2::int] AND 
     v."creationType" = 0 AND 
     v."displayEnable" = true AND 
     v.enable = true)
    OR
    -- 用户私有的 (当user_id不为0时)
    (CASE 
        WHEN $1::bigint != 0 THEN
            v.categories @> ARRAY[$2::int] AND 
            v."creationType" = 0 AND 
            v."creatorUserId" = $1::bigint AND 
            v.enable = true
        ELSE false
    END)
)
ORDER BY v."displayName" ASC
LIMIT $4::int
OFFSET ($3::int - 1) * $4::int
`

type GetVoicesByCategoryParams struct {
	UserID     int64 `json:"user_id"`
	CategoryID int32 `json:"category_id"`
	Page       int32 `json:"page"`
	PageSize   int32 `json:"pageSize"`
}

type GetVoicesByCategoryRow struct {
	AiuVoiceTable AiuVoiceTable `json:"aiu_voice_table"`
	IsLiked       bool          `json:"is_liked"`
}

func (q *Queries) GetVoicesByCategory(ctx context.Context, arg *GetVoicesByCategoryParams) ([]*GetVoicesByCategoryRow, error) {
	rows, err := q.db.Query(ctx, getVoicesByCategory,
		arg.UserID,
		arg.CategoryID,
		arg.Page,
		arg.PageSize,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*GetVoicesByCategoryRow
	for rows.Next() {
		var i GetVoicesByCategoryRow
		if err := rows.Scan(
			&i.AiuVoiceTable.VoiceId,
			&i.AiuVoiceTable.DisplayName,
			&i.AiuVoiceTable.SampleUrl,
			&i.AiuVoiceTable.DisplayEnable,
			&i.AiuVoiceTable.CharName,
			&i.AiuVoiceTable.LabsTag,
			&i.AiuVoiceTable.LabsId,
			&i.AiuVoiceTable.RvcTag,
			&i.AiuVoiceTable.RvcId,
			&i.AiuVoiceTable.CreatorUserId,
			&i.AiuVoiceTable.CreationType,
			&i.AiuVoiceTable.Enable,
			&i.AiuVoiceTable.CreateTime,
			&i.AiuVoiceTable.Duration,
			&i.AiuVoiceTable.RvcTranspose,
			&i.AiuVoiceTable.RecordUrl,
			&i.AiuVoiceTable.Weight,
			&i.AiuVoiceTable.ProcessedRecordUrl,
			&i.AiuVoiceTable.RecordText,
			&i.AiuVoiceTable.Categories,
			&i.AiuVoiceTable.Nsfw,
			&i.AiuVoiceTable.AccentId,
			&i.IsLiked,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getVoicesByCategoryCount = `-- name: GetVoicesByCategoryCount :one
SELECT COUNT(*) FROM "aiu-voice-table"
WHERE (
    -- 官方音乐
    (categories @> ARRAY[$1::int] AND 
     "creationType" = 2 AND 
     "displayEnable" = true)
    OR
    -- 用户创建且公开的
    (categories @> ARRAY[$1::int] AND 
     "creationType" = 0 AND 
     "displayEnable" = true AND 
     enable = true)
    OR
    -- 用户私有的 (当user_id不为0时)
    (CASE 
        WHEN $2::bigint != 0 THEN
            categories @> ARRAY[$1::int] AND 
            "creationType" = 0 AND 
            "creatorUserId" = $2::bigint AND 
            enable = true
        ELSE false
    END)
)
`

type GetVoicesByCategoryCountParams struct {
	CategoryID int32 `json:"category_id"`
	UserID     int64 `json:"user_id"`
}

func (q *Queries) GetVoicesByCategoryCount(ctx context.Context, arg *GetVoicesByCategoryCountParams) (int64, error) {
	row := q.db.QueryRow(ctx, getVoicesByCategoryCount, arg.CategoryID, arg.UserID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getVoicesByCreatorUserID = `-- name: GetVoicesByCreatorUserID :many
SELECT 
    v."voiceId", v."displayName", v."sampleUrl", v."displayEnable", v."charName", v."labsTag", v."labsId", v."rvcTag", v."rvcId", v."creatorUserId", v."creationType", v.enable, v."createTime", v.duration, v."rvcTranspose", v."recordUrl", v.weight, v."processedRecordUrl", v."recordText", v.categories, v.nsfw, v."accentId",
    CASE WHEN uvl.id IS NOT NULL THEN true ELSE false END as is_liked
FROM "aiu-voice-table" v
LEFT JOIN "aiu_user_like_voice" uvl ON 
    uvl."user_id" = $1::bigint AND 
    uvl."voice_id" = v."voiceId" AND 
    uvl."deleted_at" IS NULL
WHERE 
    v.enable = true AND
    v."creatorUserId" = $2::bigint AND
    (v."displayEnable" = true OR NOT $3::boolean)
ORDER BY v."createTime" DESC
LIMIT $5::int
OFFSET ($4::int - 1) * $5::int
`

type GetVoicesByCreatorUserIDParams struct {
	UserID                int64 `json:"user_id"`
	QueryUserID           int64 `json:"query_user_id"`
	FilterByDisplayEnable bool  `json:"filter_by_display_enable"`
	Page                  int32 `json:"page"`
	PageSize              int32 `json:"pageSize"`
}

type GetVoicesByCreatorUserIDRow struct {
	AiuVoiceTable AiuVoiceTable `json:"aiu_voice_table"`
	IsLiked       bool          `json:"is_liked"`
}

func (q *Queries) GetVoicesByCreatorUserID(ctx context.Context, arg *GetVoicesByCreatorUserIDParams) ([]*GetVoicesByCreatorUserIDRow, error) {
	rows, err := q.db.Query(ctx, getVoicesByCreatorUserID,
		arg.UserID,
		arg.QueryUserID,
		arg.FilterByDisplayEnable,
		arg.Page,
		arg.PageSize,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*GetVoicesByCreatorUserIDRow
	for rows.Next() {
		var i GetVoicesByCreatorUserIDRow
		if err := rows.Scan(
			&i.AiuVoiceTable.VoiceId,
			&i.AiuVoiceTable.DisplayName,
			&i.AiuVoiceTable.SampleUrl,
			&i.AiuVoiceTable.DisplayEnable,
			&i.AiuVoiceTable.CharName,
			&i.AiuVoiceTable.LabsTag,
			&i.AiuVoiceTable.LabsId,
			&i.AiuVoiceTable.RvcTag,
			&i.AiuVoiceTable.RvcId,
			&i.AiuVoiceTable.CreatorUserId,
			&i.AiuVoiceTable.CreationType,
			&i.AiuVoiceTable.Enable,
			&i.AiuVoiceTable.CreateTime,
			&i.AiuVoiceTable.Duration,
			&i.AiuVoiceTable.RvcTranspose,
			&i.AiuVoiceTable.RecordUrl,
			&i.AiuVoiceTable.Weight,
			&i.AiuVoiceTable.ProcessedRecordUrl,
			&i.AiuVoiceTable.RecordText,
			&i.AiuVoiceTable.Categories,
			&i.AiuVoiceTable.Nsfw,
			&i.AiuVoiceTable.AccentId,
			&i.IsLiked,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getVoicesByCreatorUserIDCount = `-- name: GetVoicesByCreatorUserIDCount :one
SELECT 
    COUNT(*)
FROM "aiu-voice-table" v
LEFT JOIN "aiu_user_like_voice" uvl ON 
    uvl."user_id" = $1::bigint AND 
    uvl."voice_id" = v."voiceId" AND 
    uvl."deleted_at" IS NULL
WHERE 
    v.enable = true AND
    v."creatorUserId" = $2::bigint AND
    (v."displayEnable" = true OR NOT $3::boolean)
`

type GetVoicesByCreatorUserIDCountParams struct {
	UserID                int64 `json:"user_id"`
	QueryUserID           int64 `json:"query_user_id"`
	FilterByDisplayEnable bool  `json:"filter_by_display_enable"`
}

func (q *Queries) GetVoicesByCreatorUserIDCount(ctx context.Context, arg *GetVoicesByCreatorUserIDCountParams) (int64, error) {
	row := q.db.QueryRow(ctx, getVoicesByCreatorUserIDCount, arg.UserID, arg.QueryUserID, arg.FilterByDisplayEnable)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getVoicesLikedByUserID = `-- name: GetVoicesLikedByUserID :many
SELECT 
    v."voiceId", v."displayName", v."sampleUrl", v."displayEnable", v."charName", v."labsTag", v."labsId", v."rvcTag", v."rvcId", v."creatorUserId", v."creationType", v.enable, v."createTime", v.duration, v."rvcTranspose", v."recordUrl", v.weight, v."processedRecordUrl", v."recordText", v.categories, v.nsfw, v."accentId"
FROM "aiu-voice-table" v
JOIN "aiu_user_like_voice" uvl ON 
    uvl."user_id" = $1::bigint AND 
    uvl."voice_id" = v."voiceId" AND 
    uvl."deleted_at" IS NULL
WHERE 
    v.enable = true AND
    (
        -- 官方音乐
        (v."creationType" = 2 AND v."displayEnable" = true) OR
        -- 用户创建且公开的
        (v."creationType" = 0 AND v."displayEnable" = true) OR
        -- 用户私有的
        (
            CASE WHEN $2::bigint = $1::bigint THEN
                v."creatorUserId" = $2::bigint AND v."creationType" = 0
            ELSE false END
        )
    )
ORDER BY uvl."created_at" DESC
LIMIT $4::int
OFFSET ($3::int - 1) * $4::int
`

type GetVoicesLikedByUserIDParams struct {
	QueryUserID int64 `json:"query_user_id"`
	UserID      int64 `json:"user_id"`
	Page        int32 `json:"page"`
	PageSize    int32 `json:"pageSize"`
}

func (q *Queries) GetVoicesLikedByUserID(ctx context.Context, arg *GetVoicesLikedByUserIDParams) ([]*AiuVoiceTable, error) {
	rows, err := q.db.Query(ctx, getVoicesLikedByUserID,
		arg.QueryUserID,
		arg.UserID,
		arg.Page,
		arg.PageSize,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuVoiceTable
	for rows.Next() {
		var i AiuVoiceTable
		if err := rows.Scan(
			&i.VoiceId,
			&i.DisplayName,
			&i.SampleUrl,
			&i.DisplayEnable,
			&i.CharName,
			&i.LabsTag,
			&i.LabsId,
			&i.RvcTag,
			&i.RvcId,
			&i.CreatorUserId,
			&i.CreationType,
			&i.Enable,
			&i.CreateTime,
			&i.Duration,
			&i.RvcTranspose,
			&i.RecordUrl,
			&i.Weight,
			&i.ProcessedRecordUrl,
			&i.RecordText,
			&i.Categories,
			&i.Nsfw,
			&i.AccentId,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getVoicesLikedByUserIDCount = `-- name: GetVoicesLikedByUserIDCount :one
SELECT 
    COUNT(*)
FROM "aiu-voice-table" v
JOIN "aiu_user_like_voice" uvl ON 
    uvl."user_id" = $1::bigint AND 
    uvl."voice_id" = v."voiceId" AND 
    uvl."deleted_at" IS NULL
WHERE 
    v.enable = true AND
    (
        -- 官方音乐
        (v."creationType" = 2 AND v."displayEnable" = true) OR
        -- 用户创建且公开的
        (v."creationType" = 0 AND v."displayEnable" = true) OR
        -- 用户私有的
        (
            CASE WHEN $2::bigint = $1::bigint THEN
                v."creatorUserId" = $2::bigint AND v."creationType" = 0
            ELSE false END
        )
    )
`

type GetVoicesLikedByUserIDCountParams struct {
	QueryUserID int64 `json:"query_user_id"`
	UserID      int64 `json:"user_id"`
}

func (q *Queries) GetVoicesLikedByUserIDCount(ctx context.Context, arg *GetVoicesLikedByUserIDCountParams) (int64, error) {
	row := q.db.QueryRow(ctx, getVoicesLikedByUserIDCount, arg.QueryUserID, arg.UserID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getVoicesWithLikeStatus = `-- name: GetVoicesWithLikeStatus :many
SELECT 
    v."voiceId", v."displayName", v."sampleUrl", v."displayEnable", v."charName", v."labsTag", v."labsId", v."rvcTag", v."rvcId", v."creatorUserId", v."creationType", v.enable, v."createTime", v.duration, v."rvcTranspose", v."recordUrl", v.weight, v."processedRecordUrl", v."recordText", v.categories, v.nsfw, v."accentId",
    CASE WHEN uvl.id IS NOT NULL THEN TRUE ELSE FALSE END AS is_liked
FROM 
    "aiu-voice-table" v
LEFT JOIN 
    aiu_user_like_voice uvl ON 
        uvl.user_id = $1 AND 
        uvl.voice_id = v."voiceId" AND 
        uvl.deleted_at IS NULL
WHERE 
    v."voiceId" = ANY($2::int[]) AND
    (NOT $3::boolean OR v."displayEnable" = TRUE) AND
    (NOT $4::boolean OR v.enable = TRUE)
ORDER BY 
    array_position($2::int[], v."voiceId")
`

type GetVoicesWithLikeStatusParams struct {
	UserID                int64   `json:"user_id"`
	VoiceIds              []int32 `json:"voice_ids"`
	FilterByDisplayEnable bool    `json:"filter_by_display_enable"`
	FilterByEnable        bool    `json:"filter_by_enable"`
}

type GetVoicesWithLikeStatusRow struct {
	AiuVoiceTable AiuVoiceTable `json:"aiu_voice_table"`
	IsLiked       bool          `json:"is_liked"`
}

func (q *Queries) GetVoicesWithLikeStatus(ctx context.Context, arg *GetVoicesWithLikeStatusParams) ([]*GetVoicesWithLikeStatusRow, error) {
	rows, err := q.db.Query(ctx, getVoicesWithLikeStatus,
		arg.UserID,
		arg.VoiceIds,
		arg.FilterByDisplayEnable,
		arg.FilterByEnable,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*GetVoicesWithLikeStatusRow
	for rows.Next() {
		var i GetVoicesWithLikeStatusRow
		if err := rows.Scan(
			&i.AiuVoiceTable.VoiceId,
			&i.AiuVoiceTable.DisplayName,
			&i.AiuVoiceTable.SampleUrl,
			&i.AiuVoiceTable.DisplayEnable,
			&i.AiuVoiceTable.CharName,
			&i.AiuVoiceTable.LabsTag,
			&i.AiuVoiceTable.LabsId,
			&i.AiuVoiceTable.RvcTag,
			&i.AiuVoiceTable.RvcId,
			&i.AiuVoiceTable.CreatorUserId,
			&i.AiuVoiceTable.CreationType,
			&i.AiuVoiceTable.Enable,
			&i.AiuVoiceTable.CreateTime,
			&i.AiuVoiceTable.Duration,
			&i.AiuVoiceTable.RvcTranspose,
			&i.AiuVoiceTable.RecordUrl,
			&i.AiuVoiceTable.Weight,
			&i.AiuVoiceTable.ProcessedRecordUrl,
			&i.AiuVoiceTable.RecordText,
			&i.AiuVoiceTable.Categories,
			&i.AiuVoiceTable.Nsfw,
			&i.AiuVoiceTable.AccentId,
			&i.IsLiked,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateVoice = `-- name: UpdateVoice :one
UPDATE "aiu-voice-table"
SET
    "displayName" = CASE 
        WHEN $1::text IS NOT NULL THEN $1::text 
        ELSE "displayName" 
    END,
    enable = CASE 
        WHEN $2::boolean IS NOT NULL THEN $2::boolean 
        ELSE enable 
    END,
    "displayEnable" = CASE 
        WHEN $3::boolean IS NOT NULL THEN $3::boolean 
        ELSE "displayEnable" 
    END,
    categories = CASE 
        WHEN $4::integer[] IS NOT NULL THEN $4::integer[] 
        ELSE categories 
    END,
    "accentId" = CASE 
        WHEN $5::bigint IS NOT NULL THEN $5::bigint 
        ELSE "accentId" 
    END
WHERE
    "voiceId" = $6::int
RETURNING "voiceId", "displayName", "sampleUrl", "displayEnable", "charName", "labsTag", "labsId", "rvcTag", "rvcId", "creatorUserId", "creationType", enable, "createTime", duration, "rvcTranspose", "recordUrl", weight, "processedRecordUrl", "recordText", categories, nsfw, "accentId"
`

type UpdateVoiceParams struct {
	DisplayName pgtype.Text `json:"displayName"`
	Enable      pgtype.Bool `json:"enable"`
	Public      pgtype.Bool `json:"public"`
	Categories  []int32     `json:"categories"`
	AccentId    pgtype.Int8 `json:"accentId"`
	VoiceID     int32       `json:"voiceID"`
}

func (q *Queries) UpdateVoice(ctx context.Context, arg *UpdateVoiceParams) (*AiuVoiceTable, error) {
	row := q.db.QueryRow(ctx, updateVoice,
		arg.DisplayName,
		arg.Enable,
		arg.Public,
		arg.Categories,
		arg.AccentId,
		arg.VoiceID,
	)
	var i AiuVoiceTable
	err := row.Scan(
		&i.VoiceId,
		&i.DisplayName,
		&i.SampleUrl,
		&i.DisplayEnable,
		&i.CharName,
		&i.LabsTag,
		&i.LabsId,
		&i.RvcTag,
		&i.RvcId,
		&i.CreatorUserId,
		&i.CreationType,
		&i.Enable,
		&i.CreateTime,
		&i.Duration,
		&i.RvcTranspose,
		&i.RecordUrl,
		&i.Weight,
		&i.ProcessedRecordUrl,
		&i.RecordText,
		&i.Categories,
		&i.Nsfw,
		&i.AccentId,
	)
	return &i, err
}

const upsertUserVoiceLike = `-- name: UpsertUserVoiceLike :execrows
INSERT INTO aiu_user_like_voice (
    user_id,
    voice_id,
    deleted_at
) VALUES (
    $1::bigint,
    $2::int,
    NULL
)
ON CONFLICT (user_id, voice_id) 
DO UPDATE SET 
    deleted_at = NULL
`

type UpsertUserVoiceLikeParams struct {
	UserID  int64 `json:"user_id"`
	VoiceID int32 `json:"voice_id"`
}

func (q *Queries) UpsertUserVoiceLike(ctx context.Context, arg *UpsertUserVoiceLikeParams) (int64, error) {
	result, err := q.db.Exec(ctx, upsertUserVoiceLike, arg.UserID, arg.VoiceID)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}
