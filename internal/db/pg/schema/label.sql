create table aiu_label_table
(
    id                         serial
        primary key,
    label_name                 varchar(255)             not null
        unique,
    display_name               varchar(255),
    parent_id                  integer
        references aiu_label_table,
    emoji                      varchar(64)              not null,
    created_at                 timestamp default now()  not null,
    updated_at                 timestamp default now()  not null,
    order_num                  integer   default 100000 not null,
    show_in_for_you            boolean   default true   not null,
    show_in_new_user_selection boolean   default true   not null
);

create index idx_aiu_label_parent_id
    on aiu_label_table (parent_id);

create index idx_aiu_label_name
    on aiu_label_table (label_name);
