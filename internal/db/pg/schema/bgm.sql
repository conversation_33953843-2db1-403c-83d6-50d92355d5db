-- BGM表
create table "aiu-bgm-table"
(
    id               bigint    default nextval('"aiu-bgm-table_bgmId_seq"'::regclass) not null
        primary key,
    url              text,
    name             text,
    "coverUrl"       text,
    duration         integer,
    tags             text[],
    "referenceCount" integer,
    "creatorUserId"  bigint,
    "creationType"   integer   default 0                                              not null,
    enable           boolean   default false,
    public           boolean   default false                                          not null,
    "createTimeUtc"  bigint,
    "premiseIds"     text[]    default ARRAY []::text[],
    categories       integer[] default ARRAY []::integer[],
    nsfw             boolean   default false                                          not null
);

-- 用户点赞BGM
create table aiu_user_like_bgm
(
    id         integer                     default nextval('aiu_user_like_voice_id_seq'::regclass) not null
        primary key,
    user_id    bigint                                                                              not null,
    bgm_id     bigint                                                                              not null,
    created_at timestamp(6) with time zone default CURRENT_TIMESTAMP,
    deleted_at timestamp(6) with time zone,
    is_delete  boolean                     default false
);

create unique index ix_aiu_user_like_bgm_user_id_bgm_id
    on aiu_user_like_bgm (user_id, bgm_id);

create index ix_aiu_user_like_bgm_bgm_id
    on aiu_user_like_bgm (bgm_id);

-- BGM分类表
create table aiu_bgm_category
(
    id            bigint generated by default as identity
        primary key,
    name          text                                not null,
    tag           text,
    cover_url     text,
    weight        integer   default 0                 not null,
    creation_type integer   default 0                 not null,
    created_at    timestamp default CURRENT_TIMESTAMP not null,
    updated_at    timestamp default CURRENT_TIMESTAMP
);

create index idx_name
    on aiu_bgm_category (name);

create index idx_tag
    on aiu_bgm_category (tag);