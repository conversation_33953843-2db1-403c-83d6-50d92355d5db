create table aiu_sekai_info_table
(
    sekai_id                        bigint generated always as identity
        primary key,
    selected_char_ids               integer[] default ARRAY []::integer[],
    selected_user_inserted_char_ids integer[] default ARRAY []::integer[],
    what_if                         text      default ''::text,
    char_ids                        integer[] default ARRAY []::integer[],
    user_char_id                    bigint    default 0,
    action_button                   text      default 'start'::text,
    background_image                text      default ''::text,
    background_keywords             text      default ''::text,
    title                           text      default ''::text,
    intro                           text      default ''::text,
    bgm                             integer   default 0,
    narrator_voice_id               bigint    default 0,
    enable_narrator_voice           boolean   default false,
    created_at                      timestamp default CURRENT_TIMESTAMP,
    updated_at                      timestamp default CURRENT_TIMESTAMP,
    deleted_at                      timestamp,
    creator_user_id                 bigint    default 0,
    bgm_volume                      bigint    default 0,
    creator_user_name               text      default ''::text,
    sekai_type                      integer   default 0,
    cover_url                       text      default ''::text,
    source_id                       bigint    default 0,
    is_public                       boolean   default true,
    ai_hash_tags                    integer[] default ARRAY []::integer[],
    selected_user_char_id           bigint    default 0     not null,
    published                       boolean   default false,
    image_nsfw                      boolean   default false not null,
    text_nsfw                       boolean   default false not null,
    nsfw                            boolean   default false not null,
    selected_copy_char_ids          integer[] default ARRAY []::integer[],
    summary                         text[]    default ARRAY []::text[],
    background_image_info           jsonb     default '{}'::jsonb,
    bgm_tag                         text      default ''::text,
    greeting                        jsonb     default '[]'::jsonb,
    player_info                     json      default '{}'::json,
    extra                           json      default '{}'::json,
    template_id                     bigint    default 0,
    show_settings                   boolean   default false,
    hash_tags                       integer[] default ARRAY []::integer[],
    min_app_version                 integer   default 0,
    background_image_type           integer   default 0,
    comment_count                   integer   default 0,
    ai_category                     bigint[]  default ARRAY []::bigint[],
    user_category                   bigint[]  default ARRAY []::bigint[],
    cover_type                      integer   default 0,
    cover_image                     text      default ''::text,
    cover_char                      integer   default 0
);

create index idx_creator_user_id
    on aiu_sekai_info_table (creator_user_id);

create index idx_sekai_type
    on aiu_sekai_info_table (sekai_type);

create index idx_sekai_type_id
    on aiu_sekai_info_table (sekai_type, sekai_id);

---------------

create table aiu_sekai_session_table
(
    id              bigint generated always as identity
        primary key,
    user_id         bigint                             not null,
    sekai_id        bigint                             not null,
    sekai_title     text                     default ''::text,
    bg_url          text                     default ''::text,
    greeting_url    text                     default ''::text,
    records         jsonb                    default '[]'::jsonb,
    extra           jsonb                    default '{}'::jsonb,
    last_message_id bigint                   default 0 not null,
    last_message    text                     default ''::text,
    last_message_at timestamp with time zone default CURRENT_TIMESTAMP,
    created_at      timestamp with time zone default CURRENT_TIMESTAMP,
    updated_at      timestamp with time zone default CURRENT_TIMESTAMP,
    deleted_at      timestamp with time zone,
    summary         jsonb                    default '[]'::jsonb,
    template_id     bigint                   default 0
);

create index idx_aiu_sekai_session_table_user_id_sekai_id_index
    on aiu_sekai_session_table (user_id, sekai_id);

create index aiu_sekai_session_table_template_id_sekai_id_index
    on aiu_sekai_session_table (template_id, sekai_id);
