create table "aiu-character-table"
(
    "charId"            bigserial
        primary key,
    "charName"          text,
    "tagIds"            integer[],
    tags                json,
    "charDescription"   text,
    "charAvatarUrl"     text,
    "createTimeUtc"     bigint,
    "creatorUserId"     integer                not null,
    "delFlag"           integer                not null,
    "updateTime"        timestamp default now(),
    "isFamous"          integer   default 0,
    searchable          integer   default 1    not null,
    "creationType"      integer   default 0    not null,
    pose                json,
    language            text,
    "baseCharId"        bigint    default 0,
    enable              boolean   default false,
    voice               integer,
    "poseKeywords"      text,
    "bodyShape"         integer,
    complete            boolean,
    "charOrigin"        text      default ''::text,
    alias               text[]    default ARRAY []::text[],
    skins               json      default '{}'::json,
    "referenceImageUrl" text      default ''::text,
    weight              integer   default 0,
    public              boolean   default true,
    pronounce           text      default ''::text,
    "speakingStyle"     text      default ''::text,
    "baseCharType"      integer   default 0,
    "userCreatedPose"   json      default '{}'::json,
    "enableVoice"       boolean   default true not null,
    nsfw                boolean   default false,
    "hashTags"          integer[] default ARRAY []::integer[],
    background          text      default ''::text,
    "textNsfw"          boolean   default false,
    "imageNsfw"         boolean   default false,
    "operatorHashTags"  integer[] default ARRAY []::integer[],
    "aiHashTags"        integer[] default ARRAY []::integer[],
    "faceImage"         text
);
