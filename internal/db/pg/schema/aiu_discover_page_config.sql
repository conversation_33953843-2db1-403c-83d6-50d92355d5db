-- 创建 Discover 页面配置表
create table aiu_discover_page_config
(
    id         bigserial primary key,                                       -- 主键
    type       varchar(50)              not null,                           -- 配置类型，当前支持 sekai_campaign_banner, sekai_campaign_past
    data       jsonb                    not null,                           -- 业务数据JSON，sekai_campaign_banner,sekai_campaign_past字段：{title、subtitle、cover_image_url、secondary_page_image_url、linked_sekai_ids}
    status     varchar(20)              not null default 'active',          -- 状态：active/inactive
    sort_order integer                  not null default 0,                 -- 排序字段，数值越小越靠前
    created_at timestamp with time zone not null default current_timestamp, -- 创建时间
    updated_at timestamp with time zone not null default current_timestamp, -- 更新时间
    is_deleted boolean                  not null default false              -- 删除标记
);
-- 添加表注释
comment on table aiu_discover_page_config is 'Discover 页面配置表';
-- 添加列注释
comment on column aiu_discover_page_config.type is '配置类型，当前支持 sekai_campaign_banner, sekai_campaign_past';
comment on column aiu_discover_page_config.data is '业务数据JSON，sekai_campaign_banner,sekai_campaign_past字段：{title、subtitle、cover_image_url、secondary_page_image_url、linked_sekai_ids}';
comment on column aiu_discover_page_config.status is '状态：active/inactive';
comment on column aiu_discover_page_config.sort_order is '排序字段，数值越小越靠前';
comment on column aiu_discover_page_config.created_at is '创建时间';
comment on column aiu_discover_page_config.updated_at is '更新时间';
comment on column aiu_discover_page_config.is_deleted is '删除标记';
-- 创建索引
create index idx_aiu_discover_page_config_type_status_sort_order on aiu_discover_page_config (type, status, sort_order);
create index idx_aiu_discover_page_config_is_deleted on aiu_discover_page_config (is_deleted);
create index idx_aiu_discover_page_config_created_at on aiu_discover_page_config (created_at);
create index idx_aiu_discover_page_config_updated_at on aiu_discover_page_config (updated_at);