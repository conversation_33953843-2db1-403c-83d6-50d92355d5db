create table "aiu-voice-table"
(
    "voiceId"            bigint    default nextval('"aiu-voice-table_voiceId_seq"'::regclass) not null
        primary key,
    "displayName"        text,
    "sampleUrl"          text,
    "displayEnable"      boolean                                                              not null,
    "charName"           text,
    "labsTag"            text[],
    "labsId"             text,
    "rvcTag"             text[],
    "rvcId"              text,
    "creatorUserId"      bigint,
    "creationType"       integer                                                              not null,
    enable               boolean   default true,
    "createTime"         bigint,
    duration             integer,
    "rvcTranspose"       integer,
    "recordUrl"          text      default ''::text,
    weight               integer   default 0,
    "processedRecordUrl" text      default ''::text,
    "recordText"         text      default ''::text,
    categories           integer[] default ARRAY []::integer[],
    nsfw                 boolean   default false                                              not null,
    "accentId"           bigint    default 0
);

create index "ix_aiu-voice-table_creatorUserId"
    on "aiu-voice-table" ("creatorUserId");

create index idx_aiu_voice_table_categories
    on "aiu-voice-table" using gin (categories);


create table aiu_voice_accent_table
(
    id          bigint generated always as identity
        primary key,
    voice_name  text                                not null,
    accent_name text                                not null,
    is_delete   boolean   default false             not null,
    created_at  timestamp default CURRENT_TIMESTAMP not null,
    updated_at  timestamp default CURRENT_TIMESTAMP
);

create table aiu_voice_category
(
    id            serial
        primary key,
    name          varchar(128)                       not null,
    creation_type integer                  default 0 not null,
    weight        integer                  default 0 not null,
    cover_url     varchar(255)             default ''::character varying,
    created_at    timestamp with time zone default CURRENT_TIMESTAMP,
    updated_at    timestamp with time zone default CURRENT_TIMESTAMP,
    deleted_at    timestamp with time zone
);

create index aiu_voice_category_weight_index
    on aiu_voice_category (weight);

create index aiu_voice_category_deleted_at_index
    on aiu_voice_category (deleted_at);

create table aiu_user_like_voice
(
    id         serial
        primary key,
    user_id    bigint not null,
    voice_id   bigint not null,
    created_at timestamp with time zone default CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone
);

create unique index ix_aiu_user_like_voice_userid_voiceid
    on aiu_user_like_voice (user_id, voice_id);

create index ix_aiu_user_like_voice_voice_id
    on aiu_user_like_voice (voice_id);
