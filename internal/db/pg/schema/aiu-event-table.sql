create table "aiu-event-table"
(
    "eventId"              bigint  default nextval('"aiu-event-table_eventId_seq"'::regclass) not null
        primary key,
    "universeId"           bigint                                                             not null,
    "eventDescription"     text,
    "eventOrder"           integer                                                            not null,
    "createTimeUtc"        bigint,
    "creatorUserId"        bigint,
    chars                  json,
    "gifUrl"               text,
    video                  json,
    "backgroundKeywords"   text    default ''::text,
    "bgmTags"              text[]  default ARRAY []::text[],
    bgm                    integer default 0,
    expandation            json    default '{}'::json,
    "nextEventSuggestions" json    default '[]'::json
);


create index "ix_aiu-event-table_creatorUserId"
    on "aiu-event-table" ("creatorUserId");

create index "ix_aiu-event-table_universeId"
    on "aiu-event-table" ("universeId");

