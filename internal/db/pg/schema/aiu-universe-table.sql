create table "aiu-universe-table"
(
    "universeId"                  bigserial
        primary key,
    "charIds"                     integer[],
    "createTimeUtc"               bigint,
    "creatorUserId"               bigint,
    "delFlag"                     integer                       not null,
    "initializedChatRecords"      text,
    "updateTime"                  timestamp default now(),
    "universeDescriptionPublic"   integer   default 1,
    searchable                    integer   default 1           not null,
    "favoriteUserCount"           integer   default 0           not null,
    "creationType"                integer   default 0           not null,
    "recentDaysChatUserCount"     bigint    default '0'::bigint not null,
    chars                         json,
    enable                        boolean   default true        not null,
    bgm                           json,
    "creatorUserName"             text,
    "whatIf"                      text      default ''::text,
    background                    text      default ''::text,
    highlight                     json      default '{}'::json,
    "firstEventDescription"       text      default ''::text,
    "storyTellerStyle"            text      default ''::text,
    events                        json      default '[]'::json,
    "userCharId"                  bigint    default '0'::bigint,
    "firstEventSuggestions"       json      default '[]'::json,
    "initialRecords"              json      default '[]'::json,
    tags                          text[]    default '{}'::character varying[],
    "coverUrl"                    text,
    "baseUniverseId"              bigint    default '0'::bigint,
    "bgmVolume"                   integer   default 50,
    "selectedCharIds"             integer[] default ARRAY []::integer[],
    "selectedUserInsertedCharIds" integer[] default ARRAY []::integer[],
    "isDelete"                    boolean   default false,
    public                        boolean   default true,
    "appearedChars"               json      default '[]'::json  not null,
    nsfw                          boolean   default false,
    summary                       text[]    default ARRAY []::text[],
    source                        integer   default 0,
    "sourceDesc"                  text      default ''::text,
    "sourceId"                    integer   default 0,
    "messageId"                   integer   default 0,
    "backgroundImageInfo"         jsonb     default '{}'::jsonb,
    "gifUrl"                      text      default ''::text,
    "narratorVoiceId"             integer   default 0,
    "enableNarratorVoice"         boolean   default false,
    cta                           text      default ''::text,
    "bgmTag"                      text      default ''::text,
    background_keywords           text      default ''::text
);
