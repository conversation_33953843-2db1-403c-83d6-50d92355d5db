// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: bgm.sql

package pg

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const checkBGMsLikedByUser = `-- name: CheckBGMsLikedByUser :many
SELECT bgm_id, created_at FROM aiu_user_like_bgm
WHERE
    user_id = $1::int
    AND bgm_id = ANY($2::int[])
    AND is_delete = false
`

type CheckBGMsLikedByUserParams struct {
	UserID int32   `json:"userID"`
	BgmIDs []int32 `json:"bgmIDs"`
}

type CheckBGMsLikedByUserRow struct {
	BgmID     int64              `json:"bgm_id"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
}

// CheckBGMsLikedByUser 检查用户是否点赞了BGM，若点赞了则返回点赞时间
func (q *Queries) CheckBGMsLikedByUser(ctx context.Context, arg *CheckBGMsLikedByUserParams) ([]*CheckBGMsLikedByUserRow, error) {
	rows, err := q.db.Query(ctx, checkBGMsLikedByUser, arg.UserID, arg.BgmIDs)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*CheckBGMsLikedByUserRow
	for rows.Next() {
		var i CheckBGMsLikedByUserRow
		if err := rows.Scan(&i.BgmID, &i.CreatedAt); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const createBGM = `-- name: CreateBGM :one
INSERT INTO "aiu-bgm-table" (
    url,
    name,
    "coverUrl",
    duration,
    tags,
    "referenceCount",
    "creatorUserId",
    "creationType",
    enable,
    public,
    "createTimeUtc",
    "premiseIds",
    categories,
    nsfw
) VALUES (
    $1,
    $2,
    $3,
    $4,
    $5,
    $6,
    $7,
    $8,
    $9,
    $10,
    $11,
    $12,
    $13,
    $14
) RETURNING id, url, name, "coverUrl", duration, tags, "referenceCount", "creatorUserId", "creationType", enable, public, "createTimeUtc", "premiseIds", categories, nsfw
`

type CreateBGMParams struct {
	Url            pgtype.Text `json:"url"`
	Name           pgtype.Text `json:"name"`
	CoverUrl       pgtype.Text `json:"coverUrl"`
	Duration       pgtype.Int4 `json:"duration"`
	Tags           []string    `json:"tags"`
	ReferenceCount pgtype.Int4 `json:"referenceCount"`
	CreatorUserId  pgtype.Int8 `json:"creatorUserId"`
	CreationType   int32       `json:"creationType"`
	Enable         pgtype.Bool `json:"enable"`
	Public         bool        `json:"public"`
	CreateTimeUtc  pgtype.Int8 `json:"createTimeUtc"`
	PremiseIds     []string    `json:"premiseIds"`
	Categories     []int32     `json:"categories"`
	Nsfw           bool        `json:"nsfw"`
}

func (q *Queries) CreateBGM(ctx context.Context, arg *CreateBGMParams) (*AiuBgmTable, error) {
	row := q.db.QueryRow(ctx, createBGM,
		arg.Url,
		arg.Name,
		arg.CoverUrl,
		arg.Duration,
		arg.Tags,
		arg.ReferenceCount,
		arg.CreatorUserId,
		arg.CreationType,
		arg.Enable,
		arg.Public,
		arg.CreateTimeUtc,
		arg.PremiseIds,
		arg.Categories,
		arg.Nsfw,
	)
	var i AiuBgmTable
	err := row.Scan(
		&i.ID,
		&i.Url,
		&i.Name,
		&i.CoverUrl,
		&i.Duration,
		&i.Tags,
		&i.ReferenceCount,
		&i.CreatorUserId,
		&i.CreationType,
		&i.Enable,
		&i.Public,
		&i.CreateTimeUtc,
		&i.PremiseIds,
		&i.Categories,
		&i.Nsfw,
	)
	return &i, err
}

const getBGMByID = `-- name: GetBGMByID :one
SELECT id, url, name, "coverUrl", duration, tags, "referenceCount", "creatorUserId", "creationType", enable, public, "createTimeUtc", "premiseIds", categories, nsfw FROM "aiu-bgm-table"
WHERE
    id = $1
`

func (q *Queries) GetBGMByID(ctx context.Context, id int64) (*AiuBgmTable, error) {
	row := q.db.QueryRow(ctx, getBGMByID, id)
	var i AiuBgmTable
	err := row.Scan(
		&i.ID,
		&i.Url,
		&i.Name,
		&i.CoverUrl,
		&i.Duration,
		&i.Tags,
		&i.ReferenceCount,
		&i.CreatorUserId,
		&i.CreationType,
		&i.Enable,
		&i.Public,
		&i.CreateTimeUtc,
		&i.PremiseIds,
		&i.Categories,
		&i.Nsfw,
	)
	return &i, err
}

const getBGMByIDs = `-- name: GetBGMByIDs :many
SELECT id, url, name, "coverUrl", duration, tags, "referenceCount", "creatorUserId", "creationType", enable, public, "createTimeUtc", "premiseIds", categories, nsfw FROM "aiu-bgm-table"
WHERE
    id = ANY($1::int[])
    AND (nsfw = $2 OR NOT $3::boolean)
    AND (enable = $4 OR NOT $5::boolean)
    AND (public = $6 OR NOT $7::boolean)
`

type GetBGMByIDsParams struct {
	Ids            []int32     `json:"ids"`
	Nsfw           bool        `json:"nsfw"`
	FilterByNsfw   bool        `json:"filter_by_nsfw"`
	Enable         pgtype.Bool `json:"enable"`
	FilterByEnable bool        `json:"filter_by_enable"`
	Public         bool        `json:"public"`
	FilterByPublic bool        `json:"filter_by_public"`
}

func (q *Queries) GetBGMByIDs(ctx context.Context, arg *GetBGMByIDsParams) ([]*AiuBgmTable, error) {
	rows, err := q.db.Query(ctx, getBGMByIDs,
		arg.Ids,
		arg.Nsfw,
		arg.FilterByNsfw,
		arg.Enable,
		arg.FilterByEnable,
		arg.Public,
		arg.FilterByPublic,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuBgmTable
	for rows.Next() {
		var i AiuBgmTable
		if err := rows.Scan(
			&i.ID,
			&i.Url,
			&i.Name,
			&i.CoverUrl,
			&i.Duration,
			&i.Tags,
			&i.ReferenceCount,
			&i.CreatorUserId,
			&i.CreationType,
			&i.Enable,
			&i.Public,
			&i.CreateTimeUtc,
			&i.PremiseIds,
			&i.Categories,
			&i.Nsfw,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getBGMCategories = `-- name: GetBGMCategories :many
SELECT id, name, tag, cover_url, weight, creation_type, created_at, updated_at FROM aiu_bgm_category 
ORDER BY name ASC 
OFFSET ($1::int - 1) * $2::int
LIMIT $2::int
`

type GetBGMCategoriesParams struct {
	Page     int32 `json:"Page"`
	PageSize int32 `json:"PageSize"`
}

func (q *Queries) GetBGMCategories(ctx context.Context, arg *GetBGMCategoriesParams) ([]*AiuBgmCategory, error) {
	rows, err := q.db.Query(ctx, getBGMCategories, arg.Page, arg.PageSize)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuBgmCategory
	for rows.Next() {
		var i AiuBgmCategory
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Tag,
			&i.CoverUrl,
			&i.Weight,
			&i.CreationType,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getBGMCategoriesByIDs = `-- name: GetBGMCategoriesByIDs :many
SELECT id, name, tag, cover_url, weight, creation_type, created_at, updated_at FROM aiu_bgm_category 
WHERE id = ANY($1::int[])
`

func (q *Queries) GetBGMCategoriesByIDs(ctx context.Context, ids []int32) ([]*AiuBgmCategory, error) {
	rows, err := q.db.Query(ctx, getBGMCategoriesByIDs, ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuBgmCategory
	for rows.Next() {
		var i AiuBgmCategory
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Tag,
			&i.CoverUrl,
			&i.Weight,
			&i.CreationType,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getBGMCategoriesCount = `-- name: GetBGMCategoriesCount :one
SELECT COUNT(*) FROM aiu_bgm_category
`

func (q *Queries) GetBGMCategoriesCount(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, getBGMCategoriesCount)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getBGMCreatedByUser = `-- name: GetBGMCreatedByUser :many
SELECT id, url, name, "coverUrl", duration, tags, "referenceCount", "creatorUserId", "creationType", enable, public, "createTimeUtc", "premiseIds", categories, nsfw FROM "aiu-bgm-table"
WHERE
    "creatorUserId" = $1::int
    AND enable = true
    AND (public = true OR NOT $2::boolean)
ORDER BY
    "createTimeUtc" DESC
LIMIT $4::int 
OFFSET ($3::int - 1) * $4::int
`

type GetBGMCreatedByUserParams struct {
	QueryUserId    int32 `json:"queryUserId"`
	FilterByPublic bool  `json:"filter_by_public"`
	Page           int32 `json:"page"`
	PageSize       int32 `json:"pageSize"`
}

func (q *Queries) GetBGMCreatedByUser(ctx context.Context, arg *GetBGMCreatedByUserParams) ([]*AiuBgmTable, error) {
	rows, err := q.db.Query(ctx, getBGMCreatedByUser,
		arg.QueryUserId,
		arg.FilterByPublic,
		arg.Page,
		arg.PageSize,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuBgmTable
	for rows.Next() {
		var i AiuBgmTable
		if err := rows.Scan(
			&i.ID,
			&i.Url,
			&i.Name,
			&i.CoverUrl,
			&i.Duration,
			&i.Tags,
			&i.ReferenceCount,
			&i.CreatorUserId,
			&i.CreationType,
			&i.Enable,
			&i.Public,
			&i.CreateTimeUtc,
			&i.PremiseIds,
			&i.Categories,
			&i.Nsfw,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getBGMCreatedByUserCount = `-- name: GetBGMCreatedByUserCount :one
SELECT COUNT(*) FROM "aiu-bgm-table"
WHERE
    "creatorUserId" = $1::int
    AND enable = true
    AND (public = true OR NOT $2::boolean)
`

type GetBGMCreatedByUserCountParams struct {
	QueryUserId    int32 `json:"queryUserId"`
	FilterByPublic bool  `json:"filter_by_public"`
}

func (q *Queries) GetBGMCreatedByUserCount(ctx context.Context, arg *GetBGMCreatedByUserCountParams) (int64, error) {
	row := q.db.QueryRow(ctx, getBGMCreatedByUserCount, arg.QueryUserId, arg.FilterByPublic)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getBGMsByCategory = `-- name: GetBGMsByCategory :many
SELECT id, url, name, "coverUrl", duration, tags, "referenceCount", "creatorUserId", "creationType", enable, public, "createTimeUtc", "premiseIds", categories, nsfw
FROM "aiu-bgm-table"
WHERE
    $1::int = ANY(categories)
    AND enable = true
    AND public = true
ORDER BY "createTimeUtc" DESC
LIMIT $3::int
OFFSET ($2::int - 1) * $3::int
`

type GetBGMsByCategoryParams struct {
	CategoryID int32 `json:"categoryID"`
	Page       int32 `json:"page"`
	PageSize   int32 `json:"pageSize"`
}

// GetBGMsByCategory returns paginated BGMs in a specific category
func (q *Queries) GetBGMsByCategory(ctx context.Context, arg *GetBGMsByCategoryParams) ([]*AiuBgmTable, error) {
	rows, err := q.db.Query(ctx, getBGMsByCategory, arg.CategoryID, arg.Page, arg.PageSize)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuBgmTable
	for rows.Next() {
		var i AiuBgmTable
		if err := rows.Scan(
			&i.ID,
			&i.Url,
			&i.Name,
			&i.CoverUrl,
			&i.Duration,
			&i.Tags,
			&i.ReferenceCount,
			&i.CreatorUserId,
			&i.CreationType,
			&i.Enable,
			&i.Public,
			&i.CreateTimeUtc,
			&i.PremiseIds,
			&i.Categories,
			&i.Nsfw,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getBGMsByCategoryCount = `-- name: GetBGMsByCategoryCount :one
SELECT COUNT(*)
FROM "aiu-bgm-table"
WHERE
    $1::int = ANY(categories)
    AND enable = true
    AND public = true
`

// GetBGMsByCategoryCount returns the total count of BGMs in a specific category
func (q *Queries) GetBGMsByCategoryCount(ctx context.Context, categoryid int32) (int64, error) {
	row := q.db.QueryRow(ctx, getBGMsByCategoryCount, categoryid)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getUserLikedBGMs = `-- name: GetUserLikedBGMs :many
SELECT bgm.id, bgm.url, bgm.name, bgm."coverUrl", bgm.duration, bgm.tags, bgm."referenceCount", bgm."creatorUserId", bgm."creationType", bgm.enable, bgm.public, bgm."createTimeUtc", bgm."premiseIds", bgm.categories, bgm.nsfw
FROM "aiu-bgm-table" bgm
JOIN aiu_user_like_bgm ON 
    aiu_user_like_bgm.user_id = $1::int 
    AND aiu_user_like_bgm.bgm_id = bgm.id 
    AND aiu_user_like_bgm.is_delete = false
WHERE 
    -- 如果查询用户ID与当前用户ID不同，需要检查public字段
    bgm.public = true OR NOT $2::boolean
ORDER BY aiu_user_like_bgm.created_at DESC
LIMIT $4::int
OFFSET ($3::int - 1) * $4::int
`

type GetUserLikedBGMsParams struct {
	UserID         int32 `json:"userID"`
	FilterByPublic bool  `json:"filter_by_public"`
	Page           int32 `json:"page"`
	PageSize       int32 `json:"pageSize"`
}

// GetUserLikedBGMs 获取用户点赞的BGM
func (q *Queries) GetUserLikedBGMs(ctx context.Context, arg *GetUserLikedBGMsParams) ([]*AiuBgmTable, error) {
	rows, err := q.db.Query(ctx, getUserLikedBGMs,
		arg.UserID,
		arg.FilterByPublic,
		arg.Page,
		arg.PageSize,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuBgmTable
	for rows.Next() {
		var i AiuBgmTable
		if err := rows.Scan(
			&i.ID,
			&i.Url,
			&i.Name,
			&i.CoverUrl,
			&i.Duration,
			&i.Tags,
			&i.ReferenceCount,
			&i.CreatorUserId,
			&i.CreationType,
			&i.Enable,
			&i.Public,
			&i.CreateTimeUtc,
			&i.PremiseIds,
			&i.Categories,
			&i.Nsfw,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserLikedBGMsCount = `-- name: GetUserLikedBGMsCount :one
SELECT COUNT(*)
FROM "aiu-bgm-table" bgm
JOIN aiu_user_like_bgm ON 
    aiu_user_like_bgm.user_id = $1::int 
    AND aiu_user_like_bgm.bgm_id = bgm.id 
    AND aiu_user_like_bgm.is_delete = false
WHERE 
    -- 如果查询用户ID与当前用户ID不同，需要检查public字段
    bgm.public = true OR NOT $2::boolean
`

type GetUserLikedBGMsCountParams struct {
	UserID         int32 `json:"userID"`
	FilterByPublic bool  `json:"filter_by_public"`
}

// GetUserLikedBGMsCount 获取用户点赞的BGM数量
func (q *Queries) GetUserLikedBGMsCount(ctx context.Context, arg *GetUserLikedBGMsCountParams) (int64, error) {
	row := q.db.QueryRow(ctx, getUserLikedBGMsCount, arg.UserID, arg.FilterByPublic)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const softDeleteBGM = `-- name: SoftDeleteBGM :one
UPDATE "aiu-bgm-table"
SET
    enable = false
WHERE
    id = $1 AND "creatorUserId" = $2 AND enable = true
RETURNING id, url, name, "coverUrl", duration, tags, "referenceCount", "creatorUserId", "creationType", enable, public, "createTimeUtc", "premiseIds", categories, nsfw
`

type SoftDeleteBGMParams struct {
	ID            int64       `json:"id"`
	CreatorUserId pgtype.Int8 `json:"creatorUserId"`
}

func (q *Queries) SoftDeleteBGM(ctx context.Context, arg *SoftDeleteBGMParams) (*AiuBgmTable, error) {
	row := q.db.QueryRow(ctx, softDeleteBGM, arg.ID, arg.CreatorUserId)
	var i AiuBgmTable
	err := row.Scan(
		&i.ID,
		&i.Url,
		&i.Name,
		&i.CoverUrl,
		&i.Duration,
		&i.Tags,
		&i.ReferenceCount,
		&i.CreatorUserId,
		&i.CreationType,
		&i.Enable,
		&i.Public,
		&i.CreateTimeUtc,
		&i.PremiseIds,
		&i.Categories,
		&i.Nsfw,
	)
	return &i, err
}

const updateBGM = `-- name: UpdateBGM :one
UPDATE "aiu-bgm-table"
SET
    name = CASE 
        WHEN $1::text IS NOT NULL THEN $1::text 
        ELSE name 
    END,
    url = CASE 
        WHEN $2::text IS NOT NULL THEN $2::text 
        ELSE url 
    END,
    "coverUrl" = CASE 
        WHEN $3::text IS NOT NULL THEN $3::text 
        ELSE "coverUrl" 
    END,
    duration = CASE 
        WHEN $4::integer IS NOT NULL THEN $4::integer 
        ELSE duration 
    END,
    tags = CASE 
        WHEN $5::text[] IS NOT NULL THEN $5::text[] 
        ELSE tags 
    END,
    public = CASE 
        WHEN $6::boolean IS NOT NULL THEN $6::boolean 
        ELSE public 
    END,
    categories = CASE 
        WHEN $7::integer[] IS NOT NULL THEN $7::integer[] 
        ELSE categories 
    END,
    "premiseIds" = CASE 
        WHEN $8::text[] IS NOT NULL THEN $8::text[] 
        ELSE "premiseIds" 
    END,
    "createTimeUtc" = CASE 
        WHEN $9::bigint IS NOT NULL THEN $9::bigint 
        ELSE "createTimeUtc" 
    END
WHERE
    id = $10
    AND "creatorUserId" = $11
    AND enable = true
RETURNING id, url, name, "coverUrl", duration, tags, "referenceCount", "creatorUserId", "creationType", enable, public, "createTimeUtc", "premiseIds", categories, nsfw
`

type UpdateBGMParams struct {
	Name          pgtype.Text `json:"name"`
	URL           pgtype.Text `json:"URL"`
	CoverURL      pgtype.Text `json:"coverURL"`
	Duration      pgtype.Int4 `json:"duration"`
	Tags          []string    `json:"tags"`
	Public        pgtype.Bool `json:"public"`
	Categories    []int32     `json:"categories"`
	PremiseIDs    []string    `json:"premiseIDs"`
	CreateTimeUTC pgtype.Int8 `json:"createTimeUTC"`
	ID            int64       `json:"ID"`
	CreatorUserID pgtype.Int8 `json:"creatorUserID"`
}

// UpdateBGM updates the BGM information by ID
func (q *Queries) UpdateBGM(ctx context.Context, arg *UpdateBGMParams) (*AiuBgmTable, error) {
	row := q.db.QueryRow(ctx, updateBGM,
		arg.Name,
		arg.URL,
		arg.CoverURL,
		arg.Duration,
		arg.Tags,
		arg.Public,
		arg.Categories,
		arg.PremiseIDs,
		arg.CreateTimeUTC,
		arg.ID,
		arg.CreatorUserID,
	)
	var i AiuBgmTable
	err := row.Scan(
		&i.ID,
		&i.Url,
		&i.Name,
		&i.CoverUrl,
		&i.Duration,
		&i.Tags,
		&i.ReferenceCount,
		&i.CreatorUserId,
		&i.CreationType,
		&i.Enable,
		&i.Public,
		&i.CreateTimeUtc,
		&i.PremiseIds,
		&i.Categories,
		&i.Nsfw,
	)
	return &i, err
}
