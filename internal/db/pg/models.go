// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0

package pg

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type AiuBgmCategory struct {
	ID           int64            `json:"id"`
	Name         string           `json:"name"`
	Tag          pgtype.Text      `json:"tag"`
	CoverUrl     pgtype.Text      `json:"cover_url"`
	Weight       int32            `json:"weight"`
	CreationType int32            `json:"creation_type"`
	CreatedAt    pgtype.Timestamp `json:"created_at"`
	UpdatedAt    pgtype.Timestamp `json:"updated_at"`
}

type AiuBgmTable struct {
	ID             int64       `json:"id"`
	Url            pgtype.Text `json:"url"`
	Name           pgtype.Text `json:"name"`
	CoverUrl       pgtype.Text `json:"coverUrl"`
	Duration       pgtype.Int4 `json:"duration"`
	Tags           []string    `json:"tags"`
	ReferenceCount pgtype.Int4 `json:"referenceCount"`
	CreatorUserId  pgtype.Int8 `json:"creatorUserId"`
	CreationType   int32       `json:"creationType"`
	Enable         pgtype.Bool `json:"enable"`
	Public         bool        `json:"public"`
	CreateTimeUtc  pgtype.Int8 `json:"createTimeUtc"`
	PremiseIds     []string    `json:"premiseIds"`
	Categories     []int32     `json:"categories"`
	Nsfw           bool        `json:"nsfw"`
}

type AiuCharacterTable struct {
	CharId            int64            `json:"charId"`
	CharName          pgtype.Text      `json:"charName"`
	TagIds            []int32          `json:"tagIds"`
	Tags              []byte           `json:"tags"`
	CharDescription   pgtype.Text      `json:"charDescription"`
	CharAvatarUrl     pgtype.Text      `json:"charAvatarUrl"`
	CreateTimeUtc     pgtype.Int8      `json:"createTimeUtc"`
	CreatorUserId     int32            `json:"creatorUserId"`
	DelFlag           int32            `json:"delFlag"`
	UpdateTime        pgtype.Timestamp `json:"updateTime"`
	IsFamous          pgtype.Int4      `json:"isFamous"`
	Searchable        int32            `json:"searchable"`
	CreationType      int32            `json:"creationType"`
	Pose              []byte           `json:"pose"`
	Language          pgtype.Text      `json:"language"`
	BaseCharId        pgtype.Int8      `json:"baseCharId"`
	Enable            pgtype.Bool      `json:"enable"`
	Voice             pgtype.Int4      `json:"voice"`
	PoseKeywords      pgtype.Text      `json:"poseKeywords"`
	BodyShape         pgtype.Int4      `json:"bodyShape"`
	Complete          pgtype.Bool      `json:"complete"`
	CharOrigin        pgtype.Text      `json:"charOrigin"`
	Alias             []string         `json:"alias"`
	Skins             []byte           `json:"skins"`
	ReferenceImageUrl pgtype.Text      `json:"referenceImageUrl"`
	Weight            pgtype.Int4      `json:"weight"`
	Public            pgtype.Bool      `json:"public"`
	Pronounce         pgtype.Text      `json:"pronounce"`
	SpeakingStyle     pgtype.Text      `json:"speakingStyle"`
	BaseCharType      pgtype.Int4      `json:"baseCharType"`
	UserCreatedPose   []byte           `json:"userCreatedPose"`
	EnableVoice       bool             `json:"enableVoice"`
	Nsfw              pgtype.Bool      `json:"nsfw"`
	HashTags          []int32          `json:"hashTags"`
	Background        pgtype.Text      `json:"background"`
	TextNsfw          pgtype.Bool      `json:"textNsfw"`
	ImageNsfw         pgtype.Bool      `json:"imageNsfw"`
	OperatorHashTags  []int32          `json:"operatorHashTags"`
	AiHashTags        []int32          `json:"aiHashTags"`
	FaceImage         pgtype.Text      `json:"faceImage"`
}

// Discover 页面配置表
type AiuDiscoverPageConfig struct {
	ID int64 `json:"id"`
	// 配置类型，当前支持 sekai_campaign_banner, sekai_campaign_past
	Type string `json:"type"`
	// 业务数据JSON，sekai_campaign_banner,sekai_campaign_past字段：{title、subtitle、cover_image_url、secondary_page_image_url、linked_sekai_ids}
	Data []byte `json:"data"`
	// 状态：active/inactive
	Status string `json:"status"`
	// 排序字段，数值越小越靠前
	SortOrder int32 `json:"sort_order"`
	// 创建时间
	CreatedAt pgtype.Timestamptz `json:"created_at"`
	// 更新时间
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// 删除标记
	IsDeleted bool `json:"is_deleted"`
}

type AiuEventTable struct {
	EventId              int64       `json:"eventId"`
	UniverseId           int64       `json:"universeId"`
	EventDescription     pgtype.Text `json:"eventDescription"`
	EventOrder           int32       `json:"eventOrder"`
	CreateTimeUtc        pgtype.Int8 `json:"createTimeUtc"`
	CreatorUserId        pgtype.Int8 `json:"creatorUserId"`
	Chars                []byte      `json:"chars"`
	GifUrl               pgtype.Text `json:"gifUrl"`
	Video                []byte      `json:"video"`
	BackgroundKeywords   pgtype.Text `json:"backgroundKeywords"`
	BgmTags              []string    `json:"bgmTags"`
	Bgm                  pgtype.Int4 `json:"bgm"`
	Expandation          []byte      `json:"expandation"`
	NextEventSuggestions []byte      `json:"nextEventSuggestions"`
}

type AiuLabelTable struct {
	ID                     int32            `json:"id"`
	LabelName              string           `json:"label_name"`
	DisplayName            pgtype.Text      `json:"display_name"`
	ParentID               pgtype.Int4      `json:"parent_id"`
	Emoji                  string           `json:"emoji"`
	CreatedAt              pgtype.Timestamp `json:"created_at"`
	UpdatedAt              pgtype.Timestamp `json:"updated_at"`
	OrderNum               int32            `json:"order_num"`
	ShowInForYou           bool             `json:"show_in_for_you"`
	ShowInNewUserSelection bool             `json:"show_in_new_user_selection"`
}

type AiuSekaiInfoTable struct {
	SekaiID                     int64            `json:"sekai_id"`
	SelectedCharIds             []int32          `json:"selected_char_ids"`
	SelectedUserInsertedCharIds []int32          `json:"selected_user_inserted_char_ids"`
	WhatIf                      pgtype.Text      `json:"what_if"`
	CharIds                     []int32          `json:"char_ids"`
	UserCharID                  pgtype.Int8      `json:"user_char_id"`
	ActionButton                pgtype.Text      `json:"action_button"`
	BackgroundImage             pgtype.Text      `json:"background_image"`
	BackgroundKeywords          pgtype.Text      `json:"background_keywords"`
	Title                       pgtype.Text      `json:"title"`
	Intro                       pgtype.Text      `json:"intro"`
	Bgm                         pgtype.Int4      `json:"bgm"`
	NarratorVoiceID             pgtype.Int8      `json:"narrator_voice_id"`
	EnableNarratorVoice         pgtype.Bool      `json:"enable_narrator_voice"`
	CreatedAt                   pgtype.Timestamp `json:"created_at"`
	UpdatedAt                   pgtype.Timestamp `json:"updated_at"`
	DeletedAt                   pgtype.Timestamp `json:"deleted_at"`
	CreatorUserID               pgtype.Int8      `json:"creator_user_id"`
	BgmVolume                   pgtype.Int8      `json:"bgm_volume"`
	CreatorUserName             pgtype.Text      `json:"creator_user_name"`
	SekaiType                   pgtype.Int4      `json:"sekai_type"`
	CoverUrl                    pgtype.Text      `json:"cover_url"`
	SourceID                    pgtype.Int8      `json:"source_id"`
	IsPublic                    pgtype.Bool      `json:"is_public"`
	AiHashTags                  []int32          `json:"ai_hash_tags"`
	SelectedUserCharID          int64            `json:"selected_user_char_id"`
	Published                   pgtype.Bool      `json:"published"`
	ImageNsfw                   bool             `json:"image_nsfw"`
	TextNsfw                    bool             `json:"text_nsfw"`
	Nsfw                        bool             `json:"nsfw"`
	SelectedCopyCharIds         []int32          `json:"selected_copy_char_ids"`
	Summary                     []string         `json:"summary"`
	BackgroundImageInfo         []byte           `json:"background_image_info"`
	BgmTag                      pgtype.Text      `json:"bgm_tag"`
	Greeting                    []byte           `json:"greeting"`
	PlayerInfo                  []byte           `json:"player_info"`
	Extra                       []byte           `json:"extra"`
	TemplateID                  pgtype.Int8      `json:"template_id"`
	ShowSettings                pgtype.Bool      `json:"show_settings"`
	HashTags                    []int32          `json:"hash_tags"`
	MinAppVersion               pgtype.Int4      `json:"min_app_version"`
	BackgroundImageType         pgtype.Int4      `json:"background_image_type"`
	CommentCount                pgtype.Int4      `json:"comment_count"`
	AiCategory                  []int64          `json:"ai_category"`
	UserCategory                []int64          `json:"user_category"`
	CoverType                   pgtype.Int4      `json:"cover_type"`
	CoverImage                  pgtype.Text      `json:"cover_image"`
	CoverChar                   pgtype.Int4      `json:"cover_char"`
}

type AiuSekaiSessionTable struct {
	ID            int64              `json:"id"`
	UserID        int64              `json:"user_id"`
	SekaiID       int64              `json:"sekai_id"`
	SekaiTitle    pgtype.Text        `json:"sekai_title"`
	BgUrl         pgtype.Text        `json:"bg_url"`
	GreetingUrl   pgtype.Text        `json:"greeting_url"`
	Records       []byte             `json:"records"`
	Extra         []byte             `json:"extra"`
	LastMessageID int64              `json:"last_message_id"`
	LastMessage   pgtype.Text        `json:"last_message"`
	LastMessageAt pgtype.Timestamptz `json:"last_message_at"`
	CreatedAt     pgtype.Timestamptz `json:"created_at"`
	UpdatedAt     pgtype.Timestamptz `json:"updated_at"`
	DeletedAt     pgtype.Timestamptz `json:"deleted_at"`
	Summary       []byte             `json:"summary"`
	TemplateID    pgtype.Int8        `json:"template_id"`
}

type AiuUniverseTable struct {
	UniverseId                  int64            `json:"universeId"`
	CharIds                     []int32          `json:"charIds"`
	CreateTimeUtc               pgtype.Int8      `json:"createTimeUtc"`
	CreatorUserId               pgtype.Int8      `json:"creatorUserId"`
	DelFlag                     int32            `json:"delFlag"`
	InitializedChatRecords      pgtype.Text      `json:"initializedChatRecords"`
	UpdateTime                  pgtype.Timestamp `json:"updateTime"`
	UniverseDescriptionPublic   pgtype.Int4      `json:"universeDescriptionPublic"`
	Searchable                  int32            `json:"searchable"`
	FavoriteUserCount           int32            `json:"favoriteUserCount"`
	CreationType                int32            `json:"creationType"`
	RecentDaysChatUserCount     int64            `json:"recentDaysChatUserCount"`
	Chars                       []byte           `json:"chars"`
	Enable                      bool             `json:"enable"`
	Bgm                         []byte           `json:"bgm"`
	CreatorUserName             pgtype.Text      `json:"creatorUserName"`
	WhatIf                      pgtype.Text      `json:"whatIf"`
	Background                  pgtype.Text      `json:"background"`
	Highlight                   []byte           `json:"highlight"`
	FirstEventDescription       pgtype.Text      `json:"firstEventDescription"`
	StoryTellerStyle            pgtype.Text      `json:"storyTellerStyle"`
	Events                      []byte           `json:"events"`
	UserCharId                  pgtype.Int8      `json:"userCharId"`
	FirstEventSuggestions       []byte           `json:"firstEventSuggestions"`
	InitialRecords              []byte           `json:"initialRecords"`
	Tags                        []string         `json:"tags"`
	CoverUrl                    pgtype.Text      `json:"coverUrl"`
	BaseUniverseId              pgtype.Int8      `json:"baseUniverseId"`
	BgmVolume                   pgtype.Int4      `json:"bgmVolume"`
	SelectedCharIds             []int32          `json:"selectedCharIds"`
	SelectedUserInsertedCharIds []int32          `json:"selectedUserInsertedCharIds"`
	IsDelete                    pgtype.Bool      `json:"isDelete"`
	Public                      pgtype.Bool      `json:"public"`
	AppearedChars               []byte           `json:"appearedChars"`
	Nsfw                        pgtype.Bool      `json:"nsfw"`
	Summary                     []string         `json:"summary"`
	Source                      pgtype.Int4      `json:"source"`
	SourceDesc                  pgtype.Text      `json:"sourceDesc"`
	SourceId                    pgtype.Int4      `json:"sourceId"`
	MessageId                   pgtype.Int4      `json:"messageId"`
	BackgroundImageInfo         []byte           `json:"backgroundImageInfo"`
	GifUrl                      pgtype.Text      `json:"gifUrl"`
	NarratorVoiceId             pgtype.Int4      `json:"narratorVoiceId"`
	EnableNarratorVoice         pgtype.Bool      `json:"enableNarratorVoice"`
	Cta                         pgtype.Text      `json:"cta"`
	BgmTag                      pgtype.Text      `json:"bgmTag"`
	BackgroundKeywords          pgtype.Text      `json:"background_keywords"`
}

type AiuUserLikeBgm struct {
	ID        int32              `json:"id"`
	UserID    int64              `json:"user_id"`
	BgmID     int64              `json:"bgm_id"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
	IsDelete  pgtype.Bool        `json:"is_delete"`
}

type AiuUserLikeTable struct {
	ID          int64            `json:"id"`
	UserID      int64            `json:"user_id"`
	ElementType string           `json:"element_type"`
	ElementID   int64            `json:"element_id"`
	CreatedAt   pgtype.Timestamp `json:"created_at"`
	DeletedAt   pgtype.Timestamp `json:"deleted_at"`
}

type AiuUserLikeVoice struct {
	ID        int32              `json:"id"`
	UserID    int64              `json:"user_id"`
	VoiceID   int64              `json:"voice_id"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type AiuVoiceAccentTable struct {
	ID         int64            `json:"id"`
	VoiceName  string           `json:"voice_name"`
	AccentName string           `json:"accent_name"`
	IsDelete   bool             `json:"is_delete"`
	CreatedAt  pgtype.Timestamp `json:"created_at"`
	UpdatedAt  pgtype.Timestamp `json:"updated_at"`
}

type AiuVoiceCategory struct {
	ID           int32              `json:"id"`
	Name         string             `json:"name"`
	CreationType int32              `json:"creation_type"`
	Weight       int32              `json:"weight"`
	CoverUrl     pgtype.Text        `json:"cover_url"`
	CreatedAt    pgtype.Timestamptz `json:"created_at"`
	UpdatedAt    pgtype.Timestamptz `json:"updated_at"`
	DeletedAt    pgtype.Timestamptz `json:"deleted_at"`
}

type AiuVoiceTable struct {
	VoiceId            int64       `json:"voiceId"`
	DisplayName        pgtype.Text `json:"displayName"`
	SampleUrl          pgtype.Text `json:"sampleUrl"`
	DisplayEnable      bool        `json:"displayEnable"`
	CharName           pgtype.Text `json:"charName"`
	LabsTag            []string    `json:"labsTag"`
	LabsId             pgtype.Text `json:"labsId"`
	RvcTag             []string    `json:"rvcTag"`
	RvcId              pgtype.Text `json:"rvcId"`
	CreatorUserId      pgtype.Int8 `json:"creatorUserId"`
	CreationType       int32       `json:"creationType"`
	Enable             pgtype.Bool `json:"enable"`
	CreateTime         pgtype.Int8 `json:"createTime"`
	Duration           pgtype.Int4 `json:"duration"`
	RvcTranspose       pgtype.Int4 `json:"rvcTranspose"`
	RecordUrl          pgtype.Text `json:"recordUrl"`
	Weight             pgtype.Int4 `json:"weight"`
	ProcessedRecordUrl pgtype.Text `json:"processedRecordUrl"`
	RecordText         pgtype.Text `json:"recordText"`
	Categories         []int32     `json:"categories"`
	Nsfw               bool        `json:"nsfw"`
	AccentId           pgtype.Int8 `json:"accentId"`
}
