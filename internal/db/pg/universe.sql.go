// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: universe.sql

package pg

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const getEventGifURL = `-- name: GetEventGifURL :many
SELECT "eventId", "gifUrl" FROM "aiu-event-table" WHERE "eventId" = ANY($1::bigint[])
`

type GetEventGifURLRow struct {
	EventId int64       `json:"eventId"`
	GifUrl  pgtype.Text `json:"gifUrl"`
}

func (q *Queries) GetEventGifURL(ctx context.Context, eventID []int64) ([]*GetEventGifURLRow, error) {
	rows, err := q.db.Query(ctx, getEventGifURL, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*GetEventGifURLRow
	for rows.Next() {
		var i GetEventGifURLRow
		if err := rows.Scan(&i.EventId, &i.GifUrl); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getStoriesByIDs = `-- name: GetStoriesByIDs :many
SELECT 
    "universeId", "charIds", "createTimeUtc", "creatorUserId", "delFlag", "initializedChatRecords", "updateTime", "universeDescriptionPublic", searchable, "favoriteUserCount", "creationType", "recentDaysChatUserCount", chars, enable, bgm, "creatorUserName", "whatIf", background, highlight, "firstEventDescription", "storyTellerStyle", events, "userCharId", "firstEventSuggestions", "initialRecords", tags, "coverUrl", "baseUniverseId", "bgmVolume", "selectedCharIds", "selectedUserInsertedCharIds", "isDelete", public, "appearedChars", nsfw, summary, source, "sourceDesc", "sourceId", "messageId", "backgroundImageInfo", "gifUrl", "narratorVoiceId", "enableNarratorVoice", cta, "bgmTag", background_keywords
FROM 
    "aiu-universe-table"
WHERE 
    "universeId" = ANY($1::bigint[])
    AND (public = $2 OR NOT $3::boolean)
    AND "isDelete" = false
ORDER BY 
    array_position($1::bigint[], "universeId")
`

type GetStoriesByIDsParams struct {
	StoryIds       []int64     `json:"story_ids"`
	IsPublic       pgtype.Bool `json:"is_public"`
	FilterByPublic bool        `json:"filter_by_public"`
}

func (q *Queries) GetStoriesByIDs(ctx context.Context, arg *GetStoriesByIDsParams) ([]*AiuUniverseTable, error) {
	rows, err := q.db.Query(ctx, getStoriesByIDs, arg.StoryIds, arg.IsPublic, arg.FilterByPublic)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*AiuUniverseTable
	for rows.Next() {
		var i AiuUniverseTable
		if err := rows.Scan(
			&i.UniverseId,
			&i.CharIds,
			&i.CreateTimeUtc,
			&i.CreatorUserId,
			&i.DelFlag,
			&i.InitializedChatRecords,
			&i.UpdateTime,
			&i.UniverseDescriptionPublic,
			&i.Searchable,
			&i.FavoriteUserCount,
			&i.CreationType,
			&i.RecentDaysChatUserCount,
			&i.Chars,
			&i.Enable,
			&i.Bgm,
			&i.CreatorUserName,
			&i.WhatIf,
			&i.Background,
			&i.Highlight,
			&i.FirstEventDescription,
			&i.StoryTellerStyle,
			&i.Events,
			&i.UserCharId,
			&i.FirstEventSuggestions,
			&i.InitialRecords,
			&i.Tags,
			&i.CoverUrl,
			&i.BaseUniverseId,
			&i.BgmVolume,
			&i.SelectedCharIds,
			&i.SelectedUserInsertedCharIds,
			&i.IsDelete,
			&i.Public,
			&i.AppearedChars,
			&i.Nsfw,
			&i.Summary,
			&i.Source,
			&i.SourceDesc,
			&i.SourceId,
			&i.MessageId,
			&i.BackgroundImageInfo,
			&i.GifUrl,
			&i.NarratorVoiceId,
			&i.EnableNarratorVoice,
			&i.Cta,
			&i.BgmTag,
			&i.BackgroundKeywords,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
