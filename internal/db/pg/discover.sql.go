// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: discover.sql

package pg

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const countDiscoverPageConfigByType = `-- name: CountDiscoverPageConfigByType :one
SELECT
    count(*)
FROM
    aiu_discover_page_config
WHERE
    type = $1
  AND status = 'active'
  AND is_deleted = false
`

// CountDiscoverPageConfigByType 根据类型统计发现页面配置的总数。仅统计状态为 active 且未被删除的配置。
func (q *Queries) CountDiscoverPageConfigByType(ctx context.Context, type_ string) (int64, error) {
	row := q.db.QueryRow(ctx, countDiscoverPageConfigByType, type_)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getDiscoverPageConfigByType = `-- name: GetDiscoverPageConfigByType :many
SELECT
    id,
    type,
    data,
    status,
    sort_order,
    created_at,
    updated_at
FROM
    aiu_discover_page_config
WHERE
    type = $1
  AND status = 'active'
  AND is_deleted = false
ORDER BY
    sort_order ASC,
    created_at DESC
LIMIT $3::int
OFFSET ($2::int - 1) * $3::int
`

type GetDiscoverPageConfigByTypeParams struct {
	Type     string `json:"type"`
	Page     int32  `json:"page"`
	PageSize int32  `json:"pageSize"`
}

type GetDiscoverPageConfigByTypeRow struct {
	ID        int64              `json:"id"`
	Type      string             `json:"type"`
	Data      []byte             `json:"data"`
	Status    string             `json:"status"`
	SortOrder int32              `json:"sort_order"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
}

// GetDiscoverPageConfigByType 根据类型查询发现页面配置，支持分页。仅检索状态为 active 且未被删除的配置，按 sort_order 升序，然后按 created_at 降序排列。
func (q *Queries) GetDiscoverPageConfigByType(ctx context.Context, arg *GetDiscoverPageConfigByTypeParams) ([]*GetDiscoverPageConfigByTypeRow, error) {
	rows, err := q.db.Query(ctx, getDiscoverPageConfigByType, arg.Type, arg.Page, arg.PageSize)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*GetDiscoverPageConfigByTypeRow
	for rows.Next() {
		var i GetDiscoverPageConfigByTypeRow
		if err := rows.Scan(
			&i.ID,
			&i.Type,
			&i.Data,
			&i.Status,
			&i.SortOrder,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
