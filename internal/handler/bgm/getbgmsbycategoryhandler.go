package bgm

import (
	"net/http"

	"github.com/sekai-app/sekai-go/internal/logic/bgm"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetBGMsByCategoryHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetBGMsByCategoryReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := bgm.NewGetBGMsByCategoryLogic(r.Context(), svcCtx)
		resp, err := l.GetBGMsByCategory(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
