package sekai

import (
	"fmt"
	"net/http"
	"time"

	"github.com/sekai-app/sekai-go/internal/crates/servermsg"
	"github.com/sekai-app/sekai-go/internal/logic/sekai"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/zeromicro/go-zero/core/collection"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 接口缓存中间件没有实现按照 appversion 进行缓存
// 这里为接口自定义缓存逻辑
var cache *collection.Cache

func init() {
	var err error
	cache, err = collection.NewCache(30*time.Minute, collection.WithLimit(2000))
	if err != nil {
		panic(err)
	}
}

func getHotSekaiCacheKey(r *http.Request) string {
	appVersion := servermsg.Get(r.Context()).AppVersion
	key := fmt.Sprintf("appversion:%s-%s", appVersion, r.URL.String())
	return key
}

func GetHotSekaiHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		val, ok := cache.Get(getHotSekaiCacheKey(r))
		if ok && val != nil {
			w.Header().Add("X-Api-Cache", "HIT")
			httpx.OkJsonCtx(r.Context(), w, val)
			return
		}

		var req types.GetHotSekaiReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := sekai.NewGetHotSekaiLogic(r.Context(), svcCtx)
		resp, err := l.GetHotSekai(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
			cache.Set(getHotSekaiCacheKey(r), resp)
		}
	}
}
