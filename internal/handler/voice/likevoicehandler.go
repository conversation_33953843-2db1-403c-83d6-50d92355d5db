package voice

import (
	"net/http"

	"github.com/sekai-app/sekai-go/internal/logic/voice"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 点赞/取消点赞语音，代替原来 likeVoice、unlikeVoice 两个接口
func LikeVoiceHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.LikeVoiceReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := voice.NewLikeVoiceLogic(r.Context(), svcCtx)
		resp, err := l.LikeVoice(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
