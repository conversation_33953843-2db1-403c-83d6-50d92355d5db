// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.1

package handler

import (
	"net/http"
	"time"

	bgm "github.com/sekai-app/sekai-go/internal/handler/bgm"
	common "github.com/sekai-app/sekai-go/internal/handler/common"
	discover "github.com/sekai-app/sekai-go/internal/handler/discover"
	sekai "github.com/sekai-app/sekai-go/internal/handler/sekai"
	story "github.com/sekai-app/sekai-go/internal/handler/story"
	user "github.com/sekai-app/sekai-go/internal/handler/user"
	voice "github.com/sekai-app/sekai-go/internal/handler/voice"
	"github.com/sekai-app/sekai-go/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// 创建BGM
				Method:  http.MethodPost,
				Path:    "/create",
				Handler: bgm.CreateBGMHandler(serverCtx),
			},
			{
				// 删除BGM
				Method:  http.MethodPost,
				Path:    "/delete",
				Handler: bgm.DeleteBGMHandler(serverCtx),
			},
			{
				// 获取分类下的BGM列表
				Method:  http.MethodGet,
				Path:    "/getByCategory",
				Handler: bgm.GetBGMsByCategoryHandler(serverCtx),
			},
			{
				// 获取BGM详情
				Method:  http.MethodGet,
				Path:    "/getByID",
				Handler: bgm.GetBGMByIDHandler(serverCtx),
			},
			{
				// 获取用户创建的BGM
				Method:  http.MethodGet,
				Path:    "/getCreatedByUser",
				Handler: bgm.GetCreatedByUserHandler(serverCtx),
			},
			{
				// 获取用户喜欢的BGM
				Method:  http.MethodGet,
				Path:    "/getUserLikedBGM",
				Handler: bgm.GetUserLikedBGMHandler(serverCtx),
			},
			{
				// 搜索BGM
				Method:  http.MethodGet,
				Path:    "/search",
				Handler: bgm.SearchBGMHandler(serverCtx),
			},
			{
				// 更新BGM
				Method:  http.MethodPost,
				Path:    "/update",
				Handler: bgm.UpdateBGMHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v3/bgm"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.APICacheMiddleware},
			[]rest.Route{
				{
					// 获取 BGM Category
					Method:  http.MethodGet,
					Path:    "/getCategory",
					Handler: bgm.GetBGMCategoryHandler(serverCtx),
				},
				{
					// 获取热门BGM
					Method:  http.MethodGet,
					Path:    "/getTrendingBGM",
					Handler: bgm.GetTrendingBGMHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v3/bgm"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/health",
				Handler: common.GetHealthHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v3/common"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 根据类型获取发现页面配置
				Method:  http.MethodGet,
				Path:    "/page/config/by-type",
				Handler: discover.GetDiscoverPageConfigByTypeHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v3/discover"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 获取热门sekai
				Method:  http.MethodGet,
				Path:    "/getHot",
				Handler: sekai.GetHotSekaiHandler(serverCtx),
			},
			{
				// 获取联想搜索sekai关键词
				Method:  http.MethodGet,
				Path:    "/getKeyWords",
				Handler: sekai.GetSekaiKeywordsHandler(serverCtx),
			},
			{
				// 搜索sekai
				Method:  http.MethodGet,
				Path:    "/search",
				Handler: sekai.SearchSekaiHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v3/sekai"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 搜索story
				Method:  http.MethodGet,
				Path:    "/search",
				Handler: story.SearchStoryHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v3/story"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/:id",
				Handler: user.GetUserInfoHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v2/user"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 克隆语音
				Method:  http.MethodPost,
				Path:    "/clone",
				Handler: voice.CloneVoiceHandler(serverCtx),
			},
			{
				// 获取分类语音
				Method:  http.MethodGet,
				Path:    "/getByCategory",
				Handler: voice.GetByCategoryHandler(serverCtx),
			},
			{
				// 获取语音详情
				Method:  http.MethodGet,
				Path:    "/getByID",
				Handler: voice.GetVoiceByIDHandler(serverCtx),
			},
			{
				// 获取用户创建的语音
				Method:  http.MethodGet,
				Path:    "/getCreatedByUser",
				Handler: voice.GetCreatedByUserHandler(serverCtx),
			},
			{
				// 获取用户点赞的语音
				Method:  http.MethodGet,
				Path:    "/getLikedByUser",
				Handler: voice.GetLikedByUserHandler(serverCtx),
			},
			{
				// 获取问题
				Method:  http.MethodGet,
				Path:    "/getQuestion",
				Handler: voice.GetQuestionHandler(serverCtx),
			},
			{
				// 获取热门语音
				Method:  http.MethodGet,
				Path:    "/getTrending",
				Handler: voice.GetTrendingHandler(serverCtx),
			},
			{
				// 点赞/取消点赞语音，代替原来 likeVoice、unlikeVoice 两个接口
				Method:  http.MethodPost,
				Path:    "/like",
				Handler: voice.LikeVoiceHandler(serverCtx),
			},
			{
				// 搜索语音
				Method:  http.MethodGet,
				Path:    "/search",
				Handler: voice.SearchHandler(serverCtx),
			},
			{
				// 更新语音
				Method:  http.MethodPut,
				Path:    "/update",
				Handler: voice.UpdateByIDHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v3/voice"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.APICacheMiddleware},
			[]rest.Route{
				{
					// 获取口音
					Method:  http.MethodGet,
					Path:    "/getAccents",
					Handler: voice.GetAccentsHandler(serverCtx),
				},
				{
					// 获取分类
					Method:  http.MethodGet,
					Path:    "/getCategory",
					Handler: voice.GetCategoryHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v3/voice"),
	)
}
