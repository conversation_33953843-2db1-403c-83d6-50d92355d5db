package discover

import (
	"net/http"

	"github.com/sekai-app/sekai-go/internal/logic/discover"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 根据类型获取发现页面配置
func GetDiscoverPageConfigByTypeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetDiscoverPageConfigByTypeReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := discover.NewGetDiscoverPageConfigByTypeLogic(r.Context(), svcCtx)
		resp, err := l.GetDiscoverPageConfigByType(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
