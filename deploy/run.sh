#!/bin/bash

# 检查环境变量是否存在
if [ -z "$AIU_ENV" ]; then
    echo "Error: AIU_ENV environment variable is not set!" >&2
    exit 1
fi

# 根据不同的环境执行逻辑
case "$AIU_ENV" in
    "develop")
        echo "Running in DEVELOPMENT environment"
        ./server -f etc/sekaigo_dev.yaml
        ;;
    "stage")
        echo "Running in STAGING environment"
        ./server -f etc/sekaigo_stage.yaml
        ;;
    "production")
        echo "Running in PRODUCTION environment"
        ./server -f etc/sekaigo.yaml
        ;;
    *)
        echo "Error: Unknown AIU_ENV value '$AIU_ENV'" >&2
        exit 1
        ;;
esac