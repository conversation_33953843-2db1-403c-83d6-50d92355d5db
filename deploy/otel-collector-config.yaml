receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 127.0.0.1:4317
      http:
        endpoint: 127.0.0.1:4318

  prometheus:
    config:
      scrape_configs:
        - job_name: ${env:SERVICE_NAME}
          scrape_interval: 10s
          static_configs:
            - targets: ['127.0.0.1:6060']

processors:
  resourcedetection/ecs:
    detectors: [env, ecs]
    timeout: 2s
    override: false

  batch:
    timeout: 10s
    send_batch_size: 1000

  resource:
    attributes:
      - action: upsert
        key: service.instance.id
        from_attribute: aws.ecs.task.id
      - action: insert
        key: env
        value: ${env:AIU_ENV}

exporters:
  otlphttp:
    endpoint: http://ip-10-0-7-175.ec2.internal:9090/api/v1/otlp
    tls:
      insecure: false

service:
  pipelines:
    metrics:
      receivers: [prometheus]
      processors: [resourcedetection/ecs, resource, batch]
      exporters: [otlphttp]
