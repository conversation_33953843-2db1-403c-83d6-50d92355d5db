ARG REGISTRY=docker.io

# 第一阶段：构建阶段
FROM ${REGISTRY}/golang:1.23-alpine AS builder
RUN set -eux

WORKDIR /data/app
COPY go.mod go.sum ./
RUN go mod tidy
COPY . .

RUN rm -rf /data/app/bin/
RUN go build -ldflags="-s -w" -o ./bin/server ./main.go

# 第二阶段：运行阶段
FROM ${REGISTRY}/alpine:3.21.3

# 安装 ffmpeg
RUN set -eux \
    && apk update \
    && apk add --no-cache ffmpeg

ENV AIU_ENV=production

WORKDIR /app
COPY --from=builder /data/app/bin/server ./server
COPY --from=builder /data/app/deploy/run.sh ./run.sh

COPY --from=builder /data/app/etc/ ./etc

EXPOSE 7000
CMD ["sh", "./run.sh"]