package main

import (
	"fmt"
	"time"

	"github.com/sekai-app/sekai-go/pkg/pgmodel"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// PostgresqlConfig ...
type PostgresqlConfig struct {
	Host     string `yaml:"host"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	DBName   string `yaml:"dbname"`
	Port     int    `yaml:"port"`
}

type pgDao struct {
	db *gorm.DB
}

func newPGDao(cfg PostgresqlConfig) (*pgDao, error) {
	dsn := fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s sslmode=require",
		cfg.Host, cfg.User, cfg.Password, cfg.DBName,
	)
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	return &pgDao{db: db}, nil
}

func (p *pgDao) UpdateBGM(id int, createTimeUTC int) error {
	err := p.db.Model(&pgmodel.BGM{ID: id}).
		Updates(&pgmodel.BGM{CreateTimeUTC: createTimeUTC}).Error
	return err
}

func (p *pgDao) UpdateVoice(id int, createTime int) error {
	err := p.db.Model(&pgmodel.Voice{ID: id}).
		Updates(&pgmodel.Voice{CreateTime: createTime}).Error
	return err
}

func (p *pgDao) UpdateCharacter(id int, updateTime time.Time) error {
	err := p.db.Model(&pgmodel.Character{ID: id}).
		Updates(&pgmodel.Character{UpdateTime: pgmodel.Time{Time: updateTime}}).Error
	return err
}

func (p *pgDao) UpdateUserCharacter(id int, updateTime time.Time) error {
	err := p.db.Model(&pgmodel.UserCharacter{ID: id}).
		Updates(&pgmodel.UserCharacter{UpdateTime: pgmodel.Time{Time: updateTime}}).Error
	return err
}
