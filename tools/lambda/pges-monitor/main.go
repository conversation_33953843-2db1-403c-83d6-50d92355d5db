package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/sekai-app/sekai-go/pkg/lark"
	"github.com/sekai-app/sekai-go/pkg/pgmodel"
	"golang.org/x/sync/errgroup"
)

// Config ...
type Config struct {
	Postgresql PostgresqlConfig `yaml:"postgresql"`
	OpenSearch OpenSearchConfig `yaml:"openSearch"`
	Check      CheckConfig      `yaml:"check"`
}

// CheckConfig ...
type CheckConfig struct {
	BgmID      int            `yaml:"bgmID"`
	VoiceID    int            `yaml:"voiceID"`
	CharID     int            `yaml:"charID"`
	UserCharID int            `yaml:"userCharID"`
	MaxDelay   int            `yaml:"maxDelay"`
	LarkBot    lark.BotConfig `yaml:"lark_bot"`
}

func mustInt(s string) int {
	i, err := strconv.Atoi(s)
	if err != nil {
		log.Panicf("must int failed: %v", err)
	}

	return i
}

func parseConfig() *Config {
	cfg := &Config{
		Postgresql: PostgresqlConfig{
			Host:     os.Getenv("PG_HOST"),
			User:     os.Getenv("PG_USER"),
			Password: os.Getenv("PG_PASSWORD"),
			DBName:   os.Getenv("PG_DBNAME"),
		},
		OpenSearch: OpenSearchConfig{
			Addr:     os.Getenv("OPENSEARCH_ADDR"),
			User:     os.Getenv("OPENSEARCH_USER"),
			Password: os.Getenv("OPENSEARCH_PASSWORD"),
		},
		Check: CheckConfig{
			BgmID:      mustInt(os.Getenv("BGM_ID")),
			VoiceID:    mustInt(os.Getenv("VOICE_ID")),
			CharID:     mustInt(os.Getenv("CHAR_ID")),
			UserCharID: mustInt(os.Getenv("USER_CHAR_ID")),
			MaxDelay:   mustInt(os.Getenv("MAX_DELAY")),
			LarkBot:    lark.BotConfig{Webhook: os.Getenv("LARK_WEBHOOK")},
		},
	}
	return cfg
}

type lambdaHandler struct {
	dbDao    *pgDao
	es       *openSearchService
	checkCfg CheckConfig
	larkBot  *lark.BotClient
}

func (l *lambdaHandler) hello() (string, error) {
	return "Hello λ!", nil
}

func (l *lambdaHandler) checkBGM() error {
	ts := int(time.Now().UnixMilli())
	err := l.dbDao.UpdateBGM(l.checkCfg.BgmID, ts)
	if err != nil {
		log.Printf("update bgm failed: %v", err)
		return fmt.Errorf("update bgm failed: %v", err)
	}

	time.Sleep(time.Duration(l.checkCfg.MaxDelay) * time.Second)

	bgm, err := l.es.GetBGM(l.checkCfg.BgmID)
	if err != nil {
		log.Printf("get bgm from opensearch failed: %v", err)
		return fmt.Errorf("get bgm from opensearch failed: %v", err)
	}

	if bgm.CreateTimeUTC != ts {
		log.Printf("bgm info sync to opensearch timeout ...")
		return fmt.Errorf("bgm info sync to opensearch timeout")
	}

	return nil
}

func (l *lambdaHandler) checkVoice() error {
	ts := int(time.Now().UnixMilli())
	err := l.dbDao.UpdateVoice(l.checkCfg.VoiceID, ts)
	if err != nil {
		log.Printf("update voice failed: %v", err)
		return fmt.Errorf("update voice failed: %v", err)
	}

	time.Sleep(time.Duration(l.checkCfg.MaxDelay) * time.Second)

	voice, err := l.es.GetVoice(l.checkCfg.VoiceID)
	if err != nil {
		log.Printf("get voice from opensearch failed: %v", err)
		return fmt.Errorf("get voice from opensearch failed: %v", err)
	}

	if voice.CreateTime != ts {
		log.Printf("voice info sync to opensearch timeout ...")
		return fmt.Errorf("voice info sync to opensearch timeout")
	}

	return nil
}

func (l *lambdaHandler) checkCharacter() error {
	now := time.Now()
	err := l.dbDao.UpdateCharacter(l.checkCfg.CharID, now)
	if err != nil {
		log.Printf("update character failed: %v", err)
		return fmt.Errorf("update character failed: %v", err)
	}

	time.Sleep(time.Duration(l.checkCfg.MaxDelay) * time.Second)

	character, err := l.es.GetCharacter(l.checkCfg.CharID)
	if err != nil {
		log.Printf("get character failed: %v", err)
		return fmt.Errorf("get character failed: %v", err)
	}

	if character.UpdateTime.Format(pgmodel.TimeLayout) != now.Format(pgmodel.TimeLayout) {
		log.Printf("character info sync to opensearch timeout ...")
		return fmt.Errorf("character info sync to opensearch timeout")
	}

	return nil
}

func (l *lambdaHandler) checkUserCharacter() error {
	now := time.Now()
	err := l.dbDao.UpdateUserCharacter(l.checkCfg.UserCharID, now)
	if err != nil {
		log.Printf("update user character failed: %v", err)
		return fmt.Errorf("update user character failed: %v", err)
	}

	time.Sleep(time.Duration(l.checkCfg.MaxDelay) * time.Second)

	character, err := l.es.GetUserCharacter(l.checkCfg.UserCharID)
	if err != nil {
		log.Printf("get user character failed: %v", err)
		return fmt.Errorf("get user character failed: %v", err)
	}

	if character.UpdateTime.Format(pgmodel.TimeLayout) != now.Format(pgmodel.TimeLayout) {
		log.Printf("user character info sync to opensearch timeout ...")
		return fmt.Errorf("user character info sync to opensearch timeout")
	}

	return nil
}

func (l *lambdaHandler) handle() (string, error) {
	var eg errgroup.Group

	eg.Go(l.checkBGM)
	eg.Go(l.checkVoice)
	eg.Go(l.checkCharacter)
	eg.Go(l.checkUserCharacter)

	if err := eg.Wait(); err != nil {
		// send lark alarm msg
		msg := &lark.InteractiveMessage{
			Header: &lark.InteractiveHeader{
				Title: &lark.TagContent{
					Tag:     "plain_text",
					Content: "Postgresql 同步 Opensearch 告警",
				},
				Template: "red",
			},
			Elements: []interface{}{
				map[string]interface{}{
					"tag": "div",
					"text": &lark.TagContent{
						Tag:     "lark_md",
						Content: fmt.Sprintf("同步链路检查发生异常：%s", err.Error()),
					}},
			},
			Config:   nil,
			CardLink: nil,
		}
		err = l.larkBot.SendBotMessage(context.Background(), msg)
		if err != nil {
			log.Printf("send bot message failed: %v", err)
		}
		return "", fmt.Errorf("check postgresql -> opensearch in λ failed")
	}

	log.Printf("check postgresql -> opensearch in λ success ...")
	return "check postgresql -> opensearch in λ!", nil
}

func main() {
	cfg := parseConfig()

	dbDao, err := newPGDao(cfg.Postgresql)
	if err != nil {
		log.Fatalf("newPGDao failed: %v", err)
	}

	es, err := newOpenSearchService(cfg.OpenSearch)
	if err != nil {
		log.Fatalf("newESService failed: %v", err)
	}

	bot, err := lark.NewBotClient(cfg.Check.LarkBot)
	if err != nil {
		log.Fatalf("newLarkBot failed: %v", err)
	}

	l := lambdaHandler{dbDao: dbDao, es: es, checkCfg: cfg.Check, larkBot: bot}

	useLambda := os.Getenv("USE_LAMBDA")

	if useLambda == "true" {
		// Make the handler available for Remote Procedure Call by AWS Lambda
		lambda.Start(l.handle)
	} else {
		_, _ = l.handle()
	}
}
