package main

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"strconv"
	"time"

	"github.com/opensearch-project/opensearch-go"
	"github.com/sekai-app/sekai-go/pkg/pgmodel"
)

// OpenSearchConfig ...
type OpenSearchConfig struct {
	Addr     string `yaml:"addr"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
}

type openSearchService struct {
	cli *opensearch.Client
}

type getResponse struct {
	Found  bool            `json:"found"`
	Source json.RawMessage `json:"_source"`
}

func newOpenSearchService(cfg OpenSearchConfig) (*openSearchService, error) {
	cli, err := opensearch.NewClient(opensearch.Config{
		Addresses: []string{cfg.Addr},
		Username:  cfg.User,
		Password:  cfg.Password,
		Transport: &http.Transport{
			DialContext: (&net.Dialer{
				Timeout: 3 * time.Second, // 连接超时
			}).DialContext,
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	return &openSearchService{cli: cli}, nil
}

func (o *openSearchService) get(index, id string) (json.RawMessage, error) {
	rsp, err := o.cli.Get(index, id)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()

	if rsp.IsError() {
		return nil, fmt.Errorf("opensearch rsp error: %s", rsp.String())
	}

	getRsp := &getResponse{}
	err = json.NewDecoder(rsp.Body).Decode(getRsp)
	if err != nil {
		return nil, err
	}

	return getRsp.Source, nil
}

func (o *openSearchService) GetBGM(id int) (*pgmodel.BGM, error) {
	source, err := o.get("aiu-bgm-table", strconv.Itoa(id))
	if err != nil {
		return nil, err
	}

	var bgm *pgmodel.BGM
	err = json.Unmarshal(source, &bgm)
	if bgm == nil {
		return nil, fmt.Errorf("bgm not found")
	}
	return bgm, err
}

func (o *openSearchService) GetVoice(id int) (*pgmodel.Voice, error) {
	source, err := o.get("aiu-voice-table", strconv.Itoa(id))
	if err != nil {
		return nil, err
	}

	var voice *pgmodel.Voice
	err = json.Unmarshal(source, &voice)
	if voice == nil {
		return nil, fmt.Errorf("voice not found")
	}
	return voice, err
}

func (o *openSearchService) GetCharacter(id int) (*pgmodel.Character, error) {
	source, err := o.get("aiu-character-table", strconv.Itoa(id))
	if err != nil {
		return nil, err
	}

	var character *pgmodel.Character
	err = json.Unmarshal(source, &character)
	if character == nil {
		return nil, fmt.Errorf("character not found")
	}
	return character, err
}

func (o *openSearchService) GetUserCharacter(id int) (*pgmodel.UserCharacter, error) {
	source, err := o.get("aiu_user_inserted_char_table", strconv.Itoa(id))
	if err != nil {
		return nil, err
	}

	var character *pgmodel.UserCharacter
	err = json.Unmarshal(source, &character)
	if character == nil {
		return nil, fmt.Errorf("userCharacter not found")
	}
	return character, err
}
