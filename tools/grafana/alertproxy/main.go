package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/sekai-app/sekai-go/pkg/lark"
)

const (
	larkWebhookPrefix = "https://open.larksuite.com"
)

var (
	addr = flag.String("addr", ":8080", "HTTP service address")
)

type GrafanaAlert struct {
	Status       string                 `json:"status"`
	Labels       map[string]string      `json:"labels"`
	Annotations  map[string]string      `json:"annotations"`
	StartsAt     string                 `json:"startsAt"`
	EndsAt       string                 `json:"endsAt"`
	Values       map[string]interface{} `json:"values"`
	GeneratorURL string                 `json:"generatorURL"`
	SilenceURL   string                 `json:"silenceURL"`
	DashboardURL string                 `json:"dashboardURL"`
	PanelURL     string                 `json:"panelURL"`
}

type GrafanaWebhook struct {
	Title       string         `json:"title"`
	Message     string         `json:"message"`
	OrgID       int64          `json:"orgId"`
	DashboardID int64          `json:"dashboardId"`
	PanelID     int64          `json:"panelId"`
	Tags        []string       `json:"tags"`
	Alerts      []GrafanaAlert `json:"alerts"`
	Status      string         `json:"status"`
}

func getStatusColor(status string) string {
	switch strings.ToLower(status) {
	case "firing":
		return "red"
	case "resolved":
		return "green"
	default:
		return "grey"
	}
}

func buildAlertCard(webhook *GrafanaWebhook) *lark.InteractiveMessage {
	// 构建标题
	title := webhook.Title
	if title == "" {
		title = "Grafana 告警通知"
	}

	// 构建消息内容
	var contentBuilder strings.Builder

	contentBuilder.WriteString(fmt.Sprintf("%s\n", webhook.Message))

	return &lark.InteractiveMessage{
		Header: &lark.InteractiveHeader{
			Title: &lark.TagContent{
				Tag:     "plain_text",
				Content: title,
			},
			Template: getStatusColor(webhook.Status),
		},
		Elements: []interface{}{
			map[string]interface{}{
				"tag": "div",
				"text": &lark.TagContent{
					Tag:     "lark_md",
					Content: contentBuilder.String(),
				},
			},
		},
		Config: &lark.InteractiveConfig{
			EnableForward: true,
		},
	}
}

func handleWebhook() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// 从请求路径中提取 webhook 地址
		larkWebhookURL := larkWebhookPrefix + r.URL.Path

		// 创建 lark bot 客户端
		bot, err := lark.NewBotClient(lark.BotConfig{
			Webhook: larkWebhookURL,
		})
		if err != nil {
			log.Printf("Failed to create Lark bot client: %v", err)
			http.Error(w, "Failed to create Lark bot client", http.StatusInternalServerError)
			return
		}

		var webhook GrafanaWebhook
		if err := json.NewDecoder(r.Body).Decode(&webhook); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}

		// 构建并发送卡片消息
		card := buildAlertCard(&webhook)
		if err := bot.SendBotMessage(context.Background(), card); err != nil {
			log.Printf("Failed to send message to Lark: %v", err)
			http.Error(w, "Failed to send message to Lark", http.StatusInternalServerError)
			return
		}

		w.WriteHeader(http.StatusOK)
	}
}

func main() {
	flag.Parse()

	http.HandleFunc("/open-apis/bot/v2/hook/", handleWebhook())

	log.Printf("Starting server on %s", *addr)
	if err := http.ListenAndServe(*addr, nil); err != nil {
		log.Fatal(err)
	}
}
