repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v2.3.0
    hooks:
    -   id: check-yaml
        files: \.yaml$|\.yml$
-   repo: local
    hooks:
    -   id: go-mod-tidy
        name: go mod tidy
        entry: go mod tidy
        language: system
        files: go.mod|go.sum|\.go$
        pass_filenames: false
-   repo: https://github.com/golangci/golangci-lint
    rev: v1.62.2
    hooks:
    -   id: golangci-lint # hook id
-   repo: local
    hooks:
    -   id: unit-test
        name: run unit tests
        entry: make unit-test
        language: system
        pass_filenames: false
        stages: [pre-push]  # 这个只在push时执行
        types: [go]
