info (
	title:   "BGM API定义"
	desc:    "BGM API定义"
	author:  "ijkbytes"
	email:   "<EMAIL>"
	version: "v3"
)

syntax = "v1"

import "common.api"

// BGM 通用类型定义
type (
	BGMCategory {
		ID       int    `json:"id"`
		Name     string `json:"name"`
		CoverURL string `json:"cover_url"`
	}
	BGMBaseInfo {
		ID              int            `json:"id"`
		Name            string         `json:"name"`
		URL             string         `json:"url"`
		CoverURL        string         `json:"cover_url"`
		Duration        int            `json:"duration"`
		Tags            []string       `json:"tags"`
		ReferenceCount  int            `json:"reference_count"`
		CreatorUserID   int            `json:"creator_user_id"`
		CreatorUserName string         `json:"creator_user_name"`
		Public          bool           `json:"public"`
		NSFW            bool           `json:"nsfw"`
		Liked           bool           `json:"liked"`
		Categories      []*BGMCategory `json:"categories"`
	}
	BGMPagination {
		Pagination
		Items []*BGMBaseInfo `json:"items"`
	}
)

type (
	CreateBGMReq {
		Name       string   `json:"name"`
		URL        string   `json:"url"`
		Duration   int      `json:"duration"`
		CoverURL   string   `json:"cover_url,optional"`
		Tags       []string `json:"tags,optional"`
		Public     bool     `json:"public,default=false"`
		Categories []int32  `json:"categories,optional"`
		PremiseIDs []string `json:"premise_ids,optional"`
	}
	CreateBGMResp {
		ResponseBase
		Data BGMBaseInfo `json:"data"`
	}
)

type (
	GetBGMByIDReq {
		ID int `form:"id"`
	}
	GetBGMByIDResp {
		ResponseBase
		Data BGMBaseInfo `json:"data"`
	}
)

type (
	GetUserLikedBGMReq {
		UserID int `form:"user_id,optional"`
		Page   int `form:"page,default=1,range=[1:]"`
		Size   int `form:"size,default=10,range=[1:]"`
	}
	GetUserLikedBGMResp {
		ResponseBase
		Data BGMPagination `json:"data"`
	}
)

type (
	GetBGMCategoryReq {
		Page int `form:"page,default=1,range=[1:]"`
		Size int `form:"size,default=10,range=[1:]"`
	}
	BGMCategoryPagination {
		Pagination
		Items []*BGMCategory `json:"items"`
	}
	GetBGMCategoryResp {
		ResponseBase
		Data BGMCategoryPagination `json:"data"`
	}
)

type (
	GetCreatedByUserReq {
		UserID int `form:"user_id"`
		Page   int `form:"page,default=1,range=[1:]"`
		Size   int `form:"size,default=10,range=[1:]"`
	}
	GetCreatedByUserResp {
		ResponseBase
		Data BGMPagination `json:"data"`
	}
)

type (
	UpdateBGMReq {
		ID int `json:"id"`
		// 可选参数，这里使用指针，允许部分字段更新为零值
		Name       *string  `json:"name,optional"`
		URL        *string  `json:"url,optional"`
		Duration   *int     `json:"duration,optional"`
		CoverURL   *string  `json:"cover_url,optional"`
		Tags       []string `json:"tags,optional"`
		Public     *bool    `json:"public,optional"`
		Categories []int32  `json:"categories,optional"`
		PremiseIDs []string `json:"premise_ids,optional"`
	}
	UpdateBGMResp {
		ResponseBase
		Data BGMBaseInfo `json:"data"`
	}
)

type (
	DeleteBGMReq {
		ID int `json:"id"`
	}
	DeleteBGMResp {
		ResponseBase
	}
)

type (
	GetTrendingBGMReq {
		Page int `form:"page,default=1,range=[1:]"`
		Size int `form:"size,default=10,range=[1:]"`
	}
	GetTrendingBGMResp {
		ResponseBase
		Data BGMPagination `json:"data"`
	}
)

type (
	SearchBGMReq {
		Keyword string `form:"keyword"`
		Page    int    `form:"page,default=1,range=[1:]"`
		Size    int    `form:"size,default=10,range=[1:]"`
	}
	SearchBGMResp {
		ResponseBase
		Data BGMPagination `json:"data"`
	}
)

type (
	GetBGMsByCategoryReq {
		CategoryID int `form:"category_id"`
		Page       int `form:"page,default=1,range=[1:]"`
		Size       int `form:"size,default=10,range=[1:]"`
	}
	GetBGMsByCategoryResp {
		ResponseBase
		Data BGMPagination `json:"data"`
	}
)

@server (
	prefix: /v3/bgm
	group:  bgm
	jwt:    Auth
)
service main {
	@doc (
		summary: "创建BGM"
	)
	@handler CreateBGMHandler
	post /create (CreateBGMReq) returns (CreateBGMResp)

	@doc (
		summary: "获取BGM详情"
	)
	@handler GetBGMByIDHandler
	get /getByID (GetBGMByIDReq) returns (GetBGMByIDResp)

	@doc (
		summary: "获取用户喜欢的BGM"
	)
	@handler GetUserLikedBGMHandler
	get /getUserLikedBGM (GetUserLikedBGMReq) returns (GetUserLikedBGMResp)

	@doc (
		summary: "获取用户创建的BGM"
	)
	@handler GetCreatedByUserHandler
	get /getCreatedByUser (GetCreatedByUserReq) returns (GetCreatedByUserResp)

	@doc (
		summary: "更新BGM"
	)
	@handler UpdateBGMHandler
	post /update (UpdateBGMReq) returns (UpdateBGMResp)

	@doc (
		summary: "删除BGM"
	)
	@handler DeleteBGMHandler
	post /delete (DeleteBGMReq) returns (DeleteBGMResp)

	@doc (
		summary: "搜索BGM"
	)
	@handler SearchBGMHandler
	get /search (SearchBGMReq) returns (SearchBGMResp)

	@doc (
		summary: "获取分类下的BGM列表"
	)
	@handler GetBGMsByCategoryHandler
	get /getByCategory (GetBGMsByCategoryReq) returns (GetBGMsByCategoryResp)
}

// 带缓存的API
@server (
	prefix:     /v3/bgm
	group:      bgm
	jwt:        Auth
	middleware: APICacheMiddleware // 使用缓存中间件, 缓存10分钟，仅缓存GET请求
)
service main {
	@doc (
		summary: "获取 BGM Category"
	)
	@handler GetBGMCategoryHandler
	get /getCategory (GetBGMCategoryReq) returns (GetBGMCategoryResp)

	@doc (
		summary: "获取热门BGM"
	)
	@handler GetTrendingBGMHandler
	get /getTrendingBGM (GetTrendingBGMReq) returns (GetTrendingBGMResp)
}

