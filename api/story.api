info (
	title:   "Story API定义"
	desc:    "Story API定义"
	author:  "ijkbytes"
	email:   "<EMAIL>"
	version: "v3"
)

syntax = "v1"

import (
	"common.api"
)

type (
	StoryBaseInfo {
		StoryID         int    `json:"story_id"` // story id
		HighlightText   string `json:"highlight_text"` // 亮点
		GifURL          string `json:"gif_url"` // gif url
		CreatorUserID   int    `json:"creator_user_id"` // 创建者id
		CreatorUserName string `json:"creator_user_name"` // 创建者用户名
		Public          bool   `json:"public"` // 是否公开
		IsDeleted       bool   `json:"is_deleted"` // 是否删除
		WhatIf          string `json:"what_if"` // 设定
		ViewCount       int    `json:"view_count"` // 浏览量
	}
	StoryBasePagination {
		Pagination
		Items []*StoryBaseInfo `json:"items"`
	}
)

type (
	SearchStoryReq {
		Keywords string `form:"keywords"`
		Page     int    `form:"page,default=1,range=[1:]"`
		Size     int    `form:"size,default=10,range=[1:]"`
	}
	SearchStoryResp {
		ResponseBase
		Data StoryBasePagination `json:"data"`
	}
)

@server (
	prefix: /v3/story
	group:  story
	jwt:    Auth
)
service main {
	@doc (
		summary: "搜索story"
	)
	@handler SearchStoryHandler
	get /search (SearchStoryReq) returns (SearchStoryResp)
}

