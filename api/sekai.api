info (
	title:   "Sekai API定义"
	desc:    "Sekai API定义"
	author:  "ijkbytes"
	email:   "<EMAIL>"
	version: "v3"
)

syntax = "v1"

import (
	"common.api"
	"label.api"
)

type (
	SekaiBaseInfo {
		SekaiID         int          `json:"sekai_id"` // sekai id
		Title           string       `json:"title"` // 标题
		CoverURL        string       `json:"cover_url"` // 封面url
		NewCoverURL     string       `json:"new_cover_url"` // 新封面url
		CreatorUserID   int          `json:"creator_user_id"` // 创建者id
		CreatorUserName string       `json:"creator_user_name"` // 创建者用户名
		Public          bool         `json:"public"` // 是否公开
		Intro           string       `json:"intro"` // 简介
		BackgroundURL   string       `json:"background_url"` // 背景url
		Categories      []*LableInfo `json:"categories"` // 分类
		ViewCount       int          `json:"view_count"` // 浏览量
		TemplateID      int          `json:"template_id"` // 模板id
		MinAppVersion   int          `json:"min_app_version"` // 最小app版本
	}
	SekaiBasePagination {
		Pagination
		Items []*SekaiBaseInfo `json:"items"`
	}
)

type (
	GetSekaiKeywordsReq {
		Prefix string `form:"prefix"`
		Page   int    `form:"page,default=1,range=[1:]"`
		Size   int    `form:"size,default=10,range=[1:]"`
	}
	SekaiKeywordsData {
		Pagination
		Items []string `json:"items"`
	}
	GetSekaiKeywordsResp {
		ResponseBase
		Data SekaiKeywordsData `json:"data"`
	}
)

type (
	SearchSekaiReq {
		Keywords string `form:"keywords"`
		Page     int    `form:"page,default=1,range=[1:]"`
		Size     int    `form:"size,default=10,range=[1:]"`
	}
	SearchSekaiResp {
		ResponseBase
		Data SekaiBasePagination `json:"data"`
	}
)

type (
	GetHotSekaiReq {
		Page int `form:"page,default=1,range=[1:]"`
		Size int `form:"size,default=10,range=[1:]"`
	}
	HotSekaiResult {
		SekaiID       int    `json:"sekai_id"`
		Title         string `json:"title"`
		MinAppVersion int    `json:"min_app_version"`
	}
	HotSekaiPagination {
		Pagination
		Items []*HotSekaiResult `json:"items"`
	}
	GetHotSekaiResp {
		ResponseBase
		Data HotSekaiPagination `json:"data"`
	}
)

@server (
	prefix: /v3/sekai
	group:  sekai
	jwt:    Auth
)
service main {
	@doc (
		summary: "获取联想搜索sekai关键词"
	)
	@handler GetSekaiKeywordsHandler
	get /getKeyWords (GetSekaiKeywordsReq) returns (GetSekaiKeywordsResp)

	@doc (
		summary: "搜索sekai"
	)
	@handler SearchSekaiHandler
	get /search (SearchSekaiReq) returns (SearchSekaiResp)

	@doc (
		summary: "获取热门sekai"
	)
	@handler GetHotSekaiHandler
	get /getHot (GetHotSekaiReq) returns (GetHotSekaiResp)
}

