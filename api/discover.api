info (
	title:   "Discover API 定义"
	desc:    "Discover 功能模块 API 定义"
	author:  "Rooroo Developer"
	email:   "<EMAIL>"
	version: "v1"
)

syntax = "v1"

import "common.api"

// Discover 通用类型定义
type (
	// DiscoverPageConfigData 定义了发现页面配置中 data 字段的结构
	DiscoverPageConfigData {
		Title                 string  `json:"title"`
		Subtitle              string  `json:"subtitle"`
		CoverImageUrl         string  `json:"cover_image_url"`
		SecondaryPageImageUrl string  `json:"secondary_page_image_url"`
		LinkedSekaiIDs        []int64 `json:"linked_sekai_ids"`
	}
	// DiscoverPageConfigItem 定义了单个发现页面配置项
	DiscoverPageConfigItem {
		ID        int64                  `json:"id"`
		Type      string                 `json:"type"`
		Data      DiscoverPageConfigData `json:"data"`
		Status    string                 `json:"status"`
		SortOrder int32                  `json:"sort_order"`
		CreatedAt int64                  `json:"created_at"` // timestamp
		UpdatedAt int64                  `json:"updated_at"` // timestamp
	}
	// DiscoverPageConfigPagination 定义了发现页面配置的分页结构
	DiscoverPageConfigPagination {
		Pagination
		Items []*DiscoverPageConfigItem `json:"items"`
	}
)

// API 请求和响应类型
type (
	// GetDiscoverPageConfigByTypeReq 获取特定类型的发现页面配置请求
	GetDiscoverPageConfigByTypeReq {
		Type string `form:"type"` // 配置类型
		Page int    `form:"page,default=1,range=[1:]"` // 页码，从1开始
		Size int    `form:"size,default=10,range=[1:]"` // 每页数量
	}
	// GetDiscoverPageConfigByTypeResp 获取特定类型的发现页面配置响应
	GetDiscoverPageConfigByTypeResp {
		ResponseBase
		Data *DiscoverPageConfigPagination `json:"data"`
	}
)

@server (
	group:  discover
	prefix: /v3/discover
)
service main {
	@doc "根据类型获取发现页面配置"
	@handler getDiscoverPageConfigByType
	get /page/config/by-type (GetDiscoverPageConfigByTypeReq) returns (GetDiscoverPageConfigByTypeResp)
}

