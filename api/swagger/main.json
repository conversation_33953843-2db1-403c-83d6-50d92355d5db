{"swagger": "2.0", "info": {"title": "", "version": ""}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/discover/page/config/by-type": {"get": {"summary": "根据类型获取发现页面配置", "operationId": "getDiscoverPageConfigByType", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetDiscoverPageConfigByTypeResp"}}}, "parameters": [{"name": "type", "description": " 配置类型", "in": "query", "required": true, "type": "string"}, {"name": "page", "description": " 页码，从1开始", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "description": " 每页数量", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["discover"], "consumes": ["multipart/form-data"]}}, "/v2/user/{id}": {"get": {"operationId": "GetUserInfoHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UserInfoRsp"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "optional_case", "description": " 可选参数", "in": "query", "required": false, "type": "string"}], "tags": ["user"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/bgm/create": {"post": {"summary": "创建BGM", "operationId": "Create<PERSON><PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CreateBGMResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateBGMReq"}}], "tags": ["bgm"], "security": [{"apiKey": []}]}}, "/v3/bgm/delete": {"post": {"summary": "删除BGM", "operationId": "DeleteBGMHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/DeleteBGMResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DeleteBGMReq"}}], "tags": ["bgm"], "security": [{"apiKey": []}]}}, "/v3/bgm/getByCategory": {"get": {"summary": "获取分类下的BGM列表", "operationId": "GetBGMsByCategoryHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetBGMsByCategoryResp"}}}, "parameters": [{"name": "category_id", "in": "query", "required": true, "type": "integer", "format": "int32"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["bgm"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/bgm/getByID": {"get": {"summary": "获取BGM详情", "operationId": "GetBGMByIDHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetBGMByIDResp"}}}, "parameters": [{"name": "id", "in": "query", "required": true, "type": "integer", "format": "int32"}], "tags": ["bgm"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/bgm/getCategory": {"get": {"summary": "获取 BGM Category", "operationId": "GetBGMCategoryHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetBGMCategoryResp"}}}, "parameters": [{"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["bgm"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/bgm/getCreatedByUser": {"get": {"summary": "获取用户创建的BGM", "operationId": "GetCreatedByUserHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetCreatedByUserResp"}}}, "parameters": [{"name": "user_id", "in": "query", "required": true, "type": "integer", "format": "int32"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["bgm"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/bgm/getTrendingBGM": {"get": {"summary": "获取热门BGM", "operationId": "GetTrendingBGMHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetTrendingBGMResp"}}}, "parameters": [{"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["bgm"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/bgm/getUserLikedBGM": {"get": {"summary": "获取用户喜欢的BGM", "operationId": "GetUserLikedBGMHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetUserLikedBGMResp"}}}, "parameters": [{"name": "user_id", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["bgm"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/bgm/search": {"get": {"summary": "搜索BGM", "operationId": "SearchBGMHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SearchBGMResp"}}}, "parameters": [{"name": "keyword", "in": "query", "required": true, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["bgm"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/bgm/update": {"post": {"summary": "更新BGM", "operationId": "UpdateBGMHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpdateBGMResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateBGMReq"}}], "tags": ["bgm"], "security": [{"apiKey": []}]}}, "/v3/common/health": {"get": {"operationId": "GetHealthHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/HealthRsp"}}}, "tags": ["common"]}}, "/v3/sekai/getHot": {"get": {"summary": "获取热门sekai", "operationId": "GetHotSekaiHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetHotSekaiResp"}}}, "parameters": [{"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["sekai"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/sekai/getKeyWords": {"get": {"summary": "获取联想搜索sekai关键词", "operationId": "GetSekaiKeywordsHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetSekaiKeywordsResp"}}}, "parameters": [{"name": "prefix", "in": "query", "required": true, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["sekai"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/sekai/search": {"get": {"summary": "搜索sekai", "operationId": "SearchSekaiHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SearchSekaiResp"}}}, "parameters": [{"name": "keywords", "in": "query", "required": true, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["sekai"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/story/search": {"get": {"summary": "搜索story", "operationId": "SearchStoryHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SearchStoryResp"}}}, "parameters": [{"name": "keywords", "in": "query", "required": true, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["story"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/voice/clone": {"post": {"summary": "克隆语音", "operationId": "CloneVoiceHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CloneVoiceResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CloneVoiceReq"}}], "tags": ["voice"], "security": [{"apiKey": []}]}}, "/v3/voice/getAccents": {"get": {"summary": "获取口音", "operationId": "GetAccentsHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetAccentsResp"}}}, "parameters": [{"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["voice"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/voice/getByCategory": {"get": {"summary": "获取分类语音", "operationId": "GetByCategoryHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetByCategoryResp"}}}, "parameters": [{"name": "category_id", "in": "query", "required": true, "type": "integer", "format": "int32"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["voice"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/voice/getByID": {"get": {"summary": "获取语音详情", "operationId": "GetVoiceByIDHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetVoiceByIDResp"}}}, "parameters": [{"name": "voice_id", "in": "query", "required": true, "type": "integer", "format": "int32"}], "tags": ["voice"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/voice/getCategory": {"get": {"summary": "获取分类", "operationId": "GetCategoryHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetCategoryResp"}}}, "parameters": [{"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "99"}], "tags": ["voice"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/voice/getCreatedByUser": {"get": {"summary": "获取用户创建的语音", "operationId": "GetCreatedByUserHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetVoiceCreatedByUserResp"}}}, "parameters": [{"name": "user_id", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["voice"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/voice/getLikedByUser": {"get": {"summary": "获取用户点赞的语音", "operationId": "GetLikedByUserHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetLikedByUserResp"}}}, "parameters": [{"name": "user_id", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["voice"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/voice/getQuestion": {"get": {"summary": "获取问题", "operationId": "GetQuestionHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetQuestionResp"}}}, "tags": ["voice"], "security": [{"apiKey": []}]}}, "/v3/voice/getTrending": {"get": {"summary": "获取热门语音", "operationId": "GetTrendingHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetTrendingResp"}}}, "parameters": [{"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["voice"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/voice/like": {"post": {"summary": "点赞/取消点赞语音，代替原来 likeVoice、unlikeVoice 两个接口", "operationId": "LikeVoiceHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/LikeVoiceResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/LikeVoiceReq"}}], "tags": ["voice"], "security": [{"apiKey": []}]}}, "/v3/voice/search": {"get": {"summary": "搜索语音", "operationId": "SearchHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SearchResp"}}}, "parameters": [{"name": "keyword", "in": "query", "required": true, "type": "string"}, {"name": "page", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "1"}, {"name": "size", "in": "query", "required": true, "type": "integer", "format": "int32", "default": "10"}], "tags": ["voice"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/v3/voice/update": {"put": {"summary": "更新语音", "operationId": "UpdateByIDHandler", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpdateVoiceByIDReq"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateVoiceByIDReq"}}], "tags": ["voice"], "security": [{"apiKey": []}]}}}, "definitions": {"Accent": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "voice_name": {"type": "string"}}, "title": "Accent", "required": ["id", "name", "voice_name"]}, "BGMBaseInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "url": {"type": "string"}, "cover_url": {"type": "string"}, "duration": {"type": "integer", "format": "int32"}, "tags": {"type": "array", "items": {"type": "string"}}, "reference_count": {"type": "integer", "format": "int32"}, "creator_user_id": {"type": "integer", "format": "int32"}, "creator_user_name": {"type": "string"}, "public": {"type": "boolean", "format": "boolean"}, "nsfw": {"type": "boolean", "format": "boolean"}, "liked": {"type": "boolean", "format": "boolean"}, "categories": {"type": "array", "items": {"$ref": "#/definitions/BGMCategory"}}}, "title": "BGMBaseInfo", "required": ["id", "name", "url", "cover_url", "duration", "tags", "reference_count", "creator_user_id", "creator_user_name", "public", "nsfw", "liked", "categories"]}, "BGMCategory": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "cover_url": {"type": "string"}}, "title": "BGMCategory", "required": ["id", "name", "cover_url"]}, "BGMCategoryPagination": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/definitions/BGMCategory"}}}, "title": "BGMCategoryPagination", "required": ["items"]}, "BGMPagination": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/definitions/BGMBaseInfo"}}}, "title": "BGMPagination", "required": ["items"]}, "CloneVoiceReq": {"type": "object", "properties": {"record_url": {"type": "string"}, "accent_id": {"type": "integer", "format": "int32"}}, "title": "CloneVoiceReq", "required": ["record_url", "accent_id"]}, "CloneVoiceResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/VoiceInfo"}}, "title": "CloneVoiceResp", "required": ["data"]}, "CreateBGMReq": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string"}, "duration": {"type": "integer", "format": "int32"}, "cover_url": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "public": {"type": "boolean", "format": "boolean", "default": "false"}, "categories": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "premise_ids": {"type": "array", "items": {"type": "string"}}}, "title": "CreateBGMReq", "required": ["name", "url", "duration", "public"]}, "CreateBGMResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/BGMBaseInfo"}}, "title": "CreateBGMResp", "required": ["data"]}, "DeleteBGMReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}}, "title": "DeleteBGMReq", "required": ["id"]}, "DeleteBGMResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}, "title": "DeleteBGMResp"}, "DiscoverPageConfigData": {"type": "object", "properties": {"title": {"type": "string"}, "subtitle": {"type": "string"}, "cover_image_url": {"type": "string"}, "secondary_page_image_url": {"type": "string"}, "linked_sekai_ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "title": "DiscoverPageConfigData", "required": ["title", "subtitle", "cover_image_url", "secondary_page_image_url", "linked_sekai_ids"]}, "DiscoverPageConfigItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "type": {"type": "string"}, "data": {"$ref": "#/definitions/DiscoverPageConfigData"}, "status": {"type": "string"}, "sort_order": {"type": "integer", "format": "int32"}, "created_at": {"type": "integer", "format": "int64", "description": " timestamp"}, "updated_at": {"type": "integer", "format": "int64", "description": " timestamp"}}, "title": "DiscoverPageConfigItem", "required": ["id", "type", "data", "status", "sort_order", "created_at", "updated_at"]}, "DiscoverPageConfigPagination": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/definitions/DiscoverPageConfigItem"}}}, "title": "DiscoverPageConfigPagination", "required": ["items"]}, "GetAccentsPagination": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/definitions/Accent"}}}, "title": "GetAccentsPagination", "required": ["items"]}, "GetAccentsReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetAccentsReq", "required": ["page", "size"]}, "GetAccentsResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/GetAccentsPagination"}}, "title": "GetAccentsResp", "required": ["data"]}, "GetBGMByIDReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}}, "title": "GetBGMByIDReq", "required": ["id"]}, "GetBGMByIDResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/BGMBaseInfo"}}, "title": "GetBGMByIDResp", "required": ["data"]}, "GetBGMCategoryReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetBGMCategoryReq", "required": ["page", "size"]}, "GetBGMCategoryResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/BGMCategoryPagination"}}, "title": "GetBGMCategoryResp", "required": ["data"]}, "GetBGMsByCategoryReq": {"type": "object", "properties": {"category_id": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetBGMsByCategoryReq", "required": ["category_id", "page", "size"]}, "GetBGMsByCategoryResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/BGMPagination"}}, "title": "GetBGMsByCategoryResp", "required": ["data"]}, "GetByCategoryReq": {"type": "object", "properties": {"category_id": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetByCategoryReq", "required": ["category_id", "page", "size"]}, "GetByCategoryResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/VoicePagination"}}, "title": "GetByCategoryResp", "required": ["data"]}, "GetCategoryPagination": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/definitions/VoiceCategory"}}}, "title": "GetCategoryPagination", "required": ["items"]}, "GetCategoryReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "99"}}, "title": "GetCategoryReq", "required": ["page", "size"]}, "GetCategoryResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/GetCategoryPagination"}}, "title": "GetCategoryResp", "required": ["data"]}, "GetCreatedByUserReq": {"type": "object", "properties": {"user_id": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetCreatedByUserReq", "required": ["user_id", "page", "size"]}, "GetCreatedByUserResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/BGMPagination"}}, "title": "GetCreatedByUserResp", "required": ["data"]}, "GetDiscoverPageConfigByTypeReq": {"type": "object", "properties": {"type": {"type": "string", "description": " 配置类型"}, "page": {"type": "integer", "format": "int32", "default": "1", "description": " 页码，从1开始"}, "size": {"type": "integer", "format": "int32", "default": "10", "description": " 每页数量"}}, "title": "GetDiscoverPageConfigByTypeReq", "required": ["type", "page", "size"]}, "GetDiscoverPageConfigByTypeResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/DiscoverPageConfigPagination"}}, "title": "GetDiscoverPageConfigByTypeResp", "required": ["data"]}, "GetHotSekaiReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetHotSekaiReq", "required": ["page", "size"]}, "GetHotSekaiResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/HotSekaiPagination"}}, "title": "GetHotSekaiResp", "required": ["data"]}, "GetLikedByUserReq": {"type": "object", "properties": {"user_id": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetLikedByUserReq", "required": ["page", "size"]}, "GetLikedByUserResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/VoicePagination"}}, "title": "GetLikedByUserResp", "required": ["data"]}, "GetQuestionReq": {"type": "object", "title": "GetQuestionReq"}, "GetQuestionResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"type": "string"}}, "title": "GetQuestionResp", "required": ["data"]}, "GetSekaiKeywordsReq": {"type": "object", "properties": {"prefix": {"type": "string"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetSekaiKeywordsReq", "required": ["prefix", "page", "size"]}, "GetSekaiKeywordsResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/SekaiKeywordsData"}}, "title": "GetSekaiKeywordsResp", "required": ["data"]}, "GetTrendingBGMReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetTrendingBGMReq", "required": ["page", "size"]}, "GetTrendingBGMResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/BGMPagination"}}, "title": "GetTrendingBGMResp", "required": ["data"]}, "GetTrendingReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetTrendingReq", "required": ["page", "size"]}, "GetTrendingResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/VoicePagination"}}, "title": "GetTrendingResp", "required": ["data"]}, "GetUserLikedBGMReq": {"type": "object", "properties": {"user_id": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetUserLikedBGMReq", "required": ["page", "size"]}, "GetUserLikedBGMResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/BGMPagination"}}, "title": "GetUserLikedBGMResp", "required": ["data"]}, "GetVoiceByIDReq": {"type": "object", "properties": {"voice_id": {"type": "integer", "format": "int32"}}, "title": "GetVoiceByIDReq", "required": ["voice_id"]}, "GetVoiceByIDResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/VoiceInfo"}}, "title": "GetVoiceByIDResp", "required": ["data"]}, "GetVoiceCreatedByUserReq": {"type": "object", "properties": {"user_id": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "GetVoiceCreatedByUserReq", "required": ["page", "size"]}, "GetVoiceCreatedByUserResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/VoicePagination"}}, "title": "GetVoiceCreatedByUserResp", "required": ["data"]}, "HealthReq": {"type": "object", "title": "HealthReq"}, "HealthRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}, "title": "HealthRsp"}, "HotSekaiPagination": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/definitions/HotSekaiResult"}}}, "title": "HotSekaiPagination", "required": ["items"]}, "HotSekaiResult": {"type": "object", "properties": {"sekai_id": {"type": "integer", "format": "int32"}, "title": {"type": "string"}, "min_app_version": {"type": "integer", "format": "int32"}}, "title": "HotSekaiResult", "required": ["sekai_id", "title", "min_app_version"]}, "LableInfo": {"type": "object", "properties": {"lable_id": {"type": "integer", "format": "int32"}, "label_name": {"type": "string"}, "display_name": {"type": "string"}, "emoji": {"type": "string"}}, "title": "LableInfo", "required": ["lable_id", "label_name", "display_name", "emoji"]}, "LikeVoiceReq": {"type": "object", "properties": {"voice_id": {"type": "integer", "format": "int32"}, "like": {"type": "boolean", "format": "boolean"}}, "title": "LikeVoiceReq", "required": ["voice_id", "like"]}, "LikeVoiceResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}, "title": "LikeVoiceResp"}, "Pagination": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}}, "title": "Pagination", "required": ["total", "page", "size", "pages"]}, "ResponseBase": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}, "title": "ResponseBase", "required": ["code", "msg"]}, "SearchBGMReq": {"type": "object", "properties": {"keyword": {"type": "string"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "SearchBGMReq", "required": ["keyword", "page", "size"]}, "SearchBGMResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/BGMPagination"}}, "title": "SearchBGMResp", "required": ["data"]}, "SearchReq": {"type": "object", "properties": {"keyword": {"type": "string"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "SearchReq", "required": ["keyword", "page", "size"]}, "SearchResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/VoicePagination"}}, "title": "SearchResp", "required": ["data"]}, "SearchSekaiReq": {"type": "object", "properties": {"keywords": {"type": "string"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "SearchSekaiReq", "required": ["keywords", "page", "size"]}, "SearchSekaiResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/SekaiBasePagination"}}, "title": "SearchSekaiResp", "required": ["data"]}, "SearchStoryReq": {"type": "object", "properties": {"keywords": {"type": "string"}, "page": {"type": "integer", "format": "int32", "default": "1"}, "size": {"type": "integer", "format": "int32", "default": "10"}}, "title": "SearchStoryReq", "required": ["keywords", "page", "size"]}, "SearchStoryResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/StoryBasePagination"}}, "title": "SearchStoryResp", "required": ["data"]}, "SekaiBaseInfo": {"type": "object", "properties": {"sekai_id": {"type": "integer", "format": "int32", "description": " sekai id"}, "title": {"type": "string", "description": " 标题"}, "cover_url": {"type": "string", "description": " 封面url"}, "new_cover_url": {"type": "string", "description": " 新封面url"}, "creator_user_id": {"type": "integer", "format": "int32", "description": " 创建者id"}, "creator_user_name": {"type": "string", "description": " 创建者用户名"}, "public": {"type": "boolean", "format": "boolean", "description": " 是否公开"}, "intro": {"type": "string", "description": " 简介"}, "background_url": {"type": "string", "description": " 背景url"}, "categories": {"type": "array", "items": {"$ref": "#/definitions/LableInfo"}, "description": " 分类"}, "view_count": {"type": "integer", "format": "int32", "description": " 浏览量"}, "template_id": {"type": "integer", "format": "int32", "description": " 模板id"}, "min_app_version": {"type": "integer", "format": "int32", "description": " 最小app版本"}}, "title": "SekaiBaseInfo", "required": ["sekai_id", "title", "cover_url", "new_cover_url", "creator_user_id", "creator_user_name", "public", "intro", "background_url", "categories", "view_count", "template_id", "min_app_version"]}, "SekaiBasePagination": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/definitions/SekaiBaseInfo"}}}, "title": "SekaiBasePagination", "required": ["items"]}, "SekaiKeywordsData": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"type": "string"}}}, "title": "SekaiKeywordsData", "required": ["items"]}, "StoryBaseInfo": {"type": "object", "properties": {"story_id": {"type": "integer", "format": "int32", "description": " story id"}, "highlight_text": {"type": "string", "description": " 亮点"}, "gif_url": {"type": "string", "description": " gif url"}, "creator_user_id": {"type": "integer", "format": "int32", "description": " 创建者id"}, "creator_user_name": {"type": "string", "description": " 创建者用户名"}, "public": {"type": "boolean", "format": "boolean", "description": " 是否公开"}, "is_deleted": {"type": "boolean", "format": "boolean", "description": " 是否删除"}, "what_if": {"type": "string", "description": " 设定"}, "view_count": {"type": "integer", "format": "int32", "description": " 浏览量"}}, "title": "StoryBaseInfo", "required": ["story_id", "highlight_text", "gif_url", "creator_user_id", "creator_user_name", "public", "is_deleted", "what_if", "view_count"]}, "StoryBasePagination": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/definitions/StoryBaseInfo"}}}, "title": "StoryBasePagination", "required": ["items"]}, "UpdateBGMReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "url": {"type": "string"}, "duration": {"type": "integer", "format": "int32"}, "cover_url": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "public": {"type": "boolean", "format": "boolean"}, "categories": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "premise_ids": {"type": "array", "items": {"type": "string"}}}, "title": "UpdateBGMReq", "required": ["id"]}, "UpdateBGMResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/BGMBaseInfo"}}, "title": "UpdateBGMResp", "required": ["data"]}, "UpdateInfo": {"type": "object", "properties": {"display_name": {"type": "string"}, "enable": {"type": "boolean", "format": "boolean"}, "public": {"type": "boolean", "format": "boolean"}, "categories": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "accent_id": {"type": "integer", "format": "int32"}}, "title": "UpdateInfo"}, "UpdateVoiceByIDReq": {"type": "object", "properties": {"voice_id": {"type": "integer", "format": "int32"}, "update_info": {"$ref": "#/definitions/UpdateInfo"}}, "title": "UpdateVoiceByIDReq", "required": ["voice_id", "update_info"]}, "UpdateVoiceByIDResp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/definitions/VoiceInfo"}}, "title": "UpdateVoiceByIDResp", "required": ["data"]}, "UserInfoReq": {"type": "object", "properties": {"optional_case": {"type": "string", "description": " 可选参数"}}, "title": "UserInfoReq"}, "UserInfoRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "name": {"type": "string"}}, "title": "UserInfoRsp", "required": ["name"]}, "VoiceCategory": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "cover_url": {"type": "string"}}, "title": "VoiceCategory", "required": ["id", "name", "cover_url"]}, "VoiceInfo": {"type": "object", "properties": {"voice_id": {"type": "integer", "format": "int32"}, "display_name": {"type": "string"}, "sample_url": {"type": "string"}, "record_text": {"type": "string"}, "processed_record_url": {"type": "string"}, "display_enable": {"type": "boolean", "format": "boolean"}, "char_name": {"type": "string"}, "labs_tag": {"type": "array", "items": {"type": "string"}}, "labs_id": {"type": "string"}, "rvc_tag": {"type": "array", "items": {"type": "string"}}, "rvc_id": {"type": "string"}, "rvc_transpose": {"type": "integer", "format": "int32"}, "creator_user_id": {"type": "integer", "format": "int32"}, "creator_user_name": {"type": "string"}, "creation_type": {"type": "integer", "format": "int32"}, "enable": {"type": "boolean", "format": "boolean"}, "create_time": {"type": "integer", "format": "int64"}, "duration": {"type": "integer", "format": "int32"}, "public": {"type": "boolean", "format": "boolean"}, "nsfw": {"type": "boolean", "format": "boolean"}, "liked": {"type": "boolean", "format": "boolean"}, "categories": {"type": "array", "items": {"$ref": "#/definitions/VoiceCategory"}}, "accent": {"$ref": "#/definitions/Accent"}}, "title": "VoiceInfo", "required": ["voice_id", "display_name", "sample_url", "record_text", "processed_record_url", "display_enable", "char_name", "labs_tag", "labs_id", "rvc_tag", "rvc_id", "rvc_transpose", "creator_user_id", "creator_user_name", "creation_type", "enable", "create_time", "duration", "public", "nsfw", "liked", "categories", "accent"]}, "VoicePagination": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/definitions/VoiceInfo"}}}, "title": "VoicePagination", "required": ["items"]}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}}