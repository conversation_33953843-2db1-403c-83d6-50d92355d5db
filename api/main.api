syntax = "v1"

import (
	"common.api"
	"bgm.api"
	"sekai.api"
	"story.api"
	"voice.api"
	"discover.api"
)

type (
	UserInfoReq {
		ID           int    `path:"id"`
		OptionalCase string `form:"optional_case,optional"` // 可选参数
	}
	UserInfoRsp {
		ResponseBase
		Name string `json:"name"`
	}
	HealthReq  {}
	HealthRsp {
		ResponseBase
	}
)

// 用于演示的case
@server (
	prefix:  /v2/user
	group:   user
	jwt:     Auth
	timeout: 3s
)
service main {
	@handler GetUserInfoHandler
	get /:id (UserInfoReq) returns (UserInfoRsp)
}

@server (
	prefix: /v3/common
	group:  common
)
service main {
	@handler GetHealthHandler
	get /health (HealthReq) returns (HealthRsp)
}

