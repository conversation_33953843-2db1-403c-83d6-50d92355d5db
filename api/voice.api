info (
	title:   "Voice API定义"
	desc:    "Voice API定义"
	author:  "ijkbytes"
	email:   "<EMAIL>"
	version: "v3"
)

syntax = "v1"

import "common.api"

// Voice 通用类型定义
type (
	VoiceCategory {
		ID       int    `json:"id"`
		Name     string `json:"name"`
		CoverURL string `json:"cover_url"`
	}
	Accent {
		ID        int    `json:"id"`
		Name      string `json:"name"`
		VoiceName string `json:"voice_name"`
	}
	VoiceInfo {
		VoiceID            int              `json:"voice_id"`
		DisplayName        string           `json:"display_name"`
		SampleURL          string           `json:"sample_url"`
		RecordText         string           `json:"record_text"`
		ProcessedRecordURL string           `json:"processed_record_url"`
		DisplayEnable      bool             `json:"display_enable"`
		CharName           string           `json:"char_name"`
		LabsTag            []string         `json:"labs_tag"`
		LabsID             string           `json:"labs_id"`
		RvcTag             []string         `json:"rvc_tag"`
		RvcID              string           `json:"rvc_id"`
		RvcTranspose       int              `json:"rvc_transpose"`
		CreatorUserID      int              `json:"creator_user_id"`
		CreatorUserName    string           `json:"creator_user_name"`
		CreationType       int32            `json:"creation_type"`
		Enable             bool             `json:"enable"`
		CreateTime         int64            `json:"create_time"`
		Duration           int              `json:"duration"`
		Public             bool             `json:"public"`
		Nsfw               bool             `json:"nsfw"`
		Liked              bool             `json:"liked"`
		Categories         []*VoiceCategory `json:"categories"`
		Accent             Accent           `json:"accent"`
	}
	VoicePagination {
		Pagination
		Items []*VoiceInfo `json:"items"`
	}
)

type (
	GetVoiceByIDReq {
		VoiceID int `form:"voice_id"`
	}
	GetVoiceByIDResp {
		ResponseBase
		Data *VoiceInfo `json:"data"`
	}
)

type (
	CloneVoiceReq {
		RecordURL string `json:"record_url"`
		AccentID  int    `json:"accent_id"`
	}
	CloneVoiceResp {
		ResponseBase
		Data *VoiceInfo `json:"data"`
	}
)

type (
	UpdateInfo {
		DisplayName *string `json:"display_name,optional"`
		Enable      *bool   `json:"enable,optional"`
		Public      *bool   `json:"public,optional"`
		Categories  []int   `json:"categories,optional"`
		AccentID    *int    `json:"accent_id,optional"`
	}
	UpdateVoiceByIDReq {
		VoiceID    int        `json:"voice_id"`
		UpdateInfo UpdateInfo `json:"update_info"`
	}
	UpdateVoiceByIDResp {
		ResponseBase
		Data *VoiceInfo `json:"data"`
	}
)

type (
	GetVoiceCreatedByUserReq {
		UserID int `form:"user_id,optional"`
		Page   int `form:"page,default=1,range=[1:]"`
		Size   int `form:"size,default=10,range=[1:]"`
	}
	GetVoiceCreatedByUserResp {
		ResponseBase
		Data *VoicePagination `json:"data"`
	}
)

type (
	GetQuestionReq  {}
	GetQuestionResp {
		ResponseBase
		Data string `json:"data"`
	}
)

type (
	GetCategoryReq {
		Page int `form:"page,default=1,range=[1:]"`
		Size int `form:"size,default=99,range=[1:]"`
	}
	GetCategoryPagination {
		Pagination
		Items []*VoiceCategory `json:"items"`
	}
	GetCategoryResp {
		ResponseBase
		Data *GetCategoryPagination `json:"data"`
	}
)

type (
	GetByCategoryReq {
		CategoryID int `form:"category_id"`
		Page       int `form:"page,default=1,range=[1:]"`
		Size       int `form:"size,default=10,range=[1:]"`
	}
	GetByCategoryResp {
		ResponseBase
		Data *VoicePagination `json:"data"`
	}
)

type (
	GetTrendingReq {
		Page int `form:"page,default=1,range=[1:]"`
		Size int `form:"size,default=10,range=[1:]"`
	}
	GetTrendingResp {
		ResponseBase
		Data *VoicePagination `json:"data"`
	}
)

type (
	SearchReq {
		Keyword string `form:"keyword"`
		Page    int    `form:"page,default=1,range=[1:]"`
		Size    int    `form:"size,default=10,range=[1:]"`
	}
	SearchResp {
		ResponseBase
		Data *VoicePagination `json:"data"`
	}
)

type (
	GetAccentsReq {
		Page int `form:"page,default=1,range=[1:]"`
		Size int `form:"size,default=10,range=[1:]"`
	}
	GetAccentsPagination {
		Pagination
		Items []*Accent `json:"items"`
	}
	GetAccentsResp {
		ResponseBase
		Data *GetAccentsPagination `json:"data"`
	}
)

type (
	LikeVoiceReq {
		VoiceID int  `json:"voice_id"`
		Like    bool `json:"like"`
	}
	LikeVoiceResp {
		ResponseBase
	}
)

type (
	GetLikedByUserReq {
		UserID int `form:"user_id,optional"`
		Page   int `form:"page,default=1,range=[1:]"`
		Size   int `form:"size,default=10,range=[1:]"`
	}
	GetLikedByUserResp {
		ResponseBase
		Data *VoicePagination `json:"data"`
	}
)

@server (
	prefix: /v3/voice
	group:  voice
	jwt:    Auth
)
service main {
	@doc (
		summary: "获取语音详情"
	)
	@handler GetVoiceByIDHandler
	get /getByID (GetVoiceByIDReq) returns (GetVoiceByIDResp)

	@doc (
		summary: "克隆语音"
	)
	@handler CloneVoiceHandler
	post /clone (CloneVoiceReq) returns (CloneVoiceResp)

	@doc (
		summary: "更新语音"
	)
	@handler UpdateByIDHandler
	put /update (UpdateVoiceByIDReq) returns (UpdateVoiceByIDReq)

	@doc (
		summary: "获取用户创建的语音"
	)
	@handler GetCreatedByUserHandler
	get /getCreatedByUser (GetVoiceCreatedByUserReq) returns (GetVoiceCreatedByUserResp)

	@doc (
		summary: "获取问题"
	)
	@handler GetQuestionHandler
	get /getQuestion (GetQuestionReq) returns (GetQuestionResp)

	@doc (
		summary: "获取分类语音"
	)
	@handler GetByCategoryHandler
	get /getByCategory (GetByCategoryReq) returns (GetByCategoryResp)

	@doc (
		summary: "获取热门语音"
	)
	@handler GetTrendingHandler
	get /getTrending (GetTrendingReq) returns (GetTrendingResp)

	@doc (
		summary: "搜索语音"
	)
	@handler SearchHandler
	get /search (SearchReq) returns (SearchResp)

	@doc (
		summary: "点赞/取消点赞语音，代替原来 likeVoice、unlikeVoice 两个接口"
	)
	@handler LikeVoiceHandler
	post /like (LikeVoiceReq) returns (LikeVoiceResp)

	@doc (
		summary: "获取用户点赞的语音"
	)
	@handler GetLikedByUserHandler
	get /getLikedByUser (GetLikedByUserReq) returns (GetLikedByUserResp)
}

// 带缓存的API
@server (
	prefix:     /v3/voice
	group:      voice
	jwt:        Auth
	middleware: APICacheMiddleware // 使用缓存中间件, 缓存10分钟，仅缓存GET请求
)
service main {
	@doc (
		summary: "获取分类"
	)
	@handler GetCategoryHandler
	get /getCategory (GetCategoryReq) returns (GetCategoryResp)

	@doc (
		summary: "获取口音"
	)
	@handler GetAccentsHandler
	get /getAccents (GetAccentsReq) returns (GetAccentsResp)
}

