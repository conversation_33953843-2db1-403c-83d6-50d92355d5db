#@host = http://develop-api.sekai.chat
@host = http://localhost:7700

@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxODUyLCJ1c2VyX25hbWUiOiJpamtieXRlcyIsImlzX3RvdXJpc3QiOmZhbHNlLCJhdWRpbyI6dHJ1ZSwiaWF0IjoxNzQ2ODY2ODk3LCJleHAiOjE3NDgxNjI4OTd9.4J4ocrSHJ4s-2E6U4FjQfYSgMXHHzqGDdpGPK5dKfZE

### 获取sekai关键词
GET {{host}}/v3/sekai/getKeyWords?prefix=sakura&page=1&size=10
Content-Type: application/json
Authorization: Bearer {{token}}
App-Version: 1.18.0

### 获取热门sekai
GET {{host}}/v3/sekai/getHot?page=2&size=10
Content-Type: application/json
Authorization: Bearer {{token}}
App-Version: 1.20

### 搜索sekai
GET {{host}}/v3/sekai/search?keywords=dasen&page=1&size=10
Content-Type: application/json
Authorization: Bearer {{token}}
App-Version: 1.18.0

