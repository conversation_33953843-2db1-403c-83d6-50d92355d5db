#@host = http://develop-api.sekai.chat
@host = http://localhost:7700
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxODUyLCJ1c2VyX25hbWUiOiJpamtieXRlcyIsImlzX3RvdXJpc3QiOmZhbHNlLCJhdWRpbyI6dHJ1ZSwiaWF0IjoxNzQ2ODY2ODk3LCJleHAiOjE3NDgxNjI4OTd9.4J4ocrSHJ4s-2E6U4FjQfYSgMXHHzqGDdpGPK5dKfZE

### 创建BGM
POST {{host}}/v3/bgm/create
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "test go create bgm",
  "url": "https://stage-data.sekai.chat/aiu-bgm/e96e7a18-78a7-4ad9-ab44-7e45cf272237_0_BGM_normalized.mp3?AWSAccessKeyId=AKIAQE43KJDN7ARTLAVM&Signature=Sismq7Eaa5KQsWWzBsXbCIFoRHM%3D&Expires=1733288809",
  "duration": 135,
  "cover_url": "",
  "tags": [],
  "public": false,
  "categories": [15, 19],
  "premise_ids": []
}

### 删除BGM
POST {{host}}/v3/bgm/delete
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "id": 770
}

### 获取BGM详情
GET {{host}}/v3/bgm/getByID?id=770
Content-Type: application/json
Authorization: Bearer {{token}}

### 获取用户喜欢的BGM
GET {{host}}/v3/bgm/getUserLikedBGM?user_id=2972&page=1&size=10
Content-Type: application/json
Authorization: Bearer {{token}}

### 获取BGM分类
GET {{host}}/v3/bgm/getCategory?page=1&size=10
Content-Type: application/json
Authorization: Bearer {{token}}

### 获取用户创建的BGM
GET {{host}}/v3/bgm/getCreatedByUser?user_id=971&page=1&size=10
Content-Type: application/json
Authorization: Bearer {{token}}

### 更新BGM
POST {{host}}/v3/bgm/update
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "id": 759,
  "name": "更新BGM名称",
  "public": true,
  "tags": ["测试", "更新"],
  "categories": [15, 19]
}

### 获取热门BGM
GET {{host}}/v3/bgm/getTrendingBGM?page=1&size=10
Content-Type: application/json
Authorization: Bearer {{token}}

### 搜索BGM
GET {{host}}/v3/bgm/search?keyword=test&page=1&size=10
Content-Type: application/json
Authorization: Bearer {{token}} 

### 获取分类下的BGM列表
GET {{host}}/v3/bgm/getByCategory?category_id=19&page=1&size=10
Content-Type: application/json
Authorization: Bearer {{token}}